import * as http from '@/shared/services';
export interface PlacesResponse {
  place_id: number;
  licence: string;
  osm_type: string;
  osm_id: number;
  lat: string;
  lon: string;
  category: string;
  type: string;
  place_rank: number;
  importance: number;
  addresstype: string;
  name: string;
  display_name: string;
  boundingbox: string[];
}

export const searchLocationAddress = (searchText: string): Promise<PlacesResponse[] | null> => {
  return http.get(`https://nominatim.openstreetmap.org/search.php?q=${searchText}&format=jsonv2`);
  // return fetch();
};
