<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 175.7 263.68">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-10);
      }

      .cls-3 {
        fill: url(#linear-gradient-4);
      }

      .cls-4 {
        fill: url(#linear-gradient-3);
      }

      .cls-5 {
        fill: url(#linear-gradient-5);
      }

      .cls-6 {
        fill: url(#linear-gradient-8);
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: url(#linear-gradient-9);
      }

      .cls-9 {
        fill: url(#linear-gradient-11);
      }

      .cls-10 {
        fill: url(#linear-gradient-6);
      }

      .cls-11 {
        fill: url(#linear-gradient);
      }

      .cls-12 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="87.81" y1="84.71" x2="87.81" y2="129.81" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".15" stop-color="#0f0f19" stop-opacity=".34"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".83"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="87.8" y1="6.44" x2="87.8" y2="75.47" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".17" stop-color="#0f0f19" stop-opacity=".4"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".85"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="87.81" y1="198.28" x2="87.81" y2="270.12" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".15" stop-color="#0f0f19" stop-opacity=".34"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".83"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="36.29" y1="128.36" x2="36.29" y2="143.48" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".13"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".77"/>
      <stop offset=".88" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="139.32" y1="128.36" x2="139.32" y2="143.48" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".13"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".77"/>
      <stop offset=".88" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="141.62" y1="84.71" x2="141.62" y2="129.81" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".15" stop-color="#0f0f19" stop-opacity=".34"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".83"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="119.83" y1="58.21" x2="132.91" y2="77.6" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".07" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".22" stop-color="#0f0f19" stop-opacity=".4"/>
      <stop offset=".5" stop-color="#0f0f19" stop-opacity=".85"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="155.88" y1="6.44" x2="155.88" y2="75.47" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".17" stop-color="#0f0f19" stop-opacity=".4"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".85"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="-1257.5" y1="58.23" x2="-1244.42" y2="77.62" gradientTransform="translate(-1201.56 270.12) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset=".07" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".22" stop-color="#0f0f19" stop-opacity=".4"/>
      <stop offset=".5" stop-color="#0f0f19" stop-opacity=".85"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="-1221.39" y1="6.44" x2="-1221.39" y2="75.47" gradientTransform="translate(-1201.56 270.12) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".17" stop-color="#0f0f19" stop-opacity=".4"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".85"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="34" y1="84.71" x2="34" y2="129.81" gradientTransform="translate(0 270.12) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".15" stop-color="#0f0f19" stop-opacity=".34"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".83"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
  </defs>
  <path class="cls-11" d="M87.81,185.41c-12.45,0-22.55-10.1-22.55-22.55s10.1-22.55,22.55-22.55,22.55,10.1,22.55,22.55h0c-.01,12.45-10.1,22.54-22.55,22.55ZM87.81,149.18c-7.56,0-13.68,6.12-13.68,13.68,0,7.56,6.12,13.68,13.68,13.68,7.56,0,13.68-6.12,13.68-13.68h0c-.01-7.55-6.13-13.67-13.68-13.68Z"/>
  <path class="cls-1" d="M121.87,263.68h-8.87v-40.51c-.01-10.85-8.8-19.64-19.65-19.65h-11.09c-10.85,0-19.65,8.8-19.66,19.65v40.51h-8.87v-40.51c.02-15.75,12.78-28.5,28.53-28.52h11.11c15.74.03,28.48,12.78,28.5,28.52v40.51Z"/>
  <path class="cls-4" d="M87.81,71.84c-19.84,0-35.92-16.08-35.92-35.92S67.97,0,87.81,0s35.92,16.08,35.92,35.92h0c-.04,19.82-16.1,35.88-35.92,35.92ZM87.81,8.84c-14.91.03-26.98,12.14-26.95,27.05.03,14.91,12.14,26.98,27.05,26.95,14.89-.03,26.95-12.11,26.95-27-.07-14.9-12.15-26.94-27.05-26.96v-.04Z"/>
  <rect class="cls-3" x="31.86" y="126.64" width="8.87" height="15.12"/>
  <path class="cls-12" d="M143.76,126.64h-8.89c-.02-20.66-16.76-37.4-37.42-37.43h-19.3c-20.66.03-37.4,16.77-37.42,37.43h-8.86c.03-25.56,20.73-46.27,46.29-46.3h19.31c25.56.03,46.27,20.74,46.29,46.3Z"/>
  <rect class="cls-5" x="134.89" y="126.64" width="8.87" height="15.12"/>
  <path class="cls-10" d="M141.62,185.41c-12.45,0-22.55-10.1-22.55-22.55s10.1-22.55,22.55-22.55,22.55,10.1,22.55,22.55c-.01,12.45-10.1,22.54-22.55,22.55ZM141.62,149.18c-7.56,0-13.68,6.12-13.68,13.68,0,7.56,6.12,13.68,13.68,13.68,7.56,0,13.68-6.12,13.68-13.68h0c-.01-7.55-6.13-13.67-13.68-13.68Z"/>
  <path class="cls-7" d="M119.92,211.97l-7.28-5.07c5.33-7.67,14.09-12.25,23.43-12.25v8.87c-6.44,0-12.47,3.16-16.15,8.45Z"/>
  <path class="cls-6" d="M175.7,263.68h-8.83v-40.51c-.01-10.85-8.8-19.64-19.65-19.65h-11.15v-8.87h11.11c15.74.02,28.5,12.78,28.52,28.52v40.51Z"/>
  <path class="cls-8" d="M55.87,211.97c-3.68-5.29-9.71-8.45-16.15-8.45v-8.87c9.34,0,18.1,4.58,23.43,12.25l-7.28,5.07Z"/>
  <path class="cls-2" d="M8.87,263.68H0v-40.51c.02-15.74,12.78-28.5,28.52-28.52h11.15v8.87h-11.11c-10.86-.01-19.68,8.79-19.69,19.65h0v40.51Z"/>
  <path class="cls-9" d="M34,185.41c-12.45,0-22.55-10.1-22.55-22.55,0-12.45,10.1-22.55,22.55-22.55s22.55,10.1,22.55,22.55h0c-.01,12.45-10.1,22.54-22.55,22.55ZM34,149.18c-7.56,0-13.68,6.12-13.68,13.68,0,7.56,6.12,13.68,13.68,13.68,7.56,0,13.68-6.12,13.68-13.68h0c-.01-7.55-6.13-13.67-13.68-13.68Z"/>
</svg>