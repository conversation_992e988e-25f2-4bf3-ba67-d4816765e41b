import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';
import Iconify from '@/components/iconify/iconify';
import { useOutsideClick } from '@/hooks/use-outside-click';
import { useTranslate } from '@/locales/use-locales';
import { bgBlur } from '@/theme/css';
import { Box, InputAdornment, Paper, Stack, TextField, Typography } from '@mui/material';
import { useTreeViewApiRef } from '@mui/x-tree-view/hooks';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import { isEmpty, isObject, toNumber } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import React, { useEffect, useState } from 'react';
import SearchNotFound from '../search-not-found';

type Props = {
  data: TreeViewDataType[];
  onNodeSelection: (node: TreeViewDataType[]) => void;
  isError?: boolean;
  defaultValue?: string[] | TreeViewDataType[] | [];
  enableRestriction?: boolean;
  allowedSelection?: string;
  placeholder?: string;
  size?: 'small' | 'medium';
};

const BusinessEntityTreeViewMulti = ({
  data: buData,
  isError,
  onNodeSelection,
  defaultValue,
  enableRestriction = false,
  allowedSelection,
  placeholder,
  size = 'medium',
}: Props) => {
  const { t } = useTranslate();
  const apiRef = useTreeViewApiRef();

  const data = buData.reduce<TreeViewDataType[]>((acc, treeData) => {
    if (toNumber(treeData.id) === -1) {
      return [...acc, ...treeData.children];
    }
    return [...acc, treeData];
  }, []);

  const [selectedNode, setSelectedNode] = useState<TreeViewDataType[]>([]);

  const [searchTerm, setSearchTerm] = useState('');
  const [openTreeview, setOpenTreeview] = useState(false);
  const filteredData: TreeViewDataType[] = searchNodeItem(searchTerm);
  const [expandedItems, setExpandedItems] = useState<string[]>([data[0]?.id]);

  useEffect(() => {
    if (defaultValue?.length) {
      let transformedData = getTransformedBuList();
      const allDefaultBUs = defaultValue.map((buDetail: TreeViewDataType | string | number) => {
        const buId = isObject(buDetail) && buDetail?.id ? buDetail.id : toNumber(buDetail);
        return findNodeById(transformedData, buId.toString());
      });

      setSelectedNode((allDefaultBUs.length ? allDefaultBUs : []) as TreeViewDataType[]);
    } else {
      setSelectedNode([]);
    }
  }, []);

  const getAllItemsWithChildrenItemIds = () => {
    const itemIds: string[] = [];
    const registerItemId = (item: TreeViewDataType) => {
      if (item.children?.length) {
        itemIds.push(item.id);
        item.children.forEach(registerItemId);
      }
    };
    filteredData.forEach(registerItemId);
    return itemIds;
  };

  const getTransformedBuList = () => {
    let transformedData =
      filteredData.length < 2
        ? filteredData[filteredData.length - 1]
        : {
            children: filteredData,
            id: '-1',
            code: '',
            shortName: '',
            fullName: '',
            parentId: '',
            entityType: '',
            isNode: true,
            active: true,
            countryId: 0,
            mapCenter: '',
            utcTimeDiff: '',
            other_info: null,
          };

    return transformedData;
  };

  const handleItemSelectionToggle = (_event: React.SyntheticEvent, itemId: string, isSelected: boolean) => {
    if (isSelected) {
      let transformedData = getTransformedBuList();
      const selectedNodeItem = findNodeById(transformedData as TreeViewDataType, itemId);

      console.log(selectedNodeItem);

      if (enableRestriction && !selectedNodeItem?.showCheckbox) {
        enqueueSnackbar(
          `${selectedNodeItem?.entityType} selection is not allowed, only ${allowedSelection?.toLowerCase()}s are allowed for selection.`,
          { variant: 'warning', autoHideDuration: 3000 },
        );
        return;
      }

      setOpenTreeview(true);
      if (selectedNodeItem) {
        setSelectedNode((prevSelectedNode: TreeViewDataType[]) => {
          return [...prevSelectedNode, selectedNodeItem];
        });
        onNodeSelection([...selectedNode, selectedNodeItem]);
      }
    } else {
      setOpenTreeview(true);

      setSelectedNode((prevSelectedNode: TreeViewDataType[]) => {
        const filteredData = prevSelectedNode.filter((prevSelectedNode: TreeViewDataType) => {
          return toNumber(prevSelectedNode.id) !== toNumber(itemId);
        });
        return filteredData;
      });
      const filteredData = selectedNode.filter((prevSelectedNode: TreeViewDataType) => {
        return toNumber(prevSelectedNode.id) !== toNumber(itemId);
      });
      onNodeSelection(filteredData);
    }
  };

  const handleExpandedItemsChange = (_event: React.SyntheticEvent, itemIds: string[]) => {
    setExpandedItems(itemIds);
  };

  /**
   * Depth first search.
   * @param node
   * @param term
   * @param foundIds
   * @returns
   */
  function dfs(node: TreeViewDataType, term: string, foundIds: string[]) {
    // Implement your search functionality
    let isMatching = node.fullName && node.fullName.toLowerCase().indexOf(term.toLowerCase()) > -1;

    if (Array.isArray(node.children)) {
      node.children.forEach((child: TreeViewDataType) => {
        const hasMatchingChild = dfs(child, term, foundIds);
        isMatching = isMatching || hasMatchingChild;
      });
    }
    // We will add any item if it matches our search term or if it has a children that matches our term
    if (isMatching && node.id) {
      foundIds.push(node.id);
    }
    return isMatching;
  }

  /**
   * Return all those tree nodes which matche with matchedIDS
   * @param data
   * @param matchedIDS
   * @returns
   */
  function filter(data: TreeViewDataType[], matchedIDS: string[]): TreeViewDataType[] {
    return data
      .filter((item) => matchedIDS.indexOf(item.id) > -1)
      .map((item) => ({
        ...item,
        children: item.children ? filter(item.children, matchedIDS) : [],
      }));
  }

  /**
   * Search the tree leaf nodes that matches with searh term
   * @param term
   * @returns
   */
  function searchNodeItem(term: string): TreeViewDataType[] {
    // Wrap data in an object to match the node shape
    const dataNode = {
      children: data,
    } as TreeViewDataType;

    const matchedIDS: string[] = [];
    // find all items IDs that matches our search (or their children does)
    dfs(dataNode, term, matchedIDS);

    // filter the original data so that only matching items (and their fathers if they have) are returned
    return filter(data, matchedIDS);
  }

  /**
   * Find the node with given id.
   * @param root
   * @param id
   * @returns
   */
  function findNodeById(root: TreeViewDataType | undefined, id: string): TreeViewDataType | undefined {
    if (!root) return undefined; // If the root is undefined, return undefined

    // Check if the root node matches the ID
    if (root.id === id) {
      return root;
    }

    // If the root has children, recursively search in each child
    if (root.children && root.children.length > 0) {
      for (const child of root.children) {
        const foundNode = findNodeById(child, id);
        if (foundNode) {
          return foundNode;
        }
      }
    }
    // If the node is not found in the current root or its children, return undefined
    return undefined;
  }

  const onSearchTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setExpandedItems(event.target.value ? getAllItemsWithChildrenItemIds() : [data[0]?.id]);
  };

  const ref = useOutsideClick(() => {
    setOpenTreeview(false);
  });

  return (
    <>
      {allowedSelection && (
        <Typography
          variant="caption"
          sx={{
            ml: 1,
            mb: 0.5,
            display: 'block',
            color: 'text.disabled',
          }}
        >
          Only {allowedSelection.toLowerCase()}s are allowed for selection.
        </Typography>
      )}
      <Box ref={ref}>
        <Stack spacing={1} flexDirection="row" justifyContent="space-between" position="relative">
          <TextField
            placeholder={`${isEmpty(placeholder) ? t('placeholder.select_business_entity') : placeholder}`}
            multiline
            variant="outlined"
            error={isError}
            value={
              !selectedNode?.length
                ? ''
                : selectedNode
                    .map((selectedNodeDetail, index) => {
                      if (index < 2) {
                        return selectedNodeDetail?.fullName || '';
                      } else {
                        return ''; // Return null for elements after the second one
                      }
                    })
                    .filter((value) => value !== '') // Filter out null values
                    .join(', ')
              // .concat(selectedNode.length > 2 ? ` +${selectedNode.length - 2}` : '') // Concatenate count if more than two
            }
            sx={{
              borderRadius: 1,
              input: { cursor: 'pointer', height: 'auto' }, // Ensure consistent height
            }}
            fullWidth
            onClick={() => setOpenTreeview(!openTreeview)}
            size={size}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Iconify
                    icon={!openTreeview ? 'eva:arrow-ios-downward-fill' : 'eva:arrow-ios-upward-fill'}
                    sx={{ color: 'text.disabled' }}
                  />
                  {selectedNode.length > 2 && (
                    <Typography
                      variant="subtitle2"
                      sx={{
                        marginLeft: 1,
                        backgroundColor: (theme) => `${theme.palette.primary}`, // Highlighted background color
                        padding: '2px 5px', // Padding for better appearance
                        borderRadius: '4px', // Rounded corners
                      }}
                    >
                      +{selectedNode.length - 2} more {/* Label for the count */}
                    </Typography>
                  )}
                </InputAdornment>
              ),
            }}
          />
        </Stack>
        <Box sx={{ position: 'relative', width: '100%' }}>
          <Paper
            elevation={3}
            sx={{
              position: 'absolute',
              zIndex: 9,
              width: '100%',
              top: '0px',
              bgcolor: 'unset',
              ...bgBlur({
                blur: 20,
                color: '#FFFFFF',
              }),
              transition: 'opacity 0.3s ease-in-out, transform 0.3s ease-in-out',
              transform: openTreeview ? 'scaleY(1)' : 'scaleY(0)',
              opacity: openTreeview ? 1 : 0,
              transformOrigin: 'top',
              backgroundImage: `url(assets/cyan-blur.png), url(assets/red-blur.png)`,
              backgroundRepeat: 'no-repeat, no-repeat',
              backgroundPosition: 'top right, left bottom',
              backgroundSize: '50%, 50%',
              boxShadow: (theme) => theme.customShadows.dropdown,
            }}
          >
            <Stack spacing={1}>
              {openTreeview && (
                <Box sx={{ p: 1, overflowY: 'auto', maxHeight: '600px' }}>
                  <TextField
                    placeholder={t('placeholder.filter')}
                    fullWidth
                    sx={{ mb: 1 }}
                    value={searchTerm}
                    onChange={onSearchTextChange}
                    size="small"
                  />
                  {filteredData.length ? (
                    <RichTreeView
                      multiSelect
                      checkboxSelection
                      // checkboxSelection={(item: TreeViewDataType) => !!item.showCheckbox}
                      // isItemDisabled={(item) => !!item.disabled}
                      apiRef={apiRef}
                      items={filteredData}
                      selectedItems={selectedNode.map((selectedNodeDetail) => {
                        console.log('selectedItems');
                        console.log(selectedNodeDetail);
                        return selectedNodeDetail.id;
                      })}
                      onItemSelectionToggle={handleItemSelectionToggle}
                      onExpandedItemsChange={handleExpandedItemsChange}
                      defaultExpandedItems={['fullName']}
                      getItemLabel={(item) => item.fullName}
                      expandedItems={expandedItems}
                      sx={{ padding: (theme) => theme.spacing(0.5) }}
                    />
                  ) : (
                    <SearchNotFound query={searchTerm} sx={{ px: 2, pt: 4, pb: 4 }} />
                  )}
                </Box>
              )}
            </Stack>
          </Paper>
        </Box>
      </Box>
    </>
  );
};

export default BusinessEntityTreeViewMulti;
