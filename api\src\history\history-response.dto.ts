import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";

export class HistoryResponseDto {
    @ApiProperty()
    @Expose({ name: 'action_performed' })
    public action: string;

    @ApiProperty()
    @Expose({ name: 'created_by' })
    public actionBy: string;

    @ApiProperty()
    @Expose({ name: 'action_date' })
    public actionDate: Date;

    @ApiProperty()
    @Expose({ name: 'action_comments' })
    public comment: string;
}