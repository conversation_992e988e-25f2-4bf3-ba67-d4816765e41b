import React from 'react';
import { ListItem, ListItemText, Typography, Box, Chip, Stack } from '@mui/material';
import { getIcon } from '@/shared/utils/get-icon';
import { CAPABILITY_TYPE_ENUM_DISPLAY } from '@/shared/enum/capability.enum';
import { CapabilitySearchOption } from '../models/searchModel';

interface CustomDropdownCapabilityProps {
  option: CapabilitySearchOption;
  props: any;
  showDescription?: boolean;
  showChips?: boolean;
  getIconPath: (code: string) => string;
  iconSize?: number;
  listItemStyle?: object;
  chipLabelStyle?: object;
  statusColors?: Record<string, string>;
}

const CustomDropdownCapability: React.FC<CustomDropdownCapabilityProps> = ({
  option,
  props,
  // showDescription = true,
  showChips = true,
  iconSize = 24,
  listItemStyle = {},
  chipLabelStyle = {},
  statusColors,
}) => {
  const { key, ...restOfProps } = props;
  return (
    <ListItem {...restOfProps} sx={{ padding: 1, ...listItemStyle }}>
      <ListItemText
        primary={<Typography sx={{ fontWeight: 'bold' }}>{option?.capability}</Typography>}
        secondary={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {option?.coreSolution?.code && (
              <img
                src={getIcon(option?.coreSolution.code)}
                alt="icon"
                style={{ width: iconSize, height: iconSize, marginRight: 8 }}
              />
            )}
            <Box>
              <Stack direction={'row'} spacing={2}>
                <Typography variant="body2">
                  {[option?.product, option?.category?.title, option?.subCategory].filter(Boolean).join(' - ')}
                </Typography>
              </Stack>
              <Typography variant="body2" color="textSecondary">
                {CAPABILITY_TYPE_ENUM_DISPLAY[option?.capabilityType]}
              </Typography>
            </Box>
          </Box>
        }
      />
      {showChips && (
        <Chip
          label={option?.status}
          sx={{
            backgroundColor: statusColors && option.status ? statusColors[option?.status] : 'gray',
            color: 'white',
            fontWeight: 'bold',
            ...chipLabelStyle,
          }}
        />
      )}
    </ListItem>
  );
};

export default CustomDropdownCapability;
