import { AppConfig } from '../config';

export const ROOTS = {
  HOME: `${AppConfig.uiBaseUrl}`,
};

export const paths = {
  root: `${AppConfig.uiBaseUrl}`,
  applicationRoot: () => {
    return `${AppConfig.uiBaseUrl}`;
  },
  home: {
    root: ROOTS.HOME,
    app: `${ROOTS.HOME}/home`,
  },
  error: {
    page403: `${AppConfig.uiBaseUrl}403`,
    page404: `${AppConfig.uiBaseUrl}404`,
    page500: `${AppConfig.uiBaseUrl}500`,
  },
};

export const ReplaceUrlVariable = (url: string, variables: any) => {
  Object.keys(variables).forEach((variableKey: string) => {
    var re = new RegExp(':' + variableKey, 'g');
    url = url.replace(re, variables[variableKey]);
  });
  return url;
};
