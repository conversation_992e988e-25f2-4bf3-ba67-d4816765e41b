import { FC } from 'react';
import { Box, Button, Typography } from '@mui/material';

interface CustomAddButtonProps {
  text: string;
  sx?: Object;
  onClick?: () => void; // Callback function when card is clicked
}
const defaultButtonStyle = {
  borderRadius: 2,
  border: '1px solid grey',
  backgroundColor: '#ebeef1',
  color: '#3b3a88',
  fontSize: '12px',
  padding: '4px 12px',
  display: 'flex',
  alignItems: 'center',
  gap: 0.5,
};
const CustomAddButton: FC<CustomAddButtonProps> = ({ text, onClick, sx }) => {
  return (
    <Button sx={{ ...defaultButtonStyle, ...sx }} onClick={onClick}>
      <Box
        component="img"
        src="assets/media/images/Add.png"
        alt="Add Icon"
        sx={{
          width: 12,
          height: 12,
        }}
      />
      <Typography variant="body2" sx={{ fontSize: '12px' }}>
        {text}
      </Typography>
    </Button>
  );
};

export default CustomAddButton;
