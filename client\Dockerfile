### STAGE 1:BUILD ###
# Defining a node image to be used as giving it an alias of "build"
# Which version of Node image to use depends on project dependencies 
# This is needed to build and compile our code 
# while generating the docker image
#FROM node:22-alpine AS build
FROM node:22.17.0-alpine3.22  As build
# Create a Virtual directory inside the docker image
WORKDIR /dist/src/app
# Run command in Virtual directory
RUN npm cache clean --force
# Copy files from local machine to virtual directory in docker image
# RUN apk add --no-cache python3 make g++
# Copy files to virtual directory
# COPY package.json package-lock.json ./
COPY . .
RUN npm install --legacy-peer-deps
ENV NODE_ENV prod
RUN npm run build

### STAGE 2:RUN ###
# Defining nginx image to be used
FROM nginx:latest AS ngi
# RUN apt-get update && \
#     apt-get install -y --only-upgrade \
#       libpam0g libpam-modules libpam-runtime libpam-modules-bin \
#       libxslt1.1 && \
#     apt-get clean && rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y libicu72 libxml2

# Copying compiled code and nginx config to different folder
# NOTE: This path may change according to your project's output folder 
COPY --from=build /dist/src/app/dist /usr/share/nginx/html
COPY /nginx.conf  /etc/nginx/conf.d/default.conf
# Exposing a port, here it means that inside the container 
# the app will be using Port 4200 while running
EXPOSE 4200