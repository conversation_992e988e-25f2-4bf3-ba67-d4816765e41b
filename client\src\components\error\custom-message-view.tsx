import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
// import { SeverErrorIllustration } from '@/assets/illustrations';
import { varBounce, MotionContainer } from '@/components/animate';
import { FC, FormEvent } from 'react';
import SeverErrorIllustration from '@/assets/illustrations/sever-error-illustration';
import ForbiddenErrorIllustration from '@/assets/illustrations/forbidden-illustration';
import { Button } from '@mui/material';
import { useRedirect } from '@/core/hooks';
import { useLocation } from 'react-router';
import { UnauthorisedIllustration } from '@/assets/illustrations';

// ----------------------------------------------------------------------

type CustomProps = {
  showImage?: boolean;
  heading?: string;
  message: string;
  showButton?: boolean;
  permissionError?: boolean;
};

const CustomMessageView: FC<CustomProps> = ({
  permissionError = false,
  showImage = false,
  heading,
  message,
  showButton = false,
}) => {
  const navigate = useRedirect();
  const location = useLocation();

  const handleClick = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Prevent default form submission behavior
    navigate(location.pathname);
    return true;
  };

  return (
    <MotionContainer
      sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}
    >
      {heading && (
        <m.div variants={varBounce().in}>
          <Typography variant="h3" sx={{ mb: 2 }}>
            {heading}
          </Typography>
        </m.div>
      )}

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary' }}>{message}</Typography>
      </m.div>

      {showImage && !permissionError && (
        <m.div variants={varBounce().in}>
          <SeverErrorIllustration
            sx={{
              height: 260,
              my: { xs: 5, sm: 10 },
            }}
          />
        </m.div>
      )}

      {showImage && permissionError && (
        <m.div variants={varBounce().in}>
          <UnauthorisedIllustration
            sx={{
              height: 260,
              my: { xs: 5, sm: 10 },
            }}
          />
        </m.div>
      )}
      {showButton && (
        <form onSubmit={handleClick}>
          <Button type="submit" size="large" variant="contained">
            Try Again
          </Button>
        </form>
      )}
    </MotionContainer>
  );
};

export default CustomMessageView;
