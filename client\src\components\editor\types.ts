import { Theme, SxProps } from '@mui/material/styles';
import { DeltaStatic, EmitterSource, QuillOptionsStatic, RangeStatic } from 'react-quill-new';

type Value = string | DeltaStatic;
type Range = RangeStatic | null;
interface QuillOptions extends QuillOptionsStatic {
  tabIndex?: number;
}
interface ReactQuillProps {
  bounds?: string | HTMLElement;
  children?: React.ReactElement<any>;
  className?: string;
  defaultValue?: Value;
  formats?: string[];
  id?: string;
  modules?: QuillOptions['modules'];
  onChange?(value: string, delta: DeltaStatic, source: EmitterSource, editor: any): void;
  onChangeSelection?(selection: Range, source: EmitterSource, editor: any): void;
  onFocus?(selection: Range, source: EmitterSource, editor: any): void;
  onBlur?(previousSelection: Range, source: EmitterSource, editor: any): void;
  onKeyDown?: React.EventHandler<any>;
  onKeyPress?: React.EventHandler<any>;
  onKeyUp?: React.EventHandler<any>;
  placeholder?: string;
  preserveWhitespace?: boolean;
  readOnly?: boolean;
  style?: React.CSSProperties;
  tabIndex?: number;
  theme?: string;
  value?: Value;
}
// ----------------------------------------------------------------------

export interface EditorProps extends ReactQuillProps {
  error?: boolean;
  simple?: boolean;
  helperText?: React.ReactNode;
  sx?: SxProps<Theme>;
}
