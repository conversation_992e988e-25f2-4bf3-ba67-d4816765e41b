#!/bin/bash

# Check if module name is provided
if [ -z "$1" ]; then
  echo "Please provide a module name"
  echo "Usage: ./create-module.sh module-name"
  exit 1
fi

# Convert module name to lowercase
MODULE_NAME=$(echo "$1" | tr '[:upper:]' '[:lower:]')

# Create module using NestJS CLI
echo "Creating module: $MODULE_NAME"
npx nest g module $MODULE_NAME
npx nest g controller $MODULE_NAME
npx nest g service $MODULE_NAME

# Create directory structure
echo "Creating directory structure..."
mkdir -p src/$MODULE_NAME/repositories
mkdir -p src/$MODULE_NAME/models
mkdir -p src/$MODULE_NAME/dtos
mkdir -p src/$MODULE_NAME/constants
mkdir -p src/$MODULE_NAME/interfaces
mkdir -p src/$MODULE_NAME/types

# Create index files
echo "Creating index files..."
cat > src/$MODULE_NAME/repositories/index.ts << EOF
// Export all repositories
EOF

cat > src/$MODULE_NAME/models/index.ts << EOF
// Export all models
EOF

cat > src/$MODULE_NAME/dtos/index.ts << EOF
// Export all DTOs
EOF

cat > src/$MODULE_NAME/constants/index.ts << EOF
// Export all constants
EOF

cat > src/$MODULE_NAME/interfaces/index.ts << EOF
// Export all interfaces
EOF

cat > src/$MODULE_NAME/types/index.ts << EOF
// Export all types
EOF

# Create repository template
cat > src/$MODULE_NAME/repositories/${MODULE_NAME}.repository.ts << EOF
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { BaseRepository } from 'src/shared/repositories';
import { ${MODULE_NAME^} } from '../models/${MODULE_NAME}.model';

@Injectable()
export class ${MODULE_NAME^}Repository extends BaseRepository<${MODULE_NAME^}> {
  constructor(
    @InjectModel(${MODULE_NAME^})
    private readonly ${MODULE_NAME}Model: typeof ${MODULE_NAME^},
  ) {
    super(${MODULE_NAME}Model);
  }
}
EOF

# Create model template
cat > src/$MODULE_NAME/models/${MODULE_NAME}.model.ts << EOF
import { Column, DataType, Table } from 'sequelize-typescript';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: '${MODULE_NAME}s' })
export class ${MODULE_NAME^} extends BaseModel<${MODULE_NAME^}> {
  @Column({ field: 'name', type: DataType.STRING, allowNull: false })
  public name: string;
}
EOF

# Update repositories index
echo "export * from './${MODULE_NAME}.repository';" >> src/$MODULE_NAME/repositories/index.ts

# Update models index
echo "export * from './${MODULE_NAME}.model';" >> src/$MODULE_NAME/models/index.ts

# Update module file to include repositories and models
MODULE_FILE="src/$MODULE_NAME/$MODULE_NAME.module.ts"
sed -i "s/imports: \[\],/imports: \[SequelizeModule.forFeature([${MODULE_NAME^}])\],/" $MODULE_FILE
sed -i "s/providers: \[${MODULE_NAME^}Service\],/providers: \[${MODULE_NAME^}Service, ${MODULE_NAME^}Repository\],/" $MODULE_FILE
sed -i "s/exports: \[${MODULE_NAME^}Service\],/exports: \[${MODULE_NAME^}Service, ${MODULE_NAME^}Repository\],/" $MODULE_FILE

# Add necessary imports to module file
sed -i "1s/^/import { SequelizeModule } from '@nestjs\/sequelize';\nimport { ${MODULE_NAME^}Repository } from '.\/repositories';\nimport { ${MODULE_NAME^} } from '.\/models';\n/" $MODULE_FILE

echo "Module $MODULE_NAME created successfully!"