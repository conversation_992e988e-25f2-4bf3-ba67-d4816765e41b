import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE_MB } from '@/shared/constants/file.constants';
import { FormFields } from '@/shared/models';
import { convertFileToBase64 } from '@/shared/utils/convert-file-to-base64';
import { isEqual } from 'lodash';
import { enqueueSnackbar } from 'notistack';

const handleDrop = async (
  fieldName: FormFields,
  acceptedFiles: File[] | undefined,
  methods: any,
  ...restParam: any
) => {
  if (!acceptedFiles || acceptedFiles.length === 0) return;

  const [t] = restParam;

  // Separate validation for size & format
  const oversizedFiles = acceptedFiles.filter((file) => file.size / 1024 / 1024 > MAX_FILE_SIZE_MB);
  const invalidFormatFiles = acceptedFiles.filter((file) => !ALLOWED_FILE_TYPES.includes(file.type));

  // Show individual snackbars for errors
  if (oversizedFiles.length > 0) {
    oversizedFiles.forEach((file) => {
      enqueueSnackbar(`${file.name} ${t('messages.file_size_error')}`, { variant: 'error' });
    });
  }

  if (invalidFormatFiles.length > 0) {
    invalidFormatFiles.forEach((file) => {
      enqueueSnackbar(`${file.name} ${t('messages.file_format_error')}`, { variant: 'error' });
    });
  }

  // Filter only valid files
  const filteredFiles = acceptedFiles.filter(
    (file) => !oversizedFiles.includes(file) && !invalidFormatFiles.includes(file),
  );

  if (filteredFiles.length === 0) return; // Stop if no valid files remain

  const currentFiles = methods.watch(fieldName);
  const existingFiles = Array.isArray(currentFiles) ? currentFiles : [];

  // Transform valid files
  const transformedFiles = await Promise.all(
    filteredFiles.map(async (file) => {
      let result: any = {
        id: '',
        attachment_name: file.name,
        attachment_content_type: file.type,
        size: file.size,
        file,
        isdeleted: false,
        lastModified: file.lastModified,
        path: (file as any)?.path ?? '',
        preview: URL.createObjectURL(file),
        isNew: true,
      };
      try {
        let file_base64 = await convertFileToBase64(file);
        result['file_base64'] = file_base64;
      } catch (err) {
        console.log(`Could not process dropped files ${file.name}`);
        // enqueueSnackbar(`Could not process dropped files ${file.name}`, { variant: 'error' });
      } finally {
        return result;
      }
    }),
  );

  methods.setValue(fieldName, [...existingFiles, ...transformedFiles]); // ✅ Append only valid files
};

const handleRemoveFile = (fieldName: FormFields, file: any, methods: any) => {
  if (!file) return;
  const currentFiles = methods.watch(fieldName);
  if (!Array.isArray(currentFiles)) return;
  const filteredFiles = currentFiles.filter((f) => !isEqual(file, f));

  const deletedFiles = methods.watch('deletedFiles') || [];

  methods.setValue('deletedFiles', [...deletedFiles, file?.file_id]);
  methods.setValue(fieldName, filteredFiles as any);
};

const handleRemoveAllFiles = (fieldName: FormFields, ...methods: any) => {
  methods.setValue(fieldName, [] as any);
};

export { handleDrop, handleRemoveFile, handleRemoveAllFiles };
