<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 213.1 230.7">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: url(#linear-gradient-8);
      }

      .cls-6 {
        fill: url(#linear-gradient-7);
      }

      .cls-7 {
        fill: url(#linear-gradient-9);
      }

      .cls-8 {
        fill: url(#linear-gradient-6);
      }

      .cls-9 {
        fill: url(#linear-gradient);
      }

      .cls-10 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="174.2" y1="-335.44" x2="22.8" y2="-335.44" gradientTransform="translate(0 -109.19) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".81"/>
      <stop offset=".86" stop-color="#0f0f19" stop-opacity=".24"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="27.1" y1="-175.49" x2="27.1" y2="-262.89" gradientTransform="translate(0 -109.19) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".51" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="40.1" y1="-167.89" x2="-.04" y2="-167.89" gradientTransform="translate(0 -109.19) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="18.55" y1="-131.32" x2="18.55" y2="-160.52" gradientTransform="translate(0 -109.19) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".51" stop-color="#0f0f19" stop-opacity=".87"/>
      <stop offset=".8" stop-color="#0f0f19" stop-opacity=".47"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="27.1" y1="-277.99" x2="27.1" y2="-330.89" gradientTransform="translate(0 -109.19) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="40.1" y1="-270.44" x2="-.04" y2="-270.44" gradientTransform="translate(0 -109.19) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="108.31" y1="105.51" x2="125.19" y2="105.51" gradientTransform="translate(0 232.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".29" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="104.5" y1="141.2" x2="123.37" y2="141.2" gradientTransform="translate(0 232.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".29" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="94.26" y1="170.53" x2="137.23" y2="64.17" gradientTransform="translate(0 232.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".89" stop-color="#000" stop-opacity=".1"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <rect class="cls-9" x="22.8" y="221.8" width="151.4" height="8.9"/>
  <path class="cls-1" d="M23,153.7h-8.9v-81.2h10.9c3.4,0,6.2-2.8,6.2-6.2h8.9c0,8.3-6.8,15.1-15.1,15.1h-2v72.3h0Z"/>
  <path class="cls-3" d="M31.2,66.3c0-3.5-2.8-6.3-6.2-6.3H0v-8.9h25c8.3,0,15.1,6.8,15.1,15.1l-8.9.1h0Z"/>
  <rect class="cls-2" x="14.1" y="22.1" width="8.9" height="29.2"/>
  <path class="cls-10" d="M213.1,193.5h-8.9V25.7c0-4.5-1.8-8.7-5-11.9s-7.4-4.9-11.9-4.9H23v13.3h-8.9V0h173.2c6.9,0,13.4,2.7,18.2,7.5,4.9,4.9,7.5,11.3,7.6,18.2v167.8h0Z"/>
  <path class="cls-10" d="M187.3,230.6h-13.1v-8.9h13.1c9.3,0,16.9-7.6,16.9-16.9v-11.4h8.9v11.4c-.1,14.3-11.6,25.8-25.8,25.8Z"/>
  <path class="cls-4" d="M23,221.7h-8.9v-46.7h10.9c3.4,0,6.2-2.8,6.2-6.2h8.9c0,8.3-6.8,15.1-15.1,15.1h-2v37.8h0Z"/>
  <path class="cls-8" d="M40.1,168.8h-8.9c0-3.4-2.8-6.2-6.2-6.2H0v-8.9h25c8.3,0,15.1,6.7,15.1,15.1Z"/>
  <g>
    <g>
      <path class="cls-6" d="M116.62,148.63c-.16,0-.31,0-.46-.01-2.28-.12-4.23-1.04-5.62-2.64-1.59-1.82-2.29-4.35-2.04-7.32l1.98-24.04h-2.16v-8.87h11.79l-2.77,33.65c0,.11-.01.2-.02.29,1.01-.15,2.65-.58,3.84-1.18l4.03,7.9c-2.55,1.3-6.13,2.22-8.57,2.22h0Z"/>
      <path class="cls-5" d="M113.93,100.93c-5.2,0-9.43-4.23-9.43-9.43s4.23-9.43,9.43-9.43,9.43,4.23,9.43,9.43-4.23,9.43-9.43,9.43ZM113.93,90.94c-.31,0-.56.25-.56.56s.25.56.56.56.56-.25.56-.56-.25-.56-.56-.56Z"/>
    </g>
    <path class="cls-7" d="M115.74,172.71c-31.63,0-57.36-25.73-57.36-57.36s25.73-57.36,57.36-57.36,57.36,25.73,57.36,57.36-25.73,57.36-57.36,57.36ZM115.74,66.86c-26.74,0-48.49,21.75-48.49,48.49s21.75,48.49,48.49,48.49,48.49-21.75,48.49-48.49-21.75-48.49-48.49-48.49Z"/>
  </g>
</svg>