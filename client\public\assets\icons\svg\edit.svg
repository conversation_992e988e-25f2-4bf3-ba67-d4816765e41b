<?xml version="1.0" encoding="UTF-8"?>
<svg id="a" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 140.88 149.8">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: none;
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }
    </style>
    <linearGradient id="linear-gradient" x1="-22.47" y1="29.04" x2="6.21" y2="57.72" gradientTransform="translate(0 156.37) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#0f0f19"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="86.68" y1="4.23" x2="42.98" y2="190.92" gradientUnits="userSpaceOnUse">
      <stop offset=".21" stop-color="#0f0f19"/>
      <stop offset=".52" stop-color="#0f0f19" stop-opacity=".53"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-2" d="M38.52,137.19L125.05,42.25l-28.94-26.42L12.33,108.01v29.18h26.2Z"/>
  <polygon class="cls-1" points="0 104.78 0 104.94 .15 104.78 0 104.78"/>
  <path class="cls-3" d="M96.11,0L.15,104.78l-.15.17v44.85h42.17l98.71-107.55L96.11,0ZM12.33,108.01L96.11,15.83l28.94,26.42-86.52,94.94H12.33v-29.18Z"/>
</svg>