import { m } from 'framer-motion';

import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import { RouterLink } from '@/routes/components';

import { UnauthorisedIllustration } from '@/assets/illustrations';

import { varBounce, MotionContainer } from '@/components/animate';
import { useTranslate } from '@/locales/use-locales';


// ----------------------------------------------------------------------

interface NoPermissionPageProp {
  showImage?: boolean;
  URI: string;
}

export default function NoPermissionPage({ showImage = false, URI }: NoPermissionPageProp) {
  const { t } = useTranslate();

  return (
    <MotionContainer
      sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}
    >
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          {t('Access Denied')}
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ textAlign: 'center' }}>
          The page you&apos;re trying access has restricted access.
          <br />
          Please refer to your system administrator
        </Typography>
      </m.div>

      {showImage ? (
        <m.div variants={varBounce().in}>
          <UnauthorisedIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />
        </m.div>
      ) : null}

      {/* <br /> */}

      {/* <Button component={RouterLink} href={URI} target="_blank" variant="contained">
        {t('Request Access')}
      </Button> */}
    </MotionContainer>
  );
}
