<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 205.75 230.72">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: url(#linear-gradient-6);
      }

      .cls-6 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="71.53" y1="228.28" x2="134.22" y2="228.28" gradientTransform="translate(0 232.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".03" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".05" stop-color="#0f0f19" stop-opacity=".09"/>
      <stop offset=".31" stop-color="#0f0f19" stop-opacity=".76"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".7" stop-color="#0f0f19" stop-opacity=".73"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="59.89" y1="101.15" x2="145.86" y2="101.15" gradientTransform="translate(0 232.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".03" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".13" stop-color="#0f0f19" stop-opacity=".43"/>
      <stop offset=".31" stop-color="#0f0f19" stop-opacity=".86"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".69" stop-color="#0f0f19" stop-opacity=".82"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".31"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="102.87" y1="2" x2="102.87" y2="223.84" gradientTransform="translate(0 232.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".09" stop-color="#0f0f19" stop-opacity=".43"/>
      <stop offset=".24" stop-color="#0f0f19" stop-opacity=".86"/>
      <stop offset=".4" stop-color="#0f0f19"/>
      <stop offset=".62" stop-color="#0f0f19"/>
      <stop offset=".77" stop-color="#0f0f19" stop-opacity=".8"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity=".22"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="83.93" y1="68.83" x2="83.93" y2="26.19" gradientTransform="translate(0 232.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="129.49" y1="81.91" x2="129.49" y2="55.24" gradientTransform="translate(0 232.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="72.71" y1="111.17" x2="72.71" y2="153.24" gradientTransform="translate(-78.03 107.1) rotate(58.69) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".09" stop-color="#0f0f19" stop-opacity=".43"/>
      <stop offset=".24" stop-color="#0f0f19" stop-opacity=".86"/>
      <stop offset=".4" stop-color="#0f0f19"/>
      <stop offset=".62" stop-color="#0f0f19"/>
      <stop offset=".77" stop-color="#0f0f19" stop-opacity=".8"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity=".22"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <rect class="cls-6" x="71.53" width="62.69" height="8.87"/>
  <rect class="cls-1" x="59.89" y="127.13" width="85.97" height="8.87"/>
  <path class="cls-3" d="M190.72,230.72H15.03C6.73,230.71,0,223.99,0,215.69c0-2.75.76-5.46,2.19-7.81l77.48-127.34V8.88h8.87v74.14l-.65,1.06L9.77,212.48c-1.77,2.91-.85,6.69,2.06,8.46.96.59,2.07.9,3.2.9h175.69c3.4,0,6.16-2.76,6.16-6.16,0-1.13-.31-2.24-.9-3.2L117.21,83.02V8.88h8.87v71.65l77.48,127.34c4.31,7.09,2.06,16.34-5.03,20.65-2.35,1.43-5.05,2.19-7.81,2.19h0Z"/>
  <path class="cls-2" d="M83.93,206.53c-11.77,0-21.32-9.54-21.32-21.32,0-11.77,9.54-21.32,21.32-21.32,11.77,0,21.32,9.54,21.32,21.32h0c-.01,11.77-9.55,21.31-21.32,21.32ZM83.93,172.76c-6.88,0-12.45,5.57-12.45,12.45s5.57,12.45,12.45,12.45,12.45-5.57,12.45-12.45c0-6.87-5.58-12.44-12.45-12.45h0Z"/>
  <path class="cls-4" d="M129.49,177.47c-7.36,0-13.33-5.97-13.33-13.33s5.97-13.33,13.33-13.33,13.33,5.97,13.33,13.33h0c0,7.36-5.97,13.32-13.33,13.33ZM129.49,159.68c-2.46,0-4.46,2-4.46,4.46,0,2.46,2,4.46,4.46,4.46,2.46,0,4.46-2,4.46-4.46,0-2.46-2-4.46-4.46-4.46Z"/>
  <rect class="cls-5" x="50.78" y="96.07" width="43.85" height="8.87" transform="translate(-50.95 110.39) rotate(-58.69)"/>
</svg>