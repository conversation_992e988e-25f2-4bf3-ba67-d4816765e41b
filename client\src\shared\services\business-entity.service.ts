import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';
import { API_PATHS } from '../constants';
import { PERMISSIONS } from '../enum/permission.enum';
import * as http from '@/shared/services';

export const getBusinessUnitHierarchyByPermission = (
  permission: PERMISSIONS,
  lastLevel: string = '',
  locationMerging: boolean = false,
): Promise<TreeViewDataType> => {
  return http.get<TreeViewDataType>(API_PATHS.BUSINESS_ENTITY.BUSINESS_HIERARCHY, {
    params: { permission, lastLevel, locationMerging: locationMerging },
  });
};

export const pruneNode = (treeData: TreeViewDataType, roleDetail: any): TreeViewDataType => {
  if (treeData.entityType === roleDetail.roleScope) {
    treeData.children = [];
    treeData.showCheckbox = true;
    treeData.disabled = false;
    return treeData;
  }

  treeData.showCheckbox = !(roleDetail.restrictParentSelect && treeData.entityType !== roleDetail.roleScope);
  treeData.disabled = roleDetail.restrictParentSelect && treeData.entityType !== roleDetail.roleScope;

  if (treeData.children && treeData.children.length > 0) {
    treeData.children = treeData.children.map((child) => pruneNode(child, roleDetail));
  }
  return treeData;
};
