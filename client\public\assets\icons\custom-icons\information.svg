<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 105.83 105.83">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }
    </style>
    <linearGradient id="linear-gradient" x1="52.92" y1="1165.64" x2="52.92" y2="1059.81" gradientTransform="translate(0 1165.64) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#13131b"/>
      <stop offset=".42" stop-color="#13131b" stop-opacity=".86"/>
      <stop offset=".77" stop-color="#13131b" stop-opacity=".44"/>
      <stop offset="1" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="52.92" y1="1098.95" x2="52.92" y2="1142.95" gradientTransform="translate(0 1165.64) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#13131b"/>
      <stop offset=".39" stop-color="#13131b" stop-opacity=".74"/>
      <stop offset=".94" stop-color="#13131b" stop-opacity=".02"/>
      <stop offset=".95" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="52.92" y1="1080.38" x2="52.92" y2="1089.84" gradientTransform="translate(0 1165.64) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#13131b"/>
      <stop offset=".41" stop-color="#13131b" stop-opacity=".74"/>
      <stop offset=".99" stop-color="#13131b" stop-opacity=".02"/>
      <stop offset="1" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-1" d="M52.92,105.83C23.69,105.83,0,82.14,0,52.92S23.69,0,52.92,0s52.92,23.69,52.92,52.92h0c-.03,29.21-23.7,52.88-52.92,52.92ZM52.92,4.55c-26.71,0-48.36,21.65-48.36,48.36,0,26.71,21.65,48.36,48.36,48.36,26.71,0,48.36-21.65,48.36-48.36-.03-26.7-21.67-48.33-48.36-48.36h0Z"/>
  <rect class="cls-2" x="50.64" y="22.69" width="4.55" height="44"/>
  <rect class="cls-3" x="50.64" y="75.8" width="4.55" height="9.47"/>
</svg>