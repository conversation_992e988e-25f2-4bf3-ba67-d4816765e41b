import { useEffect } from 'react';

import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';

import { usePathname } from '@/routes/hooks';

import { useMockedUser } from '@/hooks/use-mocked-user';
import { useResponsive } from '@/hooks/use-responsive';

import Logo from '@/components/logo';
import { NavSectionVertical } from '@/components/nav-section';
import Scrollbar from '@/components/scrollbar';

import { useTheme } from '@mui/material';
import NavToggleButton from '../common/nav-toggle-button';
import { NAV, useNavData } from '../config-layout';

// ----------------------------------------------------------------------

type Props = {
  openNav: boolean;
  onCloseNav: VoidFunction;
};

export default function NavVertical({ openNav, onCloseNav }: Props) {
  const { user } = useMockedUser();
  const theme = useTheme();
  const pathname = usePathname();

  const lgUp = useResponsive('up', 'lg');

  const { data: navData } = useNavData();

  useEffect(() => {
    if (openNav) {
      onCloseNav();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const renderContent = (
    <Scrollbar
      sx={{
        height: 1,
        '& .simplebar-content': {
          height: 1,
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <Logo sx={{ mt: 3, ml: 4, mb: 1 }} openNav={openNav} />

      <NavSectionVertical
        data={navData}
        slotProps={{
          currentRole: user?.role,
        }}
      />

      <Box sx={{ flexGrow: 1 }} />
    </Scrollbar>
  );

  return lgUp ? (
    <></>
  ) : (
    <Box
      sx={{
        flexShrink: { lg: 0 },
        width: { lg: NAV.W_VERTICAL },
        bgcolor: `${theme.palette.common.white}`,
        color: `${theme.palette.common.black}`,
      }}
    >
      <NavToggleButton />

      <Drawer
        open={openNav}
        onClose={onCloseNav}
        PaperProps={{
          sx: {
            width: NAV.W_VERTICAL,
          },
        }}
      >
        {renderContent}
      </Drawer>
    </Box>
  );
}
