import { useContext } from 'react';
import { UserPermissionsContext } from '../contexts';

const usePermission = () => {
  const userPermissions = useContext(UserPermissionsContext);

  /**
   * Check the user has given permission on given location.
   * @param permissionName
   * @param locationId
   * @returns
   */
  const isPermissionGrantedForEntity = (permissionName: string, locationId: string): boolean => {
    const permission = userPermissions.find((permission) => permission.permissionName === permissionName);
    if (!permission) {
      return false;
    }
    return permission.locations?.includes(locationId.toString());
  };

  /**
   * Check user has the permission by permission name.
   * @param permissionName
   * @returns
   */
  const isPermissionGranted = (permissionName: string): boolean => {
    const permission = userPermissions.find((permission) => permission.permissionName === permissionName);
    return !!permission;
  };

  return { isPermissionGrantedForEntity, isPermissionGranted };
};

export default usePermission;
