<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 650 500">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-2 {
        fill: url(#linear-gradient-3);
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }
    </style>
    <linearGradient id="linear-gradient" x1="211" y1="250.6" x2="442.37" y2="250.6" gradientTransform="translate(0 502) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".93"/>
      <stop offset=".33" stop-color="#0f0f19" stop-opacity=".74"/>
      <stop offset=".63" stop-color="#0f0f19" stop-opacity=".43"/>
      <stop offset=".99" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="238.99" y1="216.79" x2="413.13" y2="216.79" gradientTransform="translate(0 502) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".93"/>
      <stop offset=".33" stop-color="#0f0f19" stop-opacity=".74"/>
      <stop offset=".63" stop-color="#0f0f19" stop-opacity=".43"/>
      <stop offset=".99" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="239.57" y1="263.92" x2="288.56" y2="263.92" gradientTransform="translate(0 502) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".93"/>
      <stop offset=".33" stop-color="#0f0f19" stop-opacity=".74"/>
      <stop offset=".63" stop-color="#0f0f19" stop-opacity=".43"/>
      <stop offset=".99" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-1" d="M442.37,317.94H211V184.86h231.37v133.08Zm-222.5-8.87h213.63v-115.34H219.86v115.34Z"/>
  <rect class="cls-3" x="238.99" y="280.77" width="174.14" height="8.87"/>
  <path class="cls-2" d="M288.56,262.58h-49v-49h49v49Zm-40.13-8.87h31.26v-31.26h-31.26v31.26Z"/>
</svg>