import { m } from 'framer-motion';
import { useState, useEffect } from 'react';
import Box, { BoxProps } from '@mui/material/Box';

import Logo from '../logo';
import { Typography } from '@mui/material';

// ----------------------------------------------------------------------

export default function SplashScreen({ sx, ...other }: BoxProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <Box
      sx={{
        right: 0,
        width: 1,
        bottom: 0,
        height: 1,
        zIndex: 9998,
        display: 'flex',
        position: 'absolute',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        ...sx,
      }}
      {...other}
    >
      <>
        <m.div
          animate={{
            opacity: [1, 0.48, 0.48, 1, 1],
          }}
          transition={{
            duration: 2,
            ease: 'easeInOut',
            repeatDelay: 1,
            repeat: Infinity,
          }}
        >
          <Logo disabledLink sx={{ width: 250 }} />
          <Typography sx={{ textAlign: 'center', marginLeft: 8 }}>Loading...</Typography>
        </m.div>
      </>
    </Box>
  );
}
