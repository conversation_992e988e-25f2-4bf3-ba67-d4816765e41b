<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 125.27 237.05">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }

      .cls-3 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="-16365.98" y1="10743.48" x2="-16233.03" y2="10743.48" gradientTransform="translate(-10692.25 -16112.52) rotate(-90)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".77"/>
      <stop offset=".93" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-10505.94" y1="22796.23" x2="-10372.99" y2="22796.23" gradientTransform="translate(10488.4 -22746)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".77"/>
      <stop offset=".93" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <polygon class="cls-3" points="94.16 137.93 112.73 119.42 93.83 100.46 100.12 94.2 125.27 119.45 100.42 144.21 94.16 137.93"/>
  <rect class="cls-2" x="46.8" y="120.51" width="8.87" height="132.95" transform="translate(-45.05 355.26) rotate(-134.9)"/>
  <rect class="cls-1" x="-16.42" y="45.79" width="132.95" height="8.87" transform="translate(49.8 121.13) rotate(-134.89)"/>
</svg>