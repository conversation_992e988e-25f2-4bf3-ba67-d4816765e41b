import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
import { SeverErrorIllustration } from '@/assets/illustrations';
import { varBounce, MotionContainer } from '@/components/animate';
import { Button } from '@mui/material';
import { useRedirect } from '@/core/hooks';
import { useLocation } from 'react-router';
import { FormEvent } from 'react';

export default function Page500() {
  const navigate = useRedirect();
  const location = useLocation();

  const handleClick = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Prevent default form submission behavior
    navigate(location.pathname);
    return true;
  };

  return (
    <MotionContainer
      sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}
    >
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          500 Internal Server Error
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary' }}>There was an error, please try again later.</Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <SeverErrorIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />
      </m.div>

      <form onSubmit={handleClick}>
        <Button type="submit" size="large" variant="contained">
          Try Again
        </Button>
      </form>
    </MotionContainer>
  );
}
