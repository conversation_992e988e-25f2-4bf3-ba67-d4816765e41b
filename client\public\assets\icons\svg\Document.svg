<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 168.62 230.71">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: url(#linear-gradient-7);
      }

      .cls-6 {
        fill: url(#linear-gradient-6);
      }

      .cls-7 {
        fill: url(#linear-gradient);
      }

      .cls-8 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="84.31" y1="232.71" x2="84.31" y2="2" gradientTransform="translate(0 232.71) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".29" stop-color="#0f0f19"/>
      <stop offset=".72" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="40.66" y1="183.96" x2="9.64" y2="183.96" gradientTransform="translate(0 232.71) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="48.79" y1="190.57" x2="48.79" y2="223.87" gradientTransform="translate(0 232.71) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="106.49" y1="206.17" x2="106.49" y2="135.09" gradientTransform="translate(0 232.71) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="26.62" y1="104.06" x2="141.98" y2="104.06" gradientTransform="translate(0 232.71) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="26.62" y1="72.96" x2="141.98" y2="72.96" gradientTransform="translate(0 232.71) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="26.62" y1="41.87" x2="141.98" y2="41.87" gradientTransform="translate(0 232.71) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-7" d="M168.62,230.71H0V46.92L46.96,0h121.66v230.71ZM8.87,221.84h150.88V8.87H50.63L8.87,50.59v171.25Z"/>
  <rect class="cls-1" x="9.64" y="44.32" width="31.03" height="8.87"/>
  <polygon class="cls-8" points="53.23 53.19 40.66 53.19 40.66 44.32 44.36 44.32 44.36 42.14 53.23 42.14 53.23 53.19"/>
  <rect class="cls-3" x="44.36" y="8.84" width="8.87" height="33.3"/>
  <path class="cls-2" d="M142.02,97.62h-71.07V26.54h71.07v71.07ZM79.82,88.75h53.33v-53.33h-53.33v53.33Z"/>
  <rect class="cls-4" x="26.62" y="124.21" width="115.36" height="8.87"/>
  <rect class="cls-6" x="26.62" y="155.31" width="115.36" height="8.87"/>
  <rect class="cls-5" x="26.62" y="186.41" width="115.36" height="8.87"/>
</svg>