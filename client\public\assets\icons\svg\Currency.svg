<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 266.24 220.27">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-15);
      }

      .cls-2 {
        fill: url(#linear-gradient-13);
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }

      .cls-4 {
        fill: url(#linear-gradient-10);
      }

      .cls-5 {
        fill: url(#linear-gradient-12);
      }

      .cls-6 {
        fill: url(#linear-gradient-4);
      }

      .cls-7 {
        fill: url(#linear-gradient-3);
      }

      .cls-8 {
        fill: url(#linear-gradient-5);
      }

      .cls-9 {
        fill: url(#linear-gradient-8);
      }

      .cls-10 {
        fill: url(#linear-gradient-14);
      }

      .cls-11 {
        fill: url(#linear-gradient-17);
      }

      .cls-12 {
        fill: url(#linear-gradient-7);
      }

      .cls-13 {
        fill: url(#linear-gradient-9);
      }

      .cls-14 {
        fill: url(#linear-gradient-11);
      }

      .cls-15 {
        fill: url(#linear-gradient-6);
      }

      .cls-16 {
        fill: url(#linear-gradient-16);
      }

      .cls-17 {
        fill: url(#linear-gradient);
      }

      .cls-18 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="82.76" y1="189.6" x2="266.24" y2="189.6" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".53" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="17514.33" y1="-2902.79" x2="17514.33" y2="-2877.58" gradientTransform="translate(17601.53 2935.46) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".44" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".99" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="257.75" y1="133.5" x2="257.75" y2="189.6" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".35"/>
      <stop offset=".75" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="253.86" y1="129.5" x2="145.7" y2="129.5" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".58" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="255.61" y1="92.37" x2="255.61" y2="151.18" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".88" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="248.81" y1="89.93" x2="157.57" y2="89.93" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".58" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="257.75" y1="56.66" x2="257.75" y2="112.76" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".88" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="253.86" y1="52.66" x2="148.27" y2="52.66" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".58" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="257.75" y1="18.24" x2="257.75" y2="74.35" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".88" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="253.85" y1="14.24" x2="114.21" y2="14.24" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".72"/>
      <stop offset=".88" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="41.27" y1="113.23" x2="114.65" y2="113.23" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".47" stop-color="#0f0f19" stop-opacity=".74"/>
      <stop offset=".91" stop-color="#0f0f19" stop-opacity=".01"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="41.43" y1="52.92" x2="114.65" y2="52.92" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".47" stop-color="#0f0f19" stop-opacity=".74"/>
      <stop offset=".91" stop-color="#0f0f19" stop-opacity=".01"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="47.56" y1="97.21" x2="79.13" y2="97.21" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#0f0f19"/>
      <stop offset=".56" stop-color="#0f0f19" stop-opacity=".81"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".27"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="47.56" y1="69.02" x2="79.13" y2="69.02" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#0f0f19"/>
      <stop offset=".56" stop-color="#0f0f19" stop-opacity=".81"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".27"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="42.49" y1="69.02" x2="26.27" y2="69.02" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".39" stop-color="#0f0f19"/>
      <stop offset=".62" stop-color="#0f0f19" stop-opacity=".82"/>
      <stop offset=".87" stop-color="#0f0f19" stop-opacity=".28"/>
      <stop offset=".97" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="43.01" y1="97.21" x2="25.21" y2="97.21" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".39" stop-color="#0f0f19"/>
      <stop offset=".62" stop-color="#0f0f19" stop-opacity=".82"/>
      <stop offset=".87" stop-color="#0f0f19" stop-opacity=".28"/>
      <stop offset=".97" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="81.15" y1="164.3" x2="81.15" y2="2" gradientTransform="translate(0 222.27) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-17" d="M174.5,65.33c-44.47,0-91.74-11.45-91.74-32.67S130.02,0,174.5,0s91.74,11.45,91.74,32.67-47.27,32.67-91.74,32.67ZM174.5,8.87c-51.34,0-82.87,13.86-82.87,23.8s31.53,23.8,82.87,23.8,82.87-13.86,82.87-23.8-31.53-23.8-82.87-23.8Z"/>
  <rect class="cls-3" x="82.76" y="32.67" width="8.87" height="25.21"/>
  <path class="cls-7" d="M253.86,88.77l-4.59-7.59c5.23-3.16,8.11-6.53,8.11-9.48v-39.03h8.87v39.03c0,4.37-2.15,10.88-12.39,17.07Z"/>
  <path class="cls-6" d="M174.5,104.36c-9.62,0-19.24-.5-28.8-1.54l.97-8.82c9.24,1,18.54,1.5,27.83,1.49,31.54,0,60.18-5.48,74.76-14.3l4.59,7.59c-15.88,9.61-46.29,15.59-79.35,15.59Z"/>
  <path class="cls-8" d="M248.81,129.9l-3.83-8c7.87-3.77,12.39-8.06,12.39-11.78v-39.03h8.87v39.03c0,5.22-3.02,12.89-17.43,19.78Z"/>
  <path class="cls-15" d="M174.5,142.77c-5.72,0-11.42-.18-16.93-.53l.56-8.85c5.32.34,10.83.51,16.36.51,28.42,0,54.76-4.49,70.48-12.01l3.83,8c-16.85,8.06-44.63,12.88-74.31,12.88Z"/>
  <path class="cls-12" d="M253.86,165.6l-4.59-7.59c5.23-3.16,8.11-6.53,8.11-9.48v-39.03h8.87v39.03c0,4.37-2.15,10.88-12.39,17.07Z"/>
  <path class="cls-9" d="M174.5,181.19c-8.98,0-17.8-.43-26.23-1.28l.89-8.83c8.14.82,16.67,1.23,25.35,1.23,31.54,0,60.18-5.48,74.76-14.3l4.59,7.59c-15.88,9.61-46.29,15.59-79.35,15.59Z"/>
  <path class="cls-13" d="M253.85,204.02l-4.59-7.59c5.23-3.17,8.11-6.54,8.11-9.49v-39.03h8.87v39.03c0,4.37-2.15,10.88-12.39,17.08Z"/>
  <path class="cls-4" d="M174.5,219.62c-22.35,0-43.76-2.71-60.29-7.62l2.53-8.5c15.73,4.68,36.25,7.25,57.76,7.25,31.53,0,60.18-5.48,74.76-14.31l4.59,7.59c-15.89,9.62-46.29,15.59-79.35,15.59Z"/>
  <path class="cls-14" d="M49.53,125.37l-8.25-3.25c9.39-23.84,36.33-35.56,60.18-26.17,4.81,1.9,9.28,4.58,13.2,7.95l-5.78,6.73c-15.74-13.49-39.43-11.67-52.92,4.06-2.72,3.18-4.9,6.78-6.43,10.68h0Z"/>
  <path class="cls-18" d="M41.43,156.5c-4.44-11.02-4.49-23.33-.15-34.39l8.25,3.25c-3.51,8.95-3.46,18.89.12,27.81l-8.22,3.33Z"/>
  <path class="cls-5" d="M84.46,185.51c-18.93.06-35.99-11.44-43.04-29.01l8.22-3.33c7.77,19.22,29.65,28.5,48.86,20.73,3.77-1.53,7.27-3.65,10.36-6.3l5.78,6.73c-8.41,7.21-19.12,11.18-30.19,11.18Z"/>
  <rect class="cls-2" x="47.56" y="120.62" width="31.57" height="8.87"/>
  <rect class="cls-10" x="47.56" y="148.81" width="31.57" height="8.87"/>
  <rect class="cls-1" x="26.27" y="148.81" width="16.22" height="8.87"/>
  <rect class="cls-16" x="25.21" y="120.62" width="17.8" height="8.87"/>
  <path class="cls-11" d="M81.15,220.27C36.33,220.27,0,183.93,0,139.12S36.33,57.97,81.15,57.97s81.15,36.33,81.15,81.15h0c-.05,44.8-36.35,81.1-81.15,81.15ZM81.15,66.84c-39.92,0-72.28,32.36-72.28,72.28s32.36,72.28,72.28,72.28,72.28-32.36,72.28-72.28h0c-.05-39.9-32.38-72.23-72.28-72.28h0Z"/>
</svg>