<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 293.64 212.56">
  <defs>
    <style>
      .cls-1 {
        fill: #f5f3f5;
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-3 {
        fill: url(#linear-gradient-4);
      }

      .cls-4 {
        fill: url(#linear-gradient-3);
      }

      .cls-5 {
        fill: url(#linear-gradient-5);
      }

      .cls-6 {
        isolation: isolate;
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: url(#linear-gradient-6);
      }

      .cls-9 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="18.26" y1="33.09" x2="175.52" y2="33.09" gradientTransform="translate(0 206.11) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#f5f3f5"/>
      <stop offset=".25" stop-color="#f5f3f5" stop-opacity=".9"/>
      <stop offset=".47" stop-color="#f5f3f5" stop-opacity=".66"/>
      <stop offset=".78" stop-color="#f5f3f5" stop-opacity=".27"/>
      <stop offset=".98" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="261.79" y1="193.09" x2="261.79" y2="107.9" gradientTransform="translate(0 206.11) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#f5f3f5"/>
      <stop offset=".23" stop-color="#f5f3f5" stop-opacity=".98"/>
      <stop offset=".34" stop-color="#f5f3f5" stop-opacity=".91"/>
      <stop offset=".46" stop-color="#f5f3f5" stop-opacity=".8"/>
      <stop offset=".59" stop-color="#f5f3f5" stop-opacity=".64"/>
      <stop offset=".73" stop-color="#f5f3f5" stop-opacity=".44"/>
      <stop offset=".88" stop-color="#f5f3f5" stop-opacity=".2"/>
      <stop offset=".98" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="234.24" y1="112.34" x2="234.24" y2="-6.45" gradientTransform="translate(0 206.11) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#f5f3f5"/>
      <stop offset=".43" stop-color="#f5f3f5" stop-opacity=".91"/>
      <stop offset=".58" stop-color="#f5f3f5" stop-opacity=".67"/>
      <stop offset=".79" stop-color="#f5f3f5" stop-opacity=".29"/>
      <stop offset=".94" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-142.49" y1="6646.24" x2="-90.51" y2="6646.24" gradientTransform="translate(365.01 6797) scale(1 -1)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-5" x1="36.95" y1="159.85" x2="225.01" y2="159.85" gradientTransform="translate(0 206.11) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f5f3f5"/>
      <stop offset=".09" stop-color="#f5f3f5" stop-opacity=".94"/>
      <stop offset=".27" stop-color="#f5f3f5" stop-opacity=".8"/>
      <stop offset=".51" stop-color="#f5f3f5" stop-opacity=".56"/>
      <stop offset=".81" stop-color="#f5f3f5" stop-opacity=".23"/>
      <stop offset="1" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" y1="117.38" x2="160.51" y2="117.38" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-7" y1="74.92" x2="150.78" y2="74.92" xlink:href="#linear-gradient-5"/>
  </defs>
  <rect class="cls-9" x="18.26" y="168.58" width="157.26" height="8.87"/>
  <polygon class="cls-1" points="18.26 177.46 0 177.46 0 0 266.22 0 266.22 13.02 257.35 13.02 257.35 8.87 8.87 8.87 8.87 168.58 18.26 168.58 18.26 177.46"/>
  <rect class="cls-2" x="257.35" y="13.02" width="8.87" height="85.19"/>
  <path class="cls-4" d="M234.24,212.56c-32.8,0-59.39-26.59-59.39-59.39,0-32.8,26.59-59.39,59.39-59.39,32.8,0,59.39,26.59,59.39,59.39h0c-.04,32.79-26.61,59.36-59.39,59.39ZM234.24,102.65c-27.9,0-50.52,22.62-50.52,50.52,0,27.9,22.62,50.52,50.52,50.52,27.9,0,50.52-22.62,50.52-50.52h0c-.03-27.89-22.63-50.49-50.52-50.52Z"/>
  <image class="cls-6" width="107" height="106" transform="translate(198.58 147.53) scale(.24)" xlink:href="data:image/png;base64,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"/>
  <polygon class="cls-1" points="225.05 180.23 216.77 171.94 223.04 165.67 225.05 167.68 226.98 165.76 233.25 172.03 225.05 180.23"/>
  <rect class="cls-3" x="222.26" y="146.33" width="51.98" height="8.87" transform="translate(-33.9 219.68) rotate(-45)"/>
  <rect class="cls-5" x="36.95" y="41.83" width="188.05" height="8.87"/>
  <rect class="cls-8" x="36.95" y="84.29" width="123.56" height="8.87"/>
  <rect class="cls-7" x="36.95" y="126.76" width="113.82" height="8.87"/>
</svg>