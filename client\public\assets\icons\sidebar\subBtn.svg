<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 622.29 157.87">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #fff;
        font-family: Pilat-DemiBold, Pilat;
        font-size: 72.06px;
        font-weight: 300;
      }
    </style>
    <linearGradient id="linear-gradient" x1="0" y1="78.93" x2="622.29" y2="78.93" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#5154a3"/>
      <stop offset=".26" stop-color="#4f519f"/>
      <stop offset=".51" stop-color="#494893"/>
      <stop offset=".76" stop-color="#403a80"/>
      <stop offset="1" stop-color="#332765"/>
      <stop offset="1" stop-color="#332765"/>
    </linearGradient>
  </defs>
  <rect class="cls-1" y="0" width="622.29" height="157.87" rx="72.9" ry="72.9"/>
  <text class="cls-2" transform="translate(191.27 101.81)"><tspan x="0" y="0">Submit</tspan></text>
</svg>