import Skeleton, { SkeletonProps } from '@mui/material/Skeleton';
import { Box } from '@mui/material';

interface CardSkeletonProps extends SkeletonProps {
  count: number;
  spacing?: number;
}

export function CardSkeleton({ count = 4, spacing = 1, ...other }: CardSkeletonProps) {
  
  // If count is 1, just return a single skeleton
  if (count === 1) {
    return <Skeleton variant="rectangular" {...other} />;
  }

  // For multiple skeletons, render them in a row with spacing
  return (
    <Box display="flex" flexDirection="row" gap={spacing} width="100%">
      {Array.from({ length: count }).map((_, index) => (
        <Box key={index} flex={1}>
          <Skeleton variant="rectangular" {...other} sx={{ width: '100%', ...other.sx }} />
        </Box>
      ))}
    </Box>
  );
}
