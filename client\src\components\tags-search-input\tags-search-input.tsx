import { GraphUserResponse, UserDetail } from '@/shared/models';
import { getTagsByType, getUserDetails, searchGraphUsers } from '@/shared/services';
import getUserLoginId from '@/shared/utils/get-user-login-id';
import { Box, CircularProgress } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { Stack } from '@mui/system';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { RHFAUtoCompleteErrorStyling } from '../hook-form/rhf-autocomplete';
import { enqueueSnackbar } from 'notistack';

type Prop = {
  initialUserId?: string | null;
  onSelect: (values: string[]) => void;
  handleClear?: () => void;
  required?: boolean;
  validationError?: string;
  placeholder?: string;
  size?: 'small' | 'medium';
  isDisabled?: boolean;
  initialTags?: string[] | undefined;
  type: 'CAPABILITY' | 'LOCATION';
};

export default function TagsSearchInput({
  onSelect,
  placeholder,
  size = 'medium',
  isDisabled = false,
  handleClear,
  initialTags,
  type,
}: Prop) {
  console.log('🚀 ~ initialTags:', initialTags);
  const { t } = useTranslation();
  const [value, setValue] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isLoadingInitialTags, setIsLoadingInitialTags] = useState(false);
  const { data, isFetching, refetch } = useQuery({
    enabled: type != undefined && searchText.trim() != '',
    queryKey: ['tags', type, searchText],
    queryFn: () => {
      if (type && searchText.trim() != '') {
        console.log('🚀 ~ searchText.trim():', searchText.trim());
        return getTagsByType(type, searchText.trim());
      }
      return [];
    },
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
  });
  useEffect(() => {
    if (initialTags && initialTags?.length) {
      const getInitialTags = async () => {
        let allInitialtags = [];
        setIsLoadingInitialTags(true);
        for (let i = 0; i < initialTags.length; i++) {
          const tag = await getTagsByType(type, initialTags[i]);
          if (tag.length > 0) {
            allInitialtags.push(tag[0]);
          }
          console.log('🚀 ~ getInitialTags ~ allInitialtags:', allInitialtags);
        }
        setValue(allInitialtags);
      };
      getInitialTags();
      setIsLoadingInitialTags(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const debouncedSetSearchText = useMemo(() => debounce(setSearchText, 500), []);
  const formContext = useFormContext();
  const errors = formContext ? useFormState().errors : {};
  useEffect(() => {
    if (searchText.length) {
      refetch();
    }
  }, [refetch, searchText]);

  return (
    <Autocomplete
      size={size}
      loading={isFetching}
      fullWidth
      id="tagsSearchByType"
      filterOptions={(x) => x}
      options={data?.filter((item: string) => !initialTags?.includes(item)) ?? []}
      disabled={isDisabled}
      isOptionEqualToValue={(option, value) => option === value}
      autoComplete
      includeInputInList
      value={value}
      sx={RHFAUtoCompleteErrorStyling(Boolean(errors?.userDetails))}
      noOptionsText={
        searchText ? (
          <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
            {t('empty_state_messages.no_tags_found')}
          </Typography>
        ) : (
          <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
            {t('messages.type_something_to_search')}
          </Typography>
        )
      }
      onChange={(_, newValue: any) => {
        console.log('🚀 ~ newValue:', newValue);
        setValue(newValue);
        onSelect(newValue);
      }}
      slots={{
        paper: (props) => (
          <Box {...props} sx={{ borderRadius: 2, boxShadow: 3, p: 1 }}>
            {isFetching ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ marginLeft: 1 }}>{t('label.searching')}</Typography>
              </Box>
            ) : searchText && data?.length === 0 ? (
              <Typography sx={{ textAlign: 'center', p: 2, color: 'text.secondary' }}>
                {t('label.no_results_found')}
              </Typography>
            ) : (
              props.children
            )}
          </Box>
        ),
      }}
      onInputChange={(_event, value) => {
        debouncedSetSearchText(value);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={<span>{placeholder}</span>}
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
          fullWidth
        />
      )}
      multiple
      //   renderOption={(props, option) => {
      //     return (
      //       <li {...props} key={option.id}>
      //         <Grid container alignItems="center">
      //           <Grid sx={{ wordWrap: 'break-word' }}>
      //             <Stack flexDirection={'column'}>
      //               <Typography variant="subtitle1" color="text.secondary" fontSize={14}>
      //                 {option.displayName}
      //               </Typography>
      //               <Typography variant="subtitle2" color="text.secondary" fontSize={12}>
      //                 {option.mail}
      //               </Typography>
      //               <Typography variant="caption" color="text.secondary" fontSize={12}>
      //                 {option.jobTitle}
      //               </Typography>
      //             </Stack>
      //           </Grid>
      //         </Grid>
      //       </li>
      //     );
      //   }}
    />
  );
}
