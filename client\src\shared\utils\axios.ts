import axios, { AxiosRequestConfig } from 'axios';
import { AppConfig } from '../../config';

// ----------------------------------------------------------------------=
const axiosInstance = axios.create({
  baseURL: `${AppConfig.httpProtocol}://${window.location.hostname}:${AppConfig.baseUrl}`,
});

axiosInstance.interceptors.response.use(
  (res) => res,
  (error) => Promise.reject((error.response && error.response.data) || 'Something went wrong'),
);

export default axiosInstance;

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await axiosInstance.get(url, { ...config });

  return res.data;
};

export const endpoints = {
  calendar: '/api/calendar',
};