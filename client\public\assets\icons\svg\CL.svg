<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 293.64 212.56">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        isolation: isolate;
      }

      .cls-6 {
        fill: url(#linear-gradient-7);
      }

      .cls-7 {
        fill: url(#linear-gradient-6);
      }

      .cls-8 {
        fill: url(#linear-gradient);
      }

      .cls-9 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="18.26" y1="41.54" x2="175.52" y2="41.54" gradientTransform="translate(0 214.56) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".93" stop-color="#0f0f19" stop-opacity=".08"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="261.79" y1="201.55" x2="261.79" y2="116.35" gradientTransform="translate(0 214.56) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".76"/>
      <stop offset=".93" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="234.24" y1="120.79" x2="234.24" y2="2" gradientTransform="translate(0 214.56) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-138.78" y1="6655.21" x2="-86.8" y2="6655.21" gradientTransform="translate(361.29 6805.97) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".76"/>
      <stop offset=".93" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="36.95" y1="168.3" x2="225.01" y2="168.3" gradientTransform="translate(0 214.56) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="36.95" y1="125.84" x2="160.51" y2="125.84" gradientTransform="translate(0 214.56) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="36.95" y1="83.37" x2="150.78" y2="83.37" gradientTransform="translate(0 214.56) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <rect class="cls-8" x="18.26" y="168.58" width="157.26" height="8.87"/>
  <polygon class="cls-9" points="18.26 177.46 0 177.46 0 0 266.22 0 266.22 13.02 257.35 13.02 257.35 8.87 8.87 8.87 8.87 168.58 18.26 168.58 18.26 177.46"/>
  <rect class="cls-1" x="257.35" y="13.02" width="8.87" height="85.19"/>
  <path class="cls-3" d="M234.24,212.56c-32.8,0-59.39-26.59-59.39-59.39,0-32.8,26.59-59.39,59.39-59.39,32.8,0,59.39,26.59,59.39,59.39h0c-.04,32.79-26.61,59.36-59.39,59.39ZM234.24,102.65c-27.9,0-50.52,22.62-50.52,50.52,0,27.9,22.62,50.52,50.52,50.52,27.9,0,50.52-22.62,50.52-50.52h0c-.03-27.89-22.63-50.49-50.52-50.52Z"/>
  <image class="cls-5" width="107" height="106" transform="translate(198.58 147.53) scale(.24)" xlink:href="data:image/png;base64,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"/>
  <polygon class="cls-9" points="225.05 180.23 216.77 171.94 223.04 165.67 225.05 167.68 226.98 165.76 233.25 172.03 225.05 180.23"/>
  <rect class="cls-2" x="222.26" y="146.33" width="51.98" height="8.87" transform="translate(-33.9 219.68) rotate(-45)"/>
  <rect class="cls-4" x="36.95" y="41.83" width="188.05" height="8.87"/>
  <rect class="cls-7" x="36.95" y="84.29" width="123.56" height="8.87"/>
  <rect class="cls-6" x="36.95" y="126.76" width="113.82" height="8.87"/>
</svg>