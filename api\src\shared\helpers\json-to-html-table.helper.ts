import { format } from 'date-fns';
/**
 * Example:
    How to use -
    Basic usage -
    const options = {
        data: [{a: 1, b: 2}, {b: 1, a: 2}, {a: 4}],
        header: ['a', 'b', 'c'] (Optional),
        header_mapping: {
            a: 'X',
            c: 'Z'
        } (Optional),
        border: 5,
        cellspacing: 2,
        cellpadding: 3,
        css: 'table {border: 1px solid red}',
        table_class: 'my-table'
    };
    const htmlTable = jsonToHtmlTable(options);
    Output (pretty) -
    <table border=1 cellspacing=0 cellpadding=0>
        <thead>
            <tr> <th>a</th> <th>b</th> </tr>
        </thead>
        </tbody>
            <tr> <td>1</td> <td>2</td> </tr>
            <tr> <td>2</td> <td>1</td> </tr>
            <tr> <td>4</td> <td> </td> </tr>
        </tbody>
    </table>
 */

/**
 * Convert json data to HTML table.
 * @param options
 * @returns
 */
export function jsonToHtmlTable(
	options: {
		data?: any[];
		header?: string[];
		border?: number;
		cellspacing?: number;
		cellpadding?: number;
		table_id?: string;
		table_class?: string;
		header_mapping?: { [key: string]: string };
		pretty?: boolean;
		css?: string;
	} = {},
): string {
	let tableData = options.data || [];
	let header = options.header;
	const border = isDefined(options.border) ? options.border : 1;
	const cellspacing = isDefined(options.cellspacing) ? options.cellspacing : 0;
	const cellpadding = isDefined(options.cellpadding) ? options.cellpadding : 0;
	const tableId = options.table_id || 'tablify';
	const tableClass = options.table_class || 'tablify';
	const headerMapping = options.header_mapping || {};
	let pretty = options.pretty;
	if (pretty === undefined) {
		pretty = true;
	}
	let isSingleRow = false;
	if (!Array.isArray(tableData)) {
		isSingleRow = true;
		tableData = [tableData];
	}

	// If header exists in options use that else create it.
	if (!Array.isArray(header)) {
		const headerObj: { [key: string]: boolean } = {};
		tableData.forEach(json => {
			const keys = Object.keys(json);
			keys.forEach(key => {
				headerObj[key] = true;
			});
		});
		header = Object.keys(headerObj);
	}

	if (isSingleRow && tableData.length === 1) {
		// Don't create row if value is not defined for the header (key for objects)
		header = header.filter(h => tableData[0][h]);
	}

	// Generate table
	let htmlTable = '';
	let cellArray: string[][] = [];
	let cellRow: string[] = [];
	cellArray.push(cellRow);
	header.forEach(key => {
		cellRow.push(`<th>${headerMapping[key] || key}</th>`);
	});
	tableData.forEach(json => {
		cellRow = [];
		cellArray.push(cellRow);
		header.forEach(key => {
			let value = json[key];
			if (value === undefined) {
				value = '';
			} else if (typeof value !== 'string') {
				value = JSON.stringify(value);
			}
			cellRow.push(`<td>${value}</td>`);
		});
	});

	let i: number;
	let j: number;
	if (isSingleRow && cellArray.length) {
		// Transpose the array to show object as key-value pair instead of table
		cellArray = cellArray[0].map((col, i) => {
			return cellArray.map(row => {
				return row[i];
			});
		});
	}

	let newLine = '';
	let indent = '';
	if (pretty) {
		newLine = '\n';
		indent = '  ';
	}
	if (options.css) {
		htmlTable += `<style>${newLine}${indent}${options.css}${newLine}</style>${newLine}`;
	}
	if (tableData.length) {
		htmlTable += `<table id="${tableId}" class="${tableClass}" border="${border}" cellspacing="${cellspacing}" cellpadding="${cellpadding}">`;
		for (i = 0; i < cellArray.length; i++) {
			htmlTable += newLine;
			htmlTable += indent;
			htmlTable += '<tr>';
			for (j = 0; j < cellArray[i].length; j++) {
				htmlTable += newLine;
				htmlTable += indent;
				htmlTable += indent;
				htmlTable += cellArray[i][j];
			}
			htmlTable += newLine;
			htmlTable += indent;
			htmlTable += '</tr>';
		}
		htmlTable += newLine;
		htmlTable += '</table>';
	}
	return htmlTable;
}

function isDefined(x: any): boolean {
	return x !== undefined && x !== null;
}

export function filterTableData(data, placeholderFields: string[]) {
	// Keep specific fields from objects
	const tableData = data.map(obj => {
		const filteredObj = Object.fromEntries(
			Object.entries(obj).filter(([key]) => placeholderFields.includes(key)),
		);
		return filteredObj;
	});
	return tableData;
}

export const ReplaceUrlVariable = (url: string, variables: any) => {
	Object.keys(variables).forEach((variableKey: string) => {
		var re = new RegExp(':' + variableKey, 'g');
		url = url.replace(re, variables[variableKey]);
	});
	return url;
};

export const getTableCSS = () => {
	return `
		table {
			border-collapse: collapse;
			width: 100%;
		}
		th, td {
			border: 1px solid #333;
			padding: 8px;
			text-align: left;
		}
		th {
			background-color: #10004F;
			color: #fff;
		}
	`;
};

type InputValue = Date | string | number | null | undefined;

export function fDate(date: InputValue, newFormat?: string) {
	const fm = newFormat || 'dd MMM yyyy';

	return date ? format(new Date(date), fm) : '';
}
