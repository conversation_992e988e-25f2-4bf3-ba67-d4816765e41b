/* eslint-disable @typescript-eslint/no-explicit-any */
type NonFunctional<T> = T extends (...args: any[]) => any ? never : T;

/**
 * Helper to produce an array of enum values.
 * @param enumeration Enumeration object.
 */
export function enumToArray<T extends object>(enumeration: any): NonFunctional<T[keyof T]>[] {
  return Object.keys(enumeration)
    .filter((key) => isNaN(Number(key)))
    .map((key) => enumeration[key])
    .filter((val: unknown) => typeof val === 'number' || typeof val === 'string');
}
