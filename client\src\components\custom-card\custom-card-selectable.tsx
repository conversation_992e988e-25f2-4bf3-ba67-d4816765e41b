import { primaryFont } from '@/theme/typography';
import { Typography } from '@mui/material';
import { Box } from '@mui/system';

export default function CustomCardSelectable({ isActive, title }: { isActive: boolean; title: string }) {
  return (
    <Box
      sx={{
        alignContent: 'center',
        width: '144px',
        cursor: 'pointer',
        height: '62px',
        borderRadius: '9px',
        textAlign: 'center',
        border: isActive ? '1px solid #0705AB' : '1px solid #D8D8F3',
        ...(isActive && {
          background: 'transparent linear-gradient(180deg, #F7F6FF 0%, #FDFDFF 100%) 0% 0% no-repeat padding-box',
        }),
        ...(isActive && {
          boxShadow: '0px 2px 3px #00000029',
        }),
      }}
    >
      <Box width="90%" margin={'auto'}>
        <Typography
          variant="partnerBranchBody"
          fontFamily={primaryFont}
          fontSize={'13px'}
          sx={{
            ...(isActive && {
              fontWeight: 'bold',
            }),
          }}
          alignSelf={'center'}
        >
          {title}
        </Typography>
      </Box>
    </Box>
  );
}
