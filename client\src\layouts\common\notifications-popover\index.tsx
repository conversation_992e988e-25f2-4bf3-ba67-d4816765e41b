import { m } from 'framer-motion';
import { useState } from 'react';

import Badge from '@mui/material/Badge';
import Divider from '@mui/material/Divider';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { varHover } from '@/components/animate';
import { ConfirmDialog } from '@/components/custom-dialog';
import Iconify from '@/components/iconify';
import Scrollbar from '@/components/scrollbar';
import { useBoolean } from '@/hooks/use-boolean';
import { useResponsive } from '@/hooks/use-responsive';
import { useTranslate } from '@/locales/use-locales';
import { LoadingButton } from '@mui/lab';
import { Button } from '@mui/material';
import NotificationItem from './notification-item';

export const _notifications = [];

// ----------------------------------------------------------------------

export default function NotificationsPopover() {
  const drawer = useBoolean();
  const { t } = useTranslate();
  const smUp = useResponsive('up', 'sm');
  const confirm = useBoolean();

  const [notifications, setNotifications] = useState(_notifications);

  const totalUnRead = notifications.filter((item: any) => item.isUnRead === true).length;

  // const handleMarkAllAsRead = () => {
  //   setNotifications(
  //     notifications.map((notification: any) => ({
  //       ...notification,
  //       isUnRead: false,
  //     })),
  //   );
  // };

  const handleClearNotification = (id: number) => {
    setNotifications(notifications.filter((notification: any) => notification.id !== id));
  };

  // const handleClearAll = () => {
  //   setNotifications([]);
  // };

  const renderHead = (
    <Stack direction="row" alignItems="center" sx={{ py: 2, pl: 2.5, pr: 1, minHeight: 68 }}>
      <Typography variant="mainTitle" sx={{ flexGrow: 1 }}>
        {t('label.notifications')}
      </Typography>

      {/* {!!totalUnRead && (
        <Tooltip title="Mark all as read">
          <IconButton color="primary" onClick={handleMarkAllAsRead}>
            <Iconify icon="eva:done-all-fill" />
          </IconButton>
        </Tooltip>
      )} */}

      {notifications.length > 0 && (
        <Button
          variant="outlined"
          size={'small'}
          onClick={() => confirm.onTrue()}
          sx={{
            bgcolor: 'white',
          }}
        >
          {t('btn_name.clear_all')}
        </Button>
      )}

      <IconButton onClick={drawer.onFalse}>
        <Iconify icon="mingcute:close-line" />
      </IconButton>
    </Stack>
  );

  const renderList = (
    <Scrollbar>
      <List disablePadding>
        {notifications.map((notification: any) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            handleClearNotification={handleClearNotification}
          />
        ))}
      </List>
    </Scrollbar>
  );

  return (
    <>
      <IconButton
        component={m.button}
        whileTap="tap"
        whileHover="hover"
        variants={varHover(1.05)}
        color={drawer.value ? 'primary' : 'default'}
        onClick={drawer.onTrue}
        sx={{
          width: 40,
          height: 40,
        }}
      >
        <Badge badgeContent={totalUnRead} color="error">
          <Iconify icon="solar:bell-bing-bold-duotone" width={27} height={36} />
        </Badge>
      </IconButton>

      <Drawer
        open={drawer.value}
        onClose={drawer.onFalse}
        anchor="right"
        slotProps={{
          backdrop: { invisible: true },
          paper: {
            sx: { width: 1, maxWidth: 420 },
          },
        }}
      >
        {renderHead}

        <Divider />

        {/* <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ pl: 2.5, pr: 1 }}>
          {renderTabs}
          <IconButton onClick={handleMarkAllAsRead}>
            <Iconify icon="solar:settings-bold-duotone" />
          </IconButton>
        </Stack>

        <Divider /> */}

        {notifications.length == 0 ? (
          <Stack flexGrow={1} alignItems="center" justifyContent="center">
            <Typography variant="h6" component="span" sx={{ color: 'text.disabled', textAlign: 'center' }}>
              {t('empty_state_messages.no_notifications_found')}
            </Typography>
          </Stack>
        ) : (
          renderList
        )}

        {/* <Box sx={{ p: 1 }}>
          <Button fullWidth size="large">
            View All
          </Button>
        </Box> */}
      </Drawer>
      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={t('label.dismiss_notifications')}
        content={t('messages.are_you_sure_want_to_dismiss_notifications')}
        action={
          <LoadingButton
            variant="contained"
            onClick={async () => {
              try {
                confirm.onFalse();
                setNotifications([]);
              } catch (err) {
                console.debug(err);
              }
            }}
          >
            {t('label.dismiss')}
          </LoadingButton>
        }
      />
    </>
  );
}
