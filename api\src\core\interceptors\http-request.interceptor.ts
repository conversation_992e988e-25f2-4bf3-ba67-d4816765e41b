import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AuthTokenPayload } from 'src/shared/types';

/**
 * This interceptor will be used to attach the current
 * context in the incoming http request
 */
@Injectable()
export class HttpRequestInterceptor implements NestInterceptor {
	intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
		const request = context.switchToHttp().getRequest();

		

		const user: AuthTokenPayload = request.user;
		if (user) {
			//locations we are attaching in the permission guard.
			request.currentContext = { user, locations: request.user.locations };
			request.currentContext.user.username = user.unique_name.toLowerCase();
		}
		return next.handle();
	}
}
