import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import { Theme, SxProps } from '@mui/material/styles';

import EmptyContent from '../empty-content';
import { TableContainer, Table, TableBody } from '@mui/material';

// ----------------------------------------------------------------------

type Props = {
  notFound: boolean;
  sx?: SxProps<Theme>;
  message?: string;
};

export default function TableNoData({ notFound, sx, message = 'No Data' }: Props) {
  return (
    <TableContainer sx={{ overflowX: 'auto' }}>
      <Table sx={{ borderCollapse: 'separate', borderSpacing: '0 2px' }}>
        <TableBody>
          <TableRow>
            {notFound ? (
              <TableCell colSpan={12}>
                <EmptyContent
                  title={message}
                  sx={{
                    py: 10,
                    ...sx,
                  }}
                />
              </TableCell>
            ) : (
              <TableCell colSpan={12} sx={{ p: 0 }} />
            )}
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
}
