import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsNumber, IsOptional, IsString } from "class-validator";

export class AttachmentDto {
  @ApiProperty({ required: true })
  @IsString()
  file_base64: string;

  @ApiProperty({ required: true })
  @IsString()
  attachment_name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty({ required: false, nullable: true })
  @IsOptional()
  @IsString()
  fileimage?: string | null;

  @ApiProperty({ required: false })
  @IsOptional()
  file?: any;

  @ApiProperty({ required: true })
  @IsString()
  attachment_content_type: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  file_id?: string;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  isdeleted?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  isPrimary?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  isNew?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  isEdited?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  url?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  size?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  prev_description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  additional_info?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  section?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  created_by?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  created_on?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  progress?: number;
}