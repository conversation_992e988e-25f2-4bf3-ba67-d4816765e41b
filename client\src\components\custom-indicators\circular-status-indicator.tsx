import { Box } from '@mui/material';

const statusColors: Record<string, string> = {
  Planned: '#28a745', // Custom green
  Expired: '#dc3545', // Custom red
  Retired: '#fd7e14', // Custom orange
  Existing: '#007bff', // Custom blue
  OTHER: '##ffc300', // Custom blue
  INACTIVE: 'magento', // Custom blue
  IN_OPERATION: '#007bff', // Custom blue
};

export const StatusIndicator = ({ status }: { status: string }) => (
  <Box
    sx={{
      width: 6,
      height: 6,
      borderRadius: '50%',
      backgroundColor: statusColors[status] || 'grey',
      display: 'inline-block',
      marginRight: 1,
      outline: 1,
    }}
  />
);
