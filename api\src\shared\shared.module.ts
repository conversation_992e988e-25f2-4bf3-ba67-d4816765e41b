import { Global, Module } from '@nestjs/common';
import {
	AdminApiClient,
	HistoryApiClient,
	MSGraphApiClient,
	NotificationApiClient,
} from './clients';
import { AttachmentApiClient } from './clients/attachment-api.client';
import {
	EntityService,
	SharedAttachmentService,
	SharedNotificationService,
	SharedPermissionService,
} from './services';
import { WorkflowYearValidator } from './validators';

const repositories = [];
@Global()
@Module({
	providers: [
		AdminApiClient,
		EntityService,
		MSGraphApiClient,
		HistoryApiClient,
		SharedPermissionService,
		SharedAttachmentService,
		SharedNotificationService,
		WorkflowYearValidator,
		NotificationApiClient,
		AttachmentApiClient,
		...repositories,
	],
})
export class SharedModule {}
