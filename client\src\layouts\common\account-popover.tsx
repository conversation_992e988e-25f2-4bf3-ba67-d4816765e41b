import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import { alpha } from '@mui/material/styles';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { varHover } from '@/components/animate';
import { useSnackbar } from '@/components/snackbar';
import CustomPopover, { usePopover } from '@/components/custom-popover';
import { useMsal } from '@azure/msal-react';
import { Avatar } from '@mui/material';
import { useTranslate } from '@/locales/use-locales';
import { useNavigate } from 'react-router';

// ----------------------------------------------------------------------

export default function AccountPopover() {
  const { accounts, instance } = useMsal();

  const { enqueueSnackbar } = useSnackbar();

  const popover = usePopover();

  const handleLogout = async () => {
    try {
      await instance.logoutRedirect();
    } catch (error) {
      enqueueSnackbar('Unable to logout!', { variant: 'error' });
    }
  };
  const navigate = useNavigate();
  
  const { t } = useTranslate();
  return (
    <>
      <IconButton
        component={m.button}
        whileTap="tap"
        whileHover="hover"
        variants={varHover(1.05)}
        onClick={popover.onOpen}
        sx={{
          width: 40,
          height: 40,
          background: (theme) => alpha(theme.palette.grey[500], 0.08),
          ...(popover.open && {
            background: (theme) =>
              `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
          }),
        }}
      >
        {
          <Avatar
            alt={accounts[0]?.name}
            sx={{
              width: 36,
              height: 36,
              fontSize: 14,
              border: (theme) => `solid 2px ${theme.palette.background.default}`,
            }}
          >
            {accounts[0]?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
        }
      </IconButton>

      <CustomPopover open={popover.open} onClose={popover.onClose} sx={{ width: 300, p: 0 }}>
        <Box sx={{ p: 2, pb: 1.5 }}>
          <Typography variant="subtitle2" noWrap>
            {accounts[0]?.name}
          </Typography>

          <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
            {accounts[0]?.username}
          </Typography>
        </Box>

        <Divider sx={{ borderStyle: 'dashed' }} />
        {/* <MenuItem onClick={handleMyProfileClick} sx={{ m: 1, fontWeight: 'fontWeightBold' }}>
          {t('label.my_profile')}
        </MenuItem> */}
        <MenuItem onClick={handleLogout} sx={{ m: 1, fontWeight: 'fontWeightBold', color: 'error.main' }}>
          {t('label.logout')}
        </MenuItem>
      </CustomPopover>
    </>
  );
}
