<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 124.1 124.1">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #f5f5f5;
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }
    </style>
    <linearGradient id="linear-gradient" x1="62.04" y1="80.36" x2="62.04" y2="106.59" gradientTransform="translate(0 136.6) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".15" stop-color="#0f0f19" stop-opacity=".34"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".83"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="62.05" y1="73.42" x2="62.05" y2="42.51" gradientTransform="translate(0 136.6) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".54" stop-color="#0f0f19" stop-opacity=".89"/>
      <stop offset=".8" stop-color="#0f0f19" stop-opacity=".54"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <circle id="Ellipse_1" data-name="Ellipse 1" class="cls-2" cx="62.05" cy="62.05" r="62.05"/>
  <g>
    <path class="cls-1" d="M62.05,56.24c-7.24,0-13.12-5.87-13.12-13.11,0-7.24,5.87-13.12,13.11-13.12,7.24,0,13.12,5.87,13.12,13.11h0c0,7.24-5.87,13.11-13.11,13.12ZM62.05,33.05c-5.57,0-10.08,4.51-10.08,10.07,0,5.57,4.51,10.08,10.07,10.08,5.57,0,10.08-4.51,10.08-10.07h0c0-5.57-4.51-10.07-10.07-10.08Z"/>
    <path class="cls-3" d="M82.54,94.08h-3.04v-13.96c-.02-7.67-6.23-13.87-13.9-13.88h-7.12c-7.67,0-13.89,6.22-13.9,13.9v13.96h-3.04v-13.98c.01-9.35,7.59-16.92,16.94-16.94h7.12c9.35.01,16.92,7.59,16.94,16.94v13.96Z"/>
  </g>
</svg>