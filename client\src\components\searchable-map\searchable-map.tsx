import { OSM } from 'ol/source';
import { fromLonLat, toLonLat } from 'ol/proj';
//@ts-ignore
import {
  Map,
  View,
  PointerInteraction,
  TileLayer,
  VectorLayer,
  DragRotateAndZoomInteraction,
  FullScreenControl,
  getMarkerImage,
  //@ts-ignore
} from 'react-openlayers';
import 'react-openlayers/dist/index.css'; // for css
import { useEffect, useRef } from 'react';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { Box } from '@mui/system';

export default function SimpleMap({
  currentLatLng,
  setLatLng,
}: {
  currentLatLng: { lat: number; lng: number };
  setLatLng: (lat: number, lng: number) => void;
}) {
  const mapRef = useRef(null);

  let markerFeature = new Feature(new Point(fromLonLat([currentLatLng.lng, currentLatLng.lat])));
  console.log('🚀 ~ markerFeature:', markerFeature);

  useEffect(() => {
    if (mapRef.current) {
      let coords = fromLonLat([currentLatLng.lng, currentLatLng.lat]);
      var updatedPoint = new Point(coords);
      var feature = new Feature(updatedPoint);
      sourceRef.current.clear();
      sourceRef.current.addFeature(feature.clone());
      //@ts-ignore
      mapRef.current.getView().setCenter(coords);
      //@ts-ignore
      mapRef.current.render();
    }
  }, [currentLatLng]);
  const source = new VectorSource({
    features: [markerFeature],
  });
  const sourceRef = useRef(source);
  return (
    <Box sx={{ mt: 2 }}>
      <Map ref={mapRef}>
        <TileLayer source={new OSM()} />
        <VectorLayer
          source={sourceRef.current}
          style={{
            'icon-src': getMarkerImage(),
            'icon-opacity': 0.6,
            'icon-anchor': [0.5, 32],
            'icon-anchor-x-units': 'fraction',
            'icon-anchor-y-units': 'pixels',
            'stroke-width': 2,
            'stroke-color': [255, 0, 0, 1],
            'fill-color': [0, 0, 255, 0.5],
          }}
        ></VectorLayer>

        <View zoom={16} center={fromLonLat([currentLatLng.lng, currentLatLng.lat])} enableRotation={false} />
        <PointerInteraction
          handleDownEvent={(event: any) => {
            const [lon, lat] = toLonLat(event.coordinate);
            setLatLng(lat, lon);
          }}
        />
        <DragRotateAndZoomInteraction />
        <FullScreenControl />
      </Map>
    </Box>
  );
}
