import { Module } from '@nestjs/common';
import { AdminApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { PermissionController } from './controllers';
import { PermissionService } from './services';
import { SharedPermissionService } from 'src/shared/services';
import { DatabaseHelper } from 'src/shared/helpers';

const repositories = [
	
];

@Module({
	controllers: [PermissionController],
	providers: [
		...repositories, 
		PermissionService, 
		AdminApiClient,
		MSGraphApiClient,
		SharedPermissionService,
		HistoryApiClient,
		DatabaseHelper
	],
})
export class PermissionModule { }
