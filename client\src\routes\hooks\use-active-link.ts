import { matchPath, useLocation } from 'react-router-dom';
import { ROOTS } from '../paths';

// ----------------------------------------------------------------------

type ReturnType = boolean;

export function useActiveLink(path: string, deep = true): ReturnType {
  const { pathname } = useLocation();
  if (path == ROOTS.HOME) {
    return path == pathname;
  }
  const normalActive = path ? !!matchPath(pathname, path) : false;

  const deepActive = path ? !!matchPath({ path, end: false }, pathname) : false;

  return deep ? deepActive : normalActive;
}
