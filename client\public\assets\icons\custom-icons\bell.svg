<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 149.1 134.46">
  <defs>
    <style>
      .cls-1 {
        stroke: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3 {
        fill: none;
        stroke-width: 1.67px;
      }

      .cls-1, .cls-3 {
        stroke-miterlimit: 16.66;
      }

      .cls-2 {
        stroke: url(#linear-gradient-3);
        stroke-linecap: square;
        stroke-miterlimit: 6.66;
      }

      .cls-3 {
        stroke: url(#linear-gradient-2);
      }
    </style>
    <linearGradient id="linear-gradient" x1="-153.82" y1="-1840.39" x2="-66.77" y2="-1840.39" gradientTransform="translate(257.23 3174.96) scale(1.67)" gradientUnits="userSpaceOnUse">
      <stop offset=".8" stop-color="#13131b"/>
      <stop offset="1" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-94.69" y1="-1831.61" x2="-122.73" y2="-1831.61" gradientTransform="translate(257.23 3174.96) scale(1.67)" gradientUnits="userSpaceOnUse">
      <stop offset=".56" stop-color="#13131b"/>
      <stop offset="1" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-109.35" y1="-1902.54" x2="-108.99" y2="-1847.36" gradientTransform="translate(257.23 3174.96) scale(1.67)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#13131b"/>
      <stop offset=".71" stop-color="#13131b"/>
      <stop offset="1" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-1" d="M0,109.28h149.1"/>
  <path class="cls-3" d="M96.38,112.8c-.07,11.57-9.51,20.89-21.08,20.82-5.48-.03-10.73-2.21-14.62-6.07-3.93-3.9-6.13-9.21-6.12-14.75"/>
  <path class="cls-2" d="M21.46,101.4v-46.6C21.55,25.04,45.66.92,75.43.83c29.87-.05,54.13,24.1,54.22,53.97v46.18"/>
</svg>