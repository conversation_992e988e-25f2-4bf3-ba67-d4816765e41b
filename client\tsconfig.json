{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Paths Alias */
    "paths": {
      "@/assets/*": ["./src/assets/*"],
      "@/config/*": ["./src/config/*"],
      "@/modules/*": ["./src/modules/*"],
      "@/routes/*": ["./src/routes/*"],
      "@/core/*": ["./src/core/*"],
      // "@/_layouts/*": ["src/_layouts/*"],
      "@/layouts/*": ["./src/layouts/*"],
      "@/shared/*": ["./src/shared/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/theme/*": ["./src/theme/*"],
      "@/locales/*": ["./src/locales/*"],
      "@/components/*": ["./src/components/*"],
      "@/hooks/*": ["./src/hooks/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
