
import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HistoryService } from './history.service';
import { HISTORY_ENTITY_TYPE, PERMISSIONS } from 'src/shared/enums';
import { Permissions } from 'src/core/decorators';
import { AuthGuard } from '@nestjs/passport';
import { PermissionsGuard } from 'src/core/guards';
import { HistoryResponseDto } from './history-response.dto';

@ApiTags('History APIs')
@ApiBearerAuth()
@Controller('history')
export class HistoryController {

    constructor(
        private readonly historyService: HistoryService
    ) { }

    @Permissions(PERMISSIONS.APPLICATION_ADMIN)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get history logs.',
        type: [HistoryResponseDto]
    })
    @ApiParam({
        name: 'id',
        type: Number,
        description: 'Capability/Location/Entity ID',
        required: true,
    })
    @ApiParam({
        name: 'type',
        type: 'enum',
        enum: HISTORY_ENTITY_TYPE,
        description: 'Type of the history',
        required: true,
    })
    @Get(':type/:id')
    async getCompleteLocationDetailById(
        @Param('id') id: number,
        @Param('type') type: HISTORY_ENTITY_TYPE,
    ): Promise<HistoryResponseDto[]> {
        return this.historyService.getHistory(id, type);
    }
}