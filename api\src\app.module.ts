import { Logger, Modu<PERSON> } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { SequelizeModule } from '@nestjs/sequelize';
import { Dialect } from 'sequelize';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AttachmentModule } from './attachment/attachments.module';
import { AuthModule } from './auth/auth.module';
import { BusinessEntityModule } from './business-entity/business-entity.module';
import { ConfigModule } from './config/config.module';
import { ConfigService } from './config/config.service';
import { CoreModule } from './core/core.module';
import { HttpRequestInterceptor } from './core/interceptors';
import { LoggingInterceptor } from './core/interceptors/logger.interceptor';
import { DatabaseModule } from './database/database.module';
import { getSequelizeOrmConfig } from './database/orm-config';
import { GraphUserModule } from './graph-user/graph-user.module';
import { HistoryModule } from './history/history.module';
import { PermissionModule } from './permission/permission.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { SharedModule } from './shared/shared.module';

@Module({
	imports: [
		DatabaseModule,
		ConfigModule,
		CoreModule,
		AuthModule,
		SharedModule,
		BusinessEntityModule,
		PermissionModule,
		GraphUserModule,
		AttachmentModule,
		HistoryModule,
		SchedulerModule,
		SequelizeModule.forRootAsync({
			imports: [ConfigModule],
			useFactory: async (configService: ConfigService) => {
				const { database } = configService.getAppConfig();
				const { dialect, host, password, db, port, username, schema, enableSSL } = database;
				return {
					dialect: dialect as Dialect,
					host,
					port,
					database: db,
					username,
					password,
					schema,
					logging: false,
					...getSequelizeOrmConfig(enableSSL),
				};
			},
			inject: [ConfigService],
		}),
	],
	controllers: [AppController],
	providers: [
		AppService,
		Logger,
		{
			provide: APP_INTERCEPTOR,
			useClass: LoggingInterceptor,
		},
		{
			provide: APP_INTERCEPTOR,
			useClass: HttpRequestInterceptor,
		},
	],
})
export class AppModule {}
