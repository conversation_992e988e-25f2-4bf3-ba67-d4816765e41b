<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 91.6 102.71">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }
    </style>
    <linearGradient id="linear-gradient" x1="45.8" y1="19.36" x2="45.8" y2="35.38" gradientTransform="translate(0 122.07) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".15" stop-color="#0f0f19" stop-opacity=".34"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".83"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="45.8" y1="122.07" x2="45.8" y2="47.86" gradientTransform="translate(0 122.07) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".54" stop-color="#0f0f19" stop-opacity=".89"/>
      <stop offset=".8" stop-color="#0f0f19" stop-opacity=".54"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0" y1="38.08" x2="91.6" y2="38.08" gradientTransform="translate(0 122.07) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".25" stop-color="#0f0f19" stop-opacity=".6"/>
      <stop offset=".5" stop-color="#0f0f19"/>
      <stop offset=".72" stop-color="#0f0f19" stop-opacity=".7"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-1" d="M45.8,102.71c-8.84-.02-15.99-7.18-16.02-16.02h5.48c0,5.82,4.71,10.54,10.53,10.54,5.82,0,10.54-4.71,10.54-10.53h0s5.48,0,5.48,0c-.02,8.84-7.18,15.99-16.02,16.02Z"/>
  <path class="cls-2" d="M82.73,74.21h-5.48V30.52c-.02-13.82-11.22-25.02-25.04-25.04h-12.82c-13.82.02-25.02,11.22-25.04,25.04v43.69h-5.48V30.52C8.89,13.67,22.54.02,39.39,0h12.82c16.85.02,30.5,13.67,30.52,30.52v43.69Z"/>
  <rect class="cls-3" y="81.25" width="91.6" height="5.48"/>
</svg>