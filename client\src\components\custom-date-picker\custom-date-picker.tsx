import { Controller } from 'react-hook-form';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { FormControl } from '@mui/material';
import 'dayjs/locale/en-gb';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
// with date-fns v2.x
import enGB from 'date-fns/locale/en-GB';
interface ICustomDatePicker {
  name: string;
  control: any;
  label: string;
}
const CustomDatePicker = ({ name, control, label }: ICustomDatePicker) => {
  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState: { error } }) => (
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGB}>
            <DatePicker
              label={label}
              value={field.value ?? null}
              onChange={(date) => field.onChange(date)}
              slotProps={{
                textField: {
                  error: !!error, // Show error styling
                  helperText: error?.message, // Show validation error
                  InputLabelProps: {
                    shrink: true, // Keeps label floating by default
                    // sx: { color: error ? 'red' : 'default' }, // Label turns red on error
                  },
                },
              }}
              // sx={{
              //   '& .MuiOutlinedInput-root': {
              //     '& fieldset': {
              //       borderColor: error ? 'red' : 'default', // Ensures red border when error is active
              //     },
              //     '&:hover fieldset': {
              //       borderColor: error ? 'red' : 'default', // Prevents black border on hover when error exists
              //     },
              //     '&.Mui-focused fieldset': {
              //       borderColor: error ? 'red' : 'default', // Red when error, blue on valid focus
              //     },
              //   },
              //   '& .MuiFormHelperText-root': {
              //     color: error ? 'red !important' : 'default', // Explicitly set red color on error
              //     fontWeight: error ? 'bold' : 'normal',
              //   },
              //   '& .MuiInputLabel-root': {
              //     color: error ? 'red !important' : 'default', // Label turns red on error
              //   },
              //   '& .MuiInputBase-input': {
              //     color: error ? 'red' : 'default', // Placeholder and text turn red when error exists
              //   },
              // }}
            />
          </LocalizationProvider>
        )}
      />
    </FormControl>
  );
};

export default CustomDatePicker;
