<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 344.24 132.77">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: url(#linear-gradient-7);
      }

      .cls-6 {
        fill: url(#linear-gradient-6);
      }

      .cls-7 {
        fill: url(#linear-gradient);
      }

      .cls-8 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="65.49" y1="57.44" x2="65.49" y2="0" gradientTransform="translate(0 132.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="274.34" y1="57.44" x2="274.34" y2="0" gradientTransform="translate(0 132.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="280.01" y1="87.99" x2="70.76" y2="87.99" gradientTransform="translate(0 132.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".27" stop-color="#0f0f19"/>
      <stop offset=".59" stop-color="#0f0f19" stop-opacity=".88"/>
      <stop offset=".83" stop-color="#0f0f19" stop-opacity=".49"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="325" y1="23.64" x2="304.25" y2="23.64" gradientTransform="translate(0 132.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".09"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="14.9" y1="23.64" x2="34.19" y2="23.64" gradientTransform="translate(0 132.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="225.44" y1="128.34" x2="225.44" y2="85.34" gradientTransform="translate(0 132.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".27" stop-color="#0f0f19"/>
      <stop offset=".53" stop-color="#0f0f19" stop-opacity=".88"/>
      <stop offset=".72" stop-color="#0f0f19" stop-opacity=".49"/>
      <stop offset=".86" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="99.73" y1="23.64" x2="239.22" y2="23.64" gradientTransform="translate(0 132.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".56"/>
      <stop offset=".26" stop-color="#0f0f19" stop-opacity=".9"/>
      <stop offset=".5" stop-color="#0f0f19"/>
      <stop offset=".7" stop-color="#0f0f19" stop-opacity=".85"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".39"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-7" d="M65.49,132.77c-15.86,0-28.72-12.86-28.72-28.72,0-15.86,12.86-28.72,28.72-28.72,15.86,0,28.72,12.86,28.72,28.72-.02,15.85-12.87,28.7-28.72,28.72ZM65.49,84.2c-10.96,0-19.85,8.89-19.85,19.85s8.89,19.85,19.85,19.85,19.85-8.89,19.85-19.85h0c-.01-10.96-8.89-19.84-19.85-19.85Z"/>
  <path class="cls-1" d="M274.34,132.77c-15.86,0-28.72-12.86-28.72-28.72,0-15.86,12.86-28.72,28.72-28.72,15.86,0,28.72,12.86,28.72,28.72-.02,15.85-12.86,28.7-28.72,28.72ZM274.34,84.2c-10.96,0-19.85,8.89-19.85,19.85,0,10.96,8.89,19.85,19.85,19.85,10.96,0,19.85-8.89,19.85-19.85h0c-.01-10.96-8.89-19.84-19.85-19.85Z"/>
  <rect class="cls-3" x="70.76" y="40.34" width="209.25" height="8.87"/>
  <path class="cls-8" d="M327.84,113.57h-2.84v-8.87h2.84c4.15,0,7.52-3.37,7.52-7.52-.03-26.48-21.49-47.94-47.97-47.97h-7.39v-8.87h7.39c31.38.04,56.8,25.46,56.84,56.84-.01,9.05-7.34,16.38-16.39,16.39Z"/>
  <rect class="cls-2" x="304.25" y="104.7" width="20.75" height="8.87"/>
  <rect class="cls-4" x="14.9" y="104.7" width="19.28" height="8.87"/>
  <path class="cls-8" d="M14.9,113.57C6.67,113.57,0,106.89,0,98.66c0-.6.04-1.19.11-1.78l3.85-31.99c1.26-10.56,9.14-19.12,19.55-21.27l.29-.05c18.31-2.52,35.94-8.65,51.87-18.02C104.18,8.83,136.63,0,169.68,0c10.21,0,20.36,1.49,30.14,4.43l-2.55,8.49c-8.95-2.69-18.24-4.06-27.58-4.06-31.47,0-62.37,8.41-89.51,24.33-16.89,9.94-35.58,16.44-54.99,19.14-6.62,1.42-11.6,6.89-12.41,13.61l-3.85,31.99c-.4,3.31,1.96,6.31,5.27,6.71.24.03.48.04.72.04v8.87Z"/>
  <path class="cls-6" d="M246.5,47.42c-12.32-16.45-29.57-28.54-49.24-34.5l2.55-8.49c21.49,6.51,40.34,19.72,53.8,37.69l-7.11,5.3Z"/>
  <rect class="cls-5" x="99.73" y="104.7" width="139.49" height="8.87"/>
</svg>