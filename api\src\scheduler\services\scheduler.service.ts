import { Injectable } from '@nestjs/common';
import { LoggerService } from 'src/core/services';
import {
	thresholdDayLimit,
} from '../types/schedular.types';

@Injectable()
export class SchedulerService {
	constructor(
		private readonly loggerService: LoggerService,
	) { }


	public async runScheduler(type: string) {
		this.loggerService.log(`Started the ${type} scheduler.`);

		switch (type) {
			default:
				throw Error('>>> Invalid scheduler type. Type should be (ENTRIES_NOTIFICATION). <<<');
		}

		this.loggerService.log(`Ended the ${type} scheduler.`);
	}
}
