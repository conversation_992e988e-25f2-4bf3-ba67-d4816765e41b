<svg width="201" height="200" viewBox="0 0 201 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1587_120554)">
<g filter="url(#filter0_di_1587_120554)">
<rect x="24.0137" y="52" width="152" height="96" rx="12" fill="white"/>
</g>
<g filter="url(#filter1_di_1587_120554)">
<rect x="36.0137" y="86" width="82" height="10" rx="5" fill="#00A76F"/>
</g>
<rect opacity="0.16" x="36.0137" y="104" width="40" height="10" rx="5" fill="#007867"/>
<g filter="url(#filter2_di_1587_120554)">
<path d="M85.0137 114C87.7751 114 90.0137 111.761 90.0137 109C90.0137 106.239 87.7751 104 85.0137 104C82.2522 104 80.0137 106.239 80.0137 109C80.0137 111.761 82.2522 114 85.0137 114Z" fill="#FFAB00"/>
</g>
<g filter="url(#filter3_di_1587_120554)">
<path d="M99.0137 114C101.775 114 104.014 111.761 104.014 109C104.014 106.239 101.775 104 99.0137 104C96.2522 104 94.0137 106.239 94.0137 109C94.0137 111.761 96.2522 114 99.0137 114Z" fill="#FFAB00"/>
</g>
<g filter="url(#filter4_di_1587_120554)">
<path d="M113.014 114C115.775 114 118.014 111.761 118.014 109C118.014 106.239 115.775 104 113.014 104C110.252 104 108.014 106.239 108.014 109C108.014 111.761 110.252 114 113.014 114Z" fill="#FFAB00"/>
</g>
</g>
<defs>
<filter id="filter0_di_1587_120554" x="16.0137" y="44" width="184" height="128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.770709 0 0 0 0 0.792653 0 0 0 0 0.818587 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120554" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717499 0 0 0 0 0.740813 0 0 0 0 0.768367 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120554"/>
</filter>
<filter id="filter1_di_1587_120554" x="32.0137" y="82" width="98" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120554" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120554"/>
</filter>
<filter id="filter2_di_1587_120554" x="76.0137" y="100" width="26" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120554" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120554"/>
</filter>
<filter id="filter3_di_1587_120554" x="90.0137" y="100" width="26" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120554" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120554"/>
</filter>
<filter id="filter4_di_1587_120554" x="104.014" y="100" width="26" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120554" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120554"/>
</filter>
<clipPath id="clip0_1587_120554">
<rect width="200" height="200" fill="white" transform="translate(0.0136719)"/>
</clipPath>
</defs>
</svg>
