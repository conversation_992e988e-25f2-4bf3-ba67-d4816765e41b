<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 92.16 116">
  <defs>
    <style>
      .cls-1 {
        stroke: url(#linear-gradient-2);
        stroke-linecap: square;
        stroke-miterlimit: 6.66;
      }

      .cls-1, .cls-2 {
        fill: none;
        stroke-width: 1.67px;
      }

      .cls-2 {
        stroke: url(#linear-gradient);
        stroke-miterlimit: 16.66;
      }
    </style>
    <linearGradient id="linear-gradient" x1="-137.09" y1="-1906.47" x2="-137.09" y2="-1870.3" gradientTransform="translate(276.04 3180.29) scale(1.67)" gradientUnits="userSpaceOnUse">
      <stop offset=".79" stop-color="#13131b"/>
      <stop offset="1" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-138.05" y1="-1860.83" x2="-138.05" y2="-1840.5" gradientTransform="translate(276.04 3180.29) scale(1.67)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#13131b"/>
      <stop offset=".69" stop-color="#13131b"/>
      <stop offset="1" stop-color="#13131b" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-2" d="M47.67,62.5c17.03,0,30.83-13.81,30.83-30.84,0-17.03-13.81-30.83-30.84-30.83s-30.83,13.81-30.83,30.84c0,.11,0,.23,0,.34.19,16.89,13.94,30.49,30.83,30.49Z"/>
  <path class="cls-1" d="M.83,115.16c-.08-20.89,16.8-37.9,37.69-37.97.19,0,.37,0,.56,0h14.69c20.84.15,37.63,17.13,37.56,37.97"/>
</svg>