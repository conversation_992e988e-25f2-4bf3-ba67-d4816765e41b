import Iconify from '@/components/iconify';
import { useTranslate } from '@/locales/use-locales';
import { fToNow } from '@/shared/utils/format-time';
import { IconButton } from '@mui/material';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';

// ----------------------------------------------------------------------

type NotificationItemProps = {
  notification: {
    id: number;
    title: string;
    category: string;
    createdAt: string;
    isUnRead: boolean;
    type: string;
    avatarUrl: string | null;
  };
  handleClearNotification: (id: number) => void;
};
export default function NotificationItem({ notification, handleClearNotification }: NotificationItemProps) {
  const { t } = useTranslate();
  const renderText = (
    <ListItemText
      disableTypography
      primary={reader(notification.title)}
      secondary={
        <Stack
          direction="row"
          alignItems="center"
          sx={{ typography: 'caption', color: 'text.disabled' }}
          divider={
            <Box
              sx={{
                width: 2,
                height: 2,
                bgcolor: 'currentColor',
                mx: 0.5,
                borderRadius: '50%',
              }}
            />
          }
        >
          {fToNow(notification.createdAt)}
          {notification.category}
        </Stack>
      }
    />
  );

  // const renderUnReadBadge = notification.isUnRead && (
  //   <Box
  //     sx={{
  //       top: 26,
  //       width: 8,
  //       height: 8,
  //       right: 20,
  //       borderRadius: '50%',
  //       bgcolor: 'info.main',
  //       position: 'absolute',
  //     }}
  //   />
  // );

  const renderCloseButton = (
    <Box
      sx={{
        top: 10,
        width: 8,
        height: 8,
        right: 35,
        position: 'absolute',
      }}
    >
      <IconButton onClick={() => handleClearNotification(notification.id)}>
        <Iconify icon="mingcute:close-line" />
      </IconButton>
    </Box>
  );

  const friendAction = (
    <Stack spacing={1} direction="row" sx={{ mt: 1.5 }}>
      <Button size="small" variant="contained">
        {t('btn_name.accept')}
      </Button>
      <Button size="small" variant="outlined">
        {t('btn_name.decline')}
      </Button>
    </Stack>
  );

  return (
    <ListItemButton
      disableRipple
      sx={{
        p: 2.5,
        alignItems: 'flex-start',
        borderBottom: (theme) => `dashed 1px ${theme.palette.divider}`,
      }}
    >
      {/* {renderUnReadBadge} */}
      {renderCloseButton}

      <Stack sx={{ flexGrow: 1 }}>
        {renderText}
        {notification.type === 'friend' && friendAction}
      </Stack>
    </ListItemButton>
  );
}

// ----------------------------------------------------------------------

function reader(data: string) {
  return (
    <Box
      dangerouslySetInnerHTML={{ __html: data }}
      sx={{
        mb: 0.5,
        '& p': { typography: 'body2', m: 0 },
        '& a': { color: 'inherit', textDecoration: 'none' },
        '& strong': { typography: 'subtitle2' },
      }}
    />
  );
}
