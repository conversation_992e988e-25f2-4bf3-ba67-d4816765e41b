<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 279.7 201.8">
  <!-- Generator: Adobe Illustrator 29.5.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 141)  -->
  <defs>
    <style>
      .st0 {
        fill: url(#linear-gradient2);
      }

      .st1 {
        fill: url(#linear-gradient10);
      }

      .st2 {
        fill: url(#linear-gradient1);
      }

      .st3 {
        fill: url(#linear-gradient9);
      }

      .st4 {
        fill: url(#linear-gradient8);
      }

      .st5 {
        fill: url(#linear-gradient7);
      }

      .st6 {
        fill: url(#linear-gradient4);
      }

      .st7 {
        fill: url(#linear-gradient5);
      }

      .st8 {
        fill: url(#linear-gradient3);
      }

      .st9 {
        fill: url(#linear-gradient6);
      }

      .st10 {
        fill: url(#linear-gradient);
      }

      .st11 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="-1119.8" y1="136.7" x2="-1119.8" y2="87.3" gradientTransform="translate(-985.2 173.5) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset=".9" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient1" x1="-1015.4" y1="37.9" x2="-1076.2" y2="37.9" gradientTransform="translate(-985.2 173.5) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset=".9" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient2" x1="-1057.6" y1="166.7" x2="-1057.6" y2="33.4" gradientTransform="translate(-985.2 173.5) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset=".3" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient3" x1="-1110.8" y1="21.5" x2="-1134.5" y2="47" gradientTransform="translate(-683.2 -632.4) rotate(-135)" gradientUnits="userSpaceOnUse">
      <stop offset=".3" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient4" x1="-1171.7" y1="103.5" x2="-1224.5" y2="103.5" gradientTransform="translate(-985.2 173.5) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".5" stop-color="#0f0f19" stop-opacity=".6"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient5" x1="-1029.3" y1="12.9" x2="-1090" y2="12.9" gradientTransform="translate(-985.2 173.5) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset=".3" stop-color="#0f0f19"/>
      <stop offset=".7" stop-color="#0f0f19" stop-opacity=".6"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient6" x1="-1127.4" y1="-15.9" x2="-1223.8" y2="39.8" gradientTransform="translate(-985.2 173.5) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset=".3" stop-color="#0f0f19"/>
      <stop offset=".7" stop-color="#0f0f19" stop-opacity=".6"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient7" x1="-1224.2" y1="70.1" x2="-1257.8" y2="70.1" gradientTransform="translate(-985.2 173.5) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset=".3" stop-color="#0f0f19"/>
      <stop offset=".7" stop-color="#0f0f19" stop-opacity=".6"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient8" x1="2150.1" y1="17812" x2="2176.4" y2="17812" gradientTransform="translate(2197.6 -17727.8) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset=".5" stop-color="#0f0f19" stop-opacity=".8"/>
      <stop offset=".9" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient9" x1="1433.5" y1="7284.9" x2="1459.8" y2="7284.9" gradientTransform="translate(-7221.8 -1362.1) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset=".5" stop-color="#0f0f19" stop-opacity=".8"/>
      <stop offset=".9" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient10" x1="5198.1" y1="5447.1" x2="5248.6" y2="5447.1" gradientTransform="translate(-5397.4 -5127.2) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset=".5" stop-color="#0f0f19" stop-opacity=".8"/>
      <stop offset=".9" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <rect class="st10" x="130.2" y="36.7" width="8.9" height="49.4"/>
  <rect class="st2" x="30.3" y="131.2" width="60.8" height="8.9"/>
  <polygon class="st0" points="30.3 140 5.8 140 5.8 6.7 139.1 6.7 139.1 36.7 130.2 36.7 130.2 15.6 14.6 15.6 14.6 131.2 30.3 131.2 30.3 140"/>
  <rect class="st8" x="122.7" y="135.9" width="34.5" height="8.9" transform="translate(-58.2 140.1) rotate(-45)"/>
  <path class="st11" d="M115.2,163.6c-10.3,0-18.6-8.4-18.6-18.6,0-4.9,2-9.7,5.4-13.1l57.3-57.3c5.8-5.8,13.6-9,21.7-9h5.5v8.9h-5.5c-5.8,0-11.4,2.3-15.5,6.4l-57.3,57.3c-3.8,3.8-3.8,10,0,13.8s10,3.8,13.8,0h0l2.5-2.5,6.3,6.3-2.5,2.5c-3.5,3.5-8.2,5.5-13.2,5.5Z"/>
  <rect class="st6" x="186.6" y="65.5" width="52.7" height="8.9"/>
  <rect class="st7" x="44.1" y="156.1" width="60.7" height="8.9"/>
  <path class="st11" d="M143.3,191H39.8c-9.7-.2-17.3-8.2-17.1-17.9.2-9.3,7.7-16.8,17.1-17.1h4.2v8.9h-4.2c-4.8.2-8.5,4.1-8.3,8.9.2,4.5,3.8,8.2,8.3,8.3h103.4v8.9Z"/>
  <polygon class="st9" points="150.5 191 143.3 191 143.3 182.2 148.1 182.2 237 130.9 241.4 138.6 150.5 191"/>
  <polygon class="st5" points="272.6 149.2 239 149.2 239 57.4 272.6 57.4 272.6 66.3 247.9 66.3 247.9 140.4 272.6 140.4 272.6 149.2"/>
  <polygon class="st11" points="50.7 78.3 49.4 77.1 48.3 78.2 42 72 49.4 64.5 56.9 72 50.7 78.3"/>
  <rect class="st4" x="23" y="79.8" width="26.3" height="8.9" transform="translate(-49 50.3) rotate(-45.1)"/>
  <rect class="st3" x="58.7" y="71.3" width="8.9" height="26.3" transform="translate(-41.3 69.5) rotate(-45.1)"/>
  <rect class="st1" x="45.2" y="70.8" width="8.9" height="50.5"/>
</svg>