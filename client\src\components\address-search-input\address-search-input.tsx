import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { PlacesResponse, searchLocationAddress } from '@/shared/services';
import { Stack } from '@mui/system';
import { useTranslation } from 'react-i18next';
import { debounce } from 'lodash';
import { Search } from '@mui/icons-material';

type Prop = {
  onSelect: (lat: number, long: number) => void;
  placeholder?: string;
};

export default function AddressSearchInput({ onSelect, placeholder }: Prop) {
  const { t } = useTranslation();
  const [value, setValue] = useState<PlacesResponse | null>(null);
  const [searchText, setSearchText] = useState('');
  const [options, setOptions] = useState<PlacesResponse[]>([]);
  const { isFetching, refetch } = useQuery(['locations', searchText], () => searchLocationAddress(searchText), {
    enabled: !!searchText,
    onSuccess(data) {
      setOptions(data ?? []);
    },
  });

  const debouncedSetSearchText = useMemo(() => debounce(setSearchText, 500), []);

  useEffect(() => {
    if (searchText.length) {
      refetch();
    }
  }, [refetch, searchText]);

  return (
    <Autocomplete
      loading={isFetching}
      fullWidth
      id="adUserSearch"
      getOptionLabel={(option) => option.display_name}
      filterOptions={(x) => x}
      options={options ?? []}
      isOptionEqualToValue={(option, value) => option.place_id === value?.place_id}
      autoComplete
      includeInputInList
      value={value}
      noOptionsText={t('empty_state_messages.no_location_found')}
      onInputChange={(_event, value) => {
        debouncedSetSearchText(value);
      }}
      popupIcon={<Search sx={{ width: 24 }} />}
      sx={{
        '& .MuiAutocomplete-popupIndicator': {
          transform: 'none !important', // disable any transform
        },
        '&.Mui-focused .MuiAutocomplete-popupIndicator': {
          transform: 'none !important', // also when focused
        },
        '&[aria-expanded="true"] .MuiAutocomplete-popupIndicator': {
          transform: 'none !important', // when open
        },
      }}
      onChange={(_, newValue: any | null) => {
        setValue(newValue);
        if (newValue) {
          onSelect(+newValue.lat, +newValue.lon);
        }
      }}
      renderInput={(params) => {
        return (
          <TextField
            {...params}
            label={<span>{placeholder || t('label.search_location')}</span>}
            slotProps={{
              inputLabel: {
                shrink: true,
              },
            }}
            fullWidth
          />
        );
      }}
      renderOption={(props, option) => {
        return (
          <li {...props} key={option.place_id}>
            <Grid container alignItems="center">
              <Grid sx={{ wordWrap: 'break-word' }}>
                <Stack flexDirection={'column'}>
                  <Typography variant="subtitle1" color="text.secondary" fontSize={14}>
                    {option.display_name}
                  </Typography>
                </Stack>
              </Grid>
            </Grid>
          </li>
        );
      }}
    />
  );
}
