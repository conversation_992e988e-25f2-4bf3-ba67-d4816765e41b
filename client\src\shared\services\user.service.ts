import * as http from '@/shared/services';
import { API_PATHS } from '../constants';
import { GraphUsersResponse, UserPermissionsResponse } from '../models';

export const searchGraphUsers = (searchText: string, orderBy: string): Promise<GraphUsersResponse | null> => {
  return http.get<GraphUsersResponse>(API_PATHS.ACTIVE_DIRECTORY.SEARCH_GRAPH_USER, {
    params: { searchText, orderBy, count: true },
  });
};

export const getUserDetails = (userId: string): Promise<GraphUsersResponse | null> => {
  return http.get<GraphUsersResponse>(API_PATHS.ACTIVE_DIRECTORY.GET_USER_DETAILS, {
    params: { userId },
  });
};

export const getUserPermissions = (): Promise<UserPermissionsResponse[]> => {
  return http.get<UserPermissionsResponse[]>(API_PATHS.APPLICATION_CONFIGURATION.USER_PERMISSIONS);
};
