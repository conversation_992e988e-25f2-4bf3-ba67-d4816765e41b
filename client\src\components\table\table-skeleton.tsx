import Stack from '@mui/material/Stack';
import Skeleton from '@mui/material/Skeleton';
import TableCell from '@mui/material/TableCell';
import TableRow, { TableRowProps } from '@mui/material/TableRow';
import { Table, TableBody } from '@mui/material';

// ----------------------------------------------------------------------

export function TableRowSkeleton({ ...other }: TableRowProps & { count: number; spacing: number }) {
  return (
    <TableRow {...other}>
      <TableCell colSpan={12}>
        <Stack spacing={other.spacing} direction="row" alignItems="center">
          {[...Array(other.count)].map((_, index) => (
            <Skeleton key={index} sx={{ height: 12, flex: 1 }} />
          ))}
        </Stack>
      </TableCell>
    </TableRow>
  );
}

export default function TableSkeleton({
  rowCount,
  colCount,
  colSpacing,
}: {
  rowCount: number;
  colCount: number;
  colSpacing: number;
}) {
  return (
    <Stack direction="column" spacing={3} alignItems={{ xs: 'flex-end', md: 'center' }}>
      <Table>
        <TableBody>
          {[...Array(rowCount)].map((_, index) => (
            <TableRowSkeleton key={index} count={colCount} spacing={colSpacing}></TableRowSkeleton>
          ))}
        </TableBody>
      </Table>
    </Stack>
  );
}
