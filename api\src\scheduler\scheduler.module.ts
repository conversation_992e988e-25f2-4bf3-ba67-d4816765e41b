import { Module } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import {
	AdminApiClient,
	HistoryApiClient,
	MSGraphApiClient,
	NotificationApiClient,
} from 'src/shared/clients';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedNotificationService, SharedPermissionService } from 'src/shared/services';
import { SchedulerService } from './services';

const repositories = [];

@Module({
	controllers: [],
	providers: [
		...repositories,
		SchedulerService,
		AdminApiClient,
		MSGraphApiClient,
		SharedPermissionService,
		SharedNotificationService,
		HistoryApiClient,
		NotificationApiClient,
		DatabaseHelper,
		ConfigService,
	],
})
export class SchedulerModule {}
