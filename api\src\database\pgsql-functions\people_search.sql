-- FUNCTION: dev_cdb.people_search_v2(text)

-- DROP FUNCTION IF EXISTS dev_cdb.people_search_v2(text);

CREATE OR REPLACE FUNCTION dev_cdb.people_search_v2(
	loginid text)
    RETURNS SETOF jsonb 
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
DECLARE
    loginIdLower TEXT := LOWER(loginId);
BEGIN
    SET search_path TO dev_cdb, public;

    RETURN QUERY
    WITH

    -- CONTACT role
    contact AS (
        SELECT jsonb_build_object(
            'type', 'GEO_CONTACT',
            'details', jsonb_build_object(
                'object_id', cd.id,
                'object_type', cd.object_type,
                'entityId', cd.entity_id,
                'entityTitle', cd.entity_title,
                'entityCode', cd.entity_code,
                'coreSolution', cs.title
               
            )
        ) AS entry
        FROM data_contact_details cd
        LEFT JOIN meta_core_solutions cs ON cd.object_id = cs.id
        CROSS JOIN LATERAL jsonb_array_elements(cd.user_details) elem
        WHERE LOWER(elem->>'loginId') = loginIdLower AND cd.location_id IS NULL
    ),

    -- INDUSTRY_EXPERT
    industry AS (
        SELECT jsonb_build_object(
            'type', 'INDUSTRY_EXPERT',
            'details', jsonb_build_object(
                'id', liv.id,
                'locationId', liv.location_id,
                'verticals', v.title,
                'locationName', loc.location_name
            )
        ) AS entry
        FROM data_location_industry_verticals liv
        LEFT JOIN meta_common_dropdowns v ON liv.industry_vertical_code = v.code
        LEFT JOIN data_locations loc ON liv.location_id = loc.id
        CROSS JOIN LATERAL jsonb_array_elements(liv.expert_users) elem
        WHERE LOWER(elem->>'loginId') = loginIdLower
    ),

    -- CAPABILITY_OWNER
    cap_owner AS (
        SELECT jsonb_build_object(
            'type', 'CAPABILITY_OWNER',
            'details', jsonb_build_object(
                'id', lwcd.id,
                'locationId', lwcd.location_id,
				 'locationName', loc.location_name,
                 'capabilityName', mc.capability,
                 'product', mc.product,
			     'subCategory', mc.subcategory,
			     'category', mcc.title
                
            )
        ) AS entry
        FROM data_location_wise_capability_details lwcd
        LEFT JOIN master_capability_hierarchy_mappings mc ON lwcd.capability_id = mc.id
		LEFT JOIN meta_capability_categories mcc ON mcc.id = mc.category_id
        LEFT JOIN data_locations loc ON lwcd.location_id = loc.id
        WHERE LOWER(lwcd.other_details->'owners'->>'loginId') = loginIdLower
    ),

    -- CAPABILITY_COOWNER
    cap_coowner AS (
        SELECT jsonb_build_object(
            'type', 'CAPABILITY_COOWNER',
            'details', jsonb_build_object(
                'id', lwcd.id,
                'locationId', lwcd.location_id,
				 'locationName', loc.location_name,
                 'capabilityName', mc.capability,
                 'product', mc.product,
			     'subCategory', mc.subcategory,
			     'category', mcc.title
                
            )
        ) AS entry
        FROM data_location_wise_capability_details lwcd
        LEFT JOIN master_capability_hierarchy_mappings mc ON lwcd.capability_id = mc.id
		LEFT JOIN meta_capability_categories mcc ON mcc.id = mc.category_id
        LEFT JOIN data_locations loc ON lwcd.location_id = loc.id
        CROSS JOIN LATERAL jsonb_array_elements(lwcd.other_details->'coOwners') co
        WHERE LOWER(co->>'loginId') = loginIdLower
    ),

    -- SUSTAINABILITY_EXPERT
    sustainability AS (
        SELECT jsonb_build_object(
            'type', 'SUSTAINABILITY_EXPERT',
            'details', jsonb_build_object(
                'id', loc.id,
                'locationName', loc.location_name
            )
        ) AS entry
        FROM data_locations loc
        CROSS JOIN LATERAL jsonb_array_elements(loc.other_details->'competencyDetails'->'sustainabilityExperts') se
        WHERE LOWER(se->>'loginId') = loginIdLower
    ),

    -- LOCATION_CONTACT
    location_contact AS (
        SELECT jsonb_build_object(
            'type', 'LOCATION_CONTACT',
            'details', jsonb_build_object(
                'locationId', loc.id,
                'locationName', loc.location_name,
                'entityTitle', loc.entity_title,
                'coreSolution', cs.title,
				'contactType',  ct.title
            )
        ) AS entry
        FROM data_locations loc
        JOIN data_contact_details cd ON cd.location_id = loc.id
		LEFT JOIN meta_common_dropdowns ct ON cd.object_id = ct.id
        JOIN meta_core_solutions cs ON loc.core_solution_id = cs.id
        CROSS JOIN LATERAL jsonb_array_elements(cd.user_details) ud
        WHERE LOWER(ud->>'loginId') = loginIdLower AND cd.location_id IS NOT NULL
    ),

    -- USER_PERMISSION
    user_permission AS (
        SELECT jsonb_build_object(
            'type', 'USER_PERMISSION',
            'details', jsonb_build_object(
                'id', up.id,
                'locationId', up.location_id,
				'groupName', ac.group_name,
				'locationName', loc.location_name
            )
        ) AS entry
        FROM map_user_permissions up
		LEFT JOIN meta_access_control_config ac ON up.config_group_id = ac.id
		LEFT JOIN data_locations loc ON up.location_id = loc.id
        WHERE LOWER(up.login_id) = loginIdLower
    )

    SELECT entry
    FROM (
        SELECT * FROM contact
        UNION ALL SELECT * FROM industry
        UNION ALL SELECT * FROM cap_owner
        UNION ALL SELECT * FROM cap_coowner
        UNION ALL SELECT * FROM sustainability
        UNION ALL SELECT * FROM location_contact
        UNION ALL SELECT * FROM user_permission
    ) all_entries;

END;
$BODY$;

ALTER FUNCTION dev_cdb.people_search_v2(text)
    OWNER TO goeuser;
