import { Injectable } from "@nestjs/common";
import * as Excel from 'exceljs';
import { CAPABILITY_LEVEL_ENUM_DISPLAY, CAPABILITY_STATUS_ENUM_DISPLAY, CAPABILITY_TYPE_ENUM_DISPLAY, GOOGLE_LISTING_STATUS_DISPLAY_ENUM, LEASE_OWNERSHIP_STATUS_TYPE_DISPLAY, LOCATION_STATUS_TYPE_DISPLAY, PROVIDER_ENUM_DISPLAY } from "../enums";

@Injectable()
export class ExcelSheetService {
    constructor() { }


    public async createLocationSheet(locationList: any[], sheetName: string) {
        const workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('Locations');

        const columns = [
            'Name',
            'Introduction',
            'Capability',
            'Type',
            'Address',
            'Status',
            'Status Date',
            'Legal Entity',
            'Lease Ownership Status',
            'Lease Expiration Date',
            'Strategic Classification',
            'Branch Archetype',
            'Brands',
            'Industry Verticals',
            'Is Google Listing?',
            'Google Listing Status',
            'Google Listing Link',
        ];
        columns.forEach((column, index) => {
            const cell = worksheet.getCell(1, index + 1);

            cell.value = column;
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '120553' },
                bgColor: { argb: '120553' },
            };
            cell.font = {
                color: { argb: 'FFFFFF' },
                bold: true,

            };

            cell.alignment = {
                horizontal: 'center',
                vertical: 'middle',
                wrapText: true,
            };

            worksheet.getColumn(index + 1).width = column.length + 5;

        });

        locationList.forEach((locationDetail, index) => {
            worksheet.getCell((index + 2), 1).value = locationDetail.locationName;
            worksheet.getCell((index + 2), 2).value = locationDetail?.otherDetails?.basicInformation?.introduction || '';
            worksheet.getCell((index + 2), 3).value = locationDetail.coreSolution.title;
            worksheet.getCell((index + 2), 4).value = locationDetail.locationType.title;
            worksheet.getCell((index + 2), 5).value = locationDetail?.otherDetails?.basicInformation?.address || '';
            worksheet.getCell((index + 2), 6).value = locationDetail?.status ? LOCATION_STATUS_TYPE_DISPLAY[locationDetail.status] : '';
            worksheet.getCell((index + 2), 7).value = locationDetail?.statusDate || '';
            worksheet.getCell((index + 2), 8).value = locationDetail?.legalEntity?.name || '';
            worksheet.getCell((index + 2), 9).value = locationDetail?.leaseOwnershipStatus ? LEASE_OWNERSHIP_STATUS_TYPE_DISPLAY[locationDetail.leaseOwnershipStatus] : '';
            worksheet.getCell((index + 2), 10).value = locationDetail?.otherDetails?.basicInformation?.leaseExpirationDate ? new Date(locationDetail.otherDetails.basicInformation.leaseExpirationDate).toISOString().split('T')[0] : '';
            worksheet.getCell((index + 2), 11).value = locationDetail?.strategicClassification?.title || '';
            worksheet.getCell((index + 2), 12).value = locationDetail?.branchArchetype?.title || '';
            worksheet.getCell((index + 2), 13).value = locationDetail?.brands?.length ? locationDetail?.brands?.map((brand) => brand.title).join(', ') : '';
            worksheet.getCell((index + 2), 14).value = locationDetail?.locationIndustryVerticals?.length ? locationDetail?.locationIndustryVerticals?.map((vertical) => vertical.industryVertical.title).join(', ') : '';
            worksheet.getCell((index + 2), 15).value = !!locationDetail?.googleListing?.isGoogleListing;
            worksheet.getCell((index + 2), 16).value = locationDetail?.googleListing?.status ? GOOGLE_LISTING_STATUS_DISPLAY_ENUM[locationDetail.googleListing.status] : '';
            worksheet.getCell((index + 2), 17).value = locationDetail?.googleListing?.link || '';
        });

        //Adjust the column width.
        worksheet.columns.forEach((column, index) => {
            let maxLength = 0;
            column['eachCell']({ includeEmpty: true }, (cell) => {
                // Set border of each cell 
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
                const columnLength = cell.value ? cell.value.toString().length : 5;
                if (columnLength > maxLength) {
                    maxLength = columnLength;
                }

            });
            column.width = maxLength < 5 ? 20 : (maxLength > 60 ? 60 : maxLength);
            column.alignment = {
                wrapText: true
            }
        });

        // Enable filtering for all columns
        // worksheet.autoFilter = {
        //     from: { row: 1, column: 1 },
        //     to: { row: 1, column: columns.length }
        // };

        return workbook.xlsx.writeBuffer()

    }

    public async createCapabilitySheet(capabilityList: any[], sheetName: string) {
        const workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('Entries');

        const columns = [
            'Capability',
            'Location Name',
            'Product Family',
            'Product',
            'Entry Name',
            'Category',
            'Description',
            'Entry Type',
            'Level',
            'Status',
            'Status Date',
            'Owner Email',
            'Provider',
            'Provider Name',
            'Verticals',
        ];
        columns.forEach((column, index) => {
            const cell = worksheet.getCell(1, index + 1);

            cell.value = column;
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '120553' },
                bgColor: { argb: '120553' },
            };
            cell.font = {
                color: { argb: 'FFFFFF' },
                bold: true,

            };

            cell.alignment = {
                horizontal: 'center',
                vertical: 'middle',
                wrapText: true,
            };

            worksheet.getColumn(index + 1).width = column.length + 5;

        });

        capabilityList.forEach((capabilityDetail, index) => {
            worksheet.getCell((index + 2), 1).value = capabilityDetail?.locationDetail?.coreSolution?.title || '';
            worksheet.getCell((index + 2), 2).value = capabilityDetail?.locationDetail?.locationName || '';
            worksheet.getCell((index + 2), 3).value = capabilityDetail?.capabilityDetail?.product || '';
            worksheet.getCell((index + 2), 4).value = capabilityDetail?.capabilityDetail?.subCategory || '';
            worksheet.getCell((index + 2), 5).value = capabilityDetail?.capabilityDetail?.capability || '';
            worksheet.getCell((index + 2), 6).value = capabilityDetail?.capabilityDetail?.category.title || '';
            worksheet.getCell((index + 2), 7).value = capabilityDetail?.otherDetails?.description || '';
            worksheet.getCell((index + 2), 8).value = capabilityDetail?.capabilityDetail?.capabilityType ? CAPABILITY_TYPE_ENUM_DISPLAY[capabilityDetail.capabilityDetail.capabilityType] : '';
            worksheet.getCell((index + 2), 9).value = capabilityDetail?.capabilityDetail?.level ? CAPABILITY_LEVEL_ENUM_DISPLAY[capabilityDetail.capabilityDetail.level] : '';
            worksheet.getCell((index + 2), 10).value = capabilityDetail?.status ? CAPABILITY_STATUS_ENUM_DISPLAY[capabilityDetail.status] : '';
            worksheet.getCell((index + 2), 11).value = capabilityDetail?.statusDate || '';
            worksheet.getCell((index + 2), 12).value = capabilityDetail.otherDetails?.owners?.email || capabilityDetail.otherDetails?.owners?.loginId || '';
            worksheet.getCell((index + 2), 13).value = capabilityDetail?.provider ? PROVIDER_ENUM_DISPLAY[capabilityDetail.provider] : '';
            worksheet.getCell((index + 2), 14).value = capabilityDetail?.providerName || '';
            worksheet.getCell((index + 2), 15).value = capabilityDetail?.capabilityDetail?.verticals?.length ? capabilityDetail.capabilityDetail.verticals.join(', ') : '';
        });

        //Adjust the column width.
        worksheet.columns.forEach((column, index) => {
            let maxLength = 20;
            column['eachCell']({ includeEmpty: true }, (cell) => {
                // Set border of each cell 
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
                const columnLength = cell.value ? cell.value.toString().length : 5;
                if (columnLength > maxLength) {
                    maxLength = columnLength;
                }

            });
            column.width = maxLength < 5 ? 20 : (maxLength > 60 ? 60 : maxLength);
            column.alignment = {
                wrapText: true
            }
        });

        // Enable filtering for all columns
        // worksheet.autoFilter = {
        //     from: { row: 1, column: 1 },
        //     to: { row: 1, column: columns.length }
        // };

        return workbook.xlsx.writeBuffer()

    }

    public async createLocationWiseCapabilitiesSheet(capabilityList: any[], sheetName: string) {
        const workbook = new Excel.Workbook();
        let worksheet = workbook.addWorksheet('Entries');

        const columns = [
            'Product Family',
            'Product',
            'Entry Name',
            'Category',
            'Description',
            'Entry Type',
            'Level',
            'Status',
            'Status Date',
            'Owner Email',
            'Provider',
            'Provider Name',
            'Verticals',
        ];
        columns.forEach((column, index) => {
            const cell = worksheet.getCell(1, index + 1);

            cell.value = column;
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '120553' },
                bgColor: { argb: '120553' },
            };
            cell.font = {
                color: { argb: 'FFFFFF' },
                bold: true,

            };

            cell.alignment = {
                horizontal: 'center',
                vertical: 'middle',
                wrapText: true,
            };

            worksheet.getColumn(index + 1).width = column.length + 5;

        });

        capabilityList.forEach((capabilityDetail, index) => {

            worksheet.getCell((index + 2), 1).value = capabilityDetail?.capabilityDetail?.product || '';
            worksheet.getCell((index + 2), 2).value = capabilityDetail?.capabilityDetail?.subCategory || '';
            worksheet.getCell((index + 2), 3).value = capabilityDetail?.capabilityDetail?.capability || '';
            worksheet.getCell((index + 2), 4).value = capabilityDetail?.capabilityDetail?.category.title || '';
            worksheet.getCell((index + 2), 5).value = capabilityDetail?.otherDetails?.description || '';
            worksheet.getCell((index + 2), 6).value = capabilityDetail?.capabilityDetail?.capabilityType ? CAPABILITY_TYPE_ENUM_DISPLAY[capabilityDetail.capabilityDetail.capabilityType] : '';
            worksheet.getCell((index + 2), 7).value = capabilityDetail?.capabilityDetail?.level ? CAPABILITY_LEVEL_ENUM_DISPLAY[capabilityDetail.capabilityDetail.level] : '';
            worksheet.getCell((index + 2), 8).value = capabilityDetail?.status ? CAPABILITY_STATUS_ENUM_DISPLAY[capabilityDetail.status] : '';
            worksheet.getCell((index + 2), 9).value = capabilityDetail?.statusDate || '';
            worksheet.getCell((index + 2), 10).value = capabilityDetail.otherDetails?.owners?.email || capabilityDetail.otherDetails?.owners?.loginId || '';
            worksheet.getCell((index + 2), 11).value = capabilityDetail?.provider ? PROVIDER_ENUM_DISPLAY[capabilityDetail.provider] : '';
            worksheet.getCell((index + 2), 12).value = capabilityDetail?.providerName || '';
            worksheet.getCell((index + 2), 13).value = capabilityDetail?.capabilityDetail?.verticals?.length ? capabilityDetail.capabilityDetail.verticals.join(', ') : '';
        });

        //Adjust the column width.
        worksheet.columns.forEach((column, index) => {
            let maxLength = 20;
            column['eachCell']({ includeEmpty: true }, (cell) => {
                // Set border of each cell 
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
                const columnLength = cell.value ? cell.value.toString().length : 5;
                if (columnLength > maxLength) {
                    maxLength = columnLength;
                }

            });
            column.width = maxLength < 5 ? 20 : (maxLength > 60 ? 60 : maxLength);
            column.alignment = {
                wrapText: true
            }
        });

        // Enable filtering for all columns
        // worksheet.autoFilter = {
        //     from: { row: 1, column: 1 },
        //     to: { row: 1, column: columns.length }
        // };

        return workbook.xlsx.writeBuffer()

    }
}