import { memo } from 'react';
import Box, { BoxProps } from '@mui/material/Box';
import BackgroundShape from './background-shape';

// ----------------------------------------------------------------------

function TreeViewIllustration({ sx, ...other }: BoxProps) {
  return (
    <Box
      component="svg"
      width="100%"
      height="100%"
      viewBox="0 0 480 360"
      
      sx={sx}
      {...other}
    >
      <BackgroundShape />

      <image href="assets/illustrations/characters/character_4.png" height="300" x="220" y="60" />

      {/* Parent Node */}
      <rect x="220" y="90" width="40" height="24" rx="4" fill="currentColor" />
      <text x="230" y="106" fontSize="10" fill="#212B36"></text>

      {/* Lines to children */}
      <line x1="240" y1="114" x2="190" y2="150" stroke="currentColor" strokeWidth="2" />
      <line x1="240" y1="114" x2="290" y2="150" stroke="currentColor" strokeWidth="2" />

      {/* Child Nodes */}
      <rect x="170" y="150" width="40" height="24" rx="4" fill="currentColor" />
      <rect x="270" y="150" width="40" height="24" rx="4" fill="currentColor" />
      <text x="178" y="166" fontSize="10" fill="#212B36"></text>
      <text x="278" y="166" fontSize="10" fill="#212B36"></text>

      {/* Lines to grandchildren */}
      <line x1="190" y1="174" x2="160" y2="210" stroke="currentColor" strokeWidth="2" />
      <line x1="210" y1="174" x2="240" y2="210" stroke="currentColor" strokeWidth="2" />
      <line x1="290" y1="174" x2="260" y2="210" stroke="currentColor" strokeWidth="2" />
      <line x1="310" y1="174" x2="340" y2="210" stroke="currentColor" strokeWidth="2" />

      {/* Leaf Nodes */}
      <rect x="145" y="210" width="30" height="20" rx="4" fill="currentColor" />
      <text x="151" y="224" fontSize="8" fill="#212B36"></text>

      <rect x="225" y="210" width="30" height="20" rx="4" fill="currentColor" />
      <text x="231" y="224" fontSize="8" fill="#212B36"></text>

      <rect x="245" y="210" width="30" height="20" rx="4" fill="currentColor" />
      <text x="251" y="224" fontSize="8" fill="#212B36"></text>

      <rect x="325" y="210" width="30" height="20" rx="4" fill="currentColor" />
      <text x="331" y="224" fontSize="8" fill="#212B36"></text>
    </Box>
  );
}

export default memo(TreeViewIllustration);
