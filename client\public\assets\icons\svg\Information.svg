<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_3" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 211.7 213.1">
  <!-- Generator: Adobe Illustrator 29.5.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 141)  -->
  <defs>
    <style>
      .st0 {
        fill: url(#linear-gradient2);
      }

      .st1 {
        fill: url(#linear-gradient1);
      }

      .st2 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="105.6" y1="238.3" x2="105.6" y2="32.1" gradientTransform="translate(0 241.3) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".4" stop-color="#0f0f19" stop-opacity=".9"/>
      <stop offset=".8" stop-color="#0f0f19" stop-opacity=".4"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient1" x1="105.6" y1="108.4" x2="105.6" y2="194.1" gradientTransform="translate(0 241.3) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".4" stop-color="#0f0f19" stop-opacity=".7"/>
      <stop offset=".9" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient2" x1="105.6" y1="72.2" x2="105.6" y2="90.6" gradientTransform="translate(0 241.3) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".4" stop-color="#0f0f19" stop-opacity=".7"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="st2" d="M105.6,209.2c-56.9,0-103.1-46.2-103.1-103.1S48.7,3,105.6,3s103.1,46.2,103.1,103.1h0c0,56.9-46.2,103-103.1,103.1ZM105.6,11.9c-52,0-94.2,42.2-94.2,94.2,0,52,42.2,94.2,94.2,94.2,52,0,94.2-42.2,94.2-94.2,0-52-42.2-94.2-94.2-94.2h0Z"/>
  <rect class="st1" x="101.2" y="47.2" width="8.9" height="85.7"/>
  <rect class="st0" x="101.2" y="150.7" width="8.9" height="18.4"/>
</svg>