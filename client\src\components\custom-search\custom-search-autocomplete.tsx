import React from 'react';
import { Autocomplete, TextField, Box, Typography, CircularProgress } from '@mui/material';
import { getIcon } from '@/shared/utils/get-icon';
import { CustomAutocompleteSearchProps } from './models/searchModel';
import { useTranslate } from '@/locales/use-locales';

const CustomAutocompleteSearch: React.FC<CustomAutocompleteSearchProps> = ({
  searchResults,
  handleSearch,
  handleSelect,
  searching,
  inputValue,
  openDropdown,
  setOpenDropdown,
  placeholder = "",
  statusColors,
  maxHeight = '300px',
  noResultsText = 'label.no_results_found',
  loadingText = 'label.searching',
  iconSize = 35,
  chipLabelStyle = {
    borderRadius: 3,
    fontSize: { xs: 10, sm: 12 },
    fontWeight: 'bold',
    color: '#fff',
    height: { xs: 18, sm: 20 },
  },
  textFieldStyle = {
    background: '#fff',
    width: { xs: '90%', sm: 500, md: 700 }, // Prevents excessive shrinking
    minWidth: 300, // Ensures it doesn't get too small
    borderRadius: '50px',
    '& .MuiOutlinedInput-root': {
      borderRadius: '50px',
      paddingLeft: '25px',
      paddingY: '17px',
    },
  },
  paperStyle = {
    width: { xs: '90%', sm: 500, md: 700 },
    minWidth: 300, // Ensures dropdown width matches input field
    borderRadius: 2,
    boxShadow: 3,
    p: 1,
  },
  listItemStyle = { p: 1.5, boxShadow: 1 },
  showChips = true,
  showDescription = true,
  noResultsTextStyle = { textAlign: 'center', p: 2, color: 'text.secondary' },
  getLabelKey,
  loadingContainer = { display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 },
  CustomDropdownComponent = null,
}) => {
  const { t } = useTranslate();
  return (
    <Autocomplete
      freeSolo
      options={searchResults}
      getOptionLabel={(option: any) => option[getLabelKey]} // Dynamically get the label
      onInputChange={(_, newValue) => handleSearch(newValue)}
      inputValue={inputValue}
      open={inputValue ? openDropdown : false}
      onOpen={() => setOpenDropdown(true)}
      onClose={() => setOpenDropdown(false)}
      onChange={(_, value) => handleSelect(value as any)}
      disableClearable
      renderInput={(params) => (
        <TextField {...params} placeholder={t(placeholder)} variant="outlined" fullWidth sx={{ ...textFieldStyle }} />
      )}
      slots={{
        paper: (props) => (
          <Box {...props} sx={{ maxHeight, ...paperStyle }}>
            {searching ? (
              <Box sx={{ ...loadingContainer }}>
                <CircularProgress size={24} />
                {loadingText && <Typography sx={{ marginLeft: 1 }}>{t(loadingText)}</Typography>}
              </Box>
            ) : inputValue && searchResults.length === 0 ? (
              <Typography sx={{ ...noResultsTextStyle }}>{t(noResultsText)}</Typography>
            ) : (
              props.children
            )}
          </Box>
        ),
      }}
      renderOption={(props, option: any) => (
        // Custom Dropdown Component can be Rendered Here
        <CustomDropdownComponent
          props={props}
          option={option}
          key={option.id}
          showDescription={showDescription}
          getIconPath={getIcon}
          iconSize={iconSize}
          showChips={showChips}
          statusColors={statusColors}
          listItemStyle={listItemStyle}
          chipLabelStyle={chipLabelStyle}
        />
      )}
    />
  );
};

export default CustomAutocompleteSearch;
