import { DropzoneOptions } from 'react-dropzone';

import { Theme, SxProps } from '@mui/material/styles';
import { Attachment } from '@/shared/models/location.model';

// ----------------------------------------------------------------------

export interface CustomFile extends File {
  path?: string;
  preview?: string;
  lastModifiedDate?: Date;
}

export interface UploadProps extends DropzoneOptions {
  error?: boolean;
  sx?: SxProps<Theme>;
  thumbnail?: boolean;
  placeholder?: React.ReactNode;
  helperText?: React.ReactNode;
  disableMultiple?: boolean;
  //
  file?: CustomFile | string | null;
  onDelete?: VoidFunction;
  //
  files?: (File | string | Attachment)[];
  onUpload?: VoidFunction;
  onRemove?: (file: CustomFile | Attachment | string) => void;
  onRemoveAll?: VoidFunction;
  onDownload?: (file: CustomFile | Attachment | string) => void;
  uploadFilesTitle: string;
  previewFilesTitle: string;
  emptyContentText?: string;
  trimTextNumber?: number;
}
