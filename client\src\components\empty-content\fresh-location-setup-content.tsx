import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
import { varBounce, MotionContainer } from '@/components/animate';
import { useTranslation } from 'react-i18next';
import Button from '@mui/material/Button';
import { useContext } from 'react';
import { LocationDetailContext, UserPermissionsContext } from '@/core/contexts';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { checkAnyPermission } from '@/shared/utils';
import { toNumber } from 'lodash';
import EmptyContent from './empty-content';
import { HIERARCHY_ENTITY_TYPE } from '@/shared/enum';

export default function FreshCountrySetup({ onClick }: { onClick: () => void }) {
  const { t } = useTranslation();
  const currentLocation = useContext(LocationDetailContext);
  const userPermissions = useContext(UserPermissionsContext);
  const hasPermission = checkAnyPermission([PERMISSIONS.GLOBAL_MANAGE], userPermissions, toNumber(currentLocation.id));
  return (
    <MotionContainer
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
      }}
    >
      {!hasPermission && <EmptyContent title={t('messages.country_detail_not_found')}></EmptyContent>}
      {hasPermission && (
        <>
          <m.div variants={varBounce().in}>
            <Typography variant="h3" sx={{ mb: 2 }}>
              {t('messages.setup_country')}
            </Typography>
          </m.div>

          <m.div variants={varBounce().in}>
            <Button
              type="submit"
              size="large"
              variant="contained"
              onClick={() => {
                onClick();
              }}
            >
              {t('btn_name.setup_country_now')}
            </Button>
          </m.div>
        </>
      )}
    </MotionContainer>
  );
}
