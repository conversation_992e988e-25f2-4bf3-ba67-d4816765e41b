export enum CAPABILITY_TYPE_ENUM {
	OPERATIONAL_AND_CONTRACTUAL = 'OPERATIONAL_AND_CONTRACTUAL',
	SYSTEM_AND_TECHNOLOGY = 'SYSTEM_AND_TECHNOLOGY',
	CERTIFICATE_AND_LICENCE = 'CERTIFICATE_AND_LICENCE',
}

export enum CAPABILITY_TYPE_ENUM_DISPLAY {
	OPERATIONAL_AND_CONTRACTUAL = 'Operational & Contractual',
	SYSTEM_AND_TECHNOLOGY = 'System & Technology',
	CERTIFICATE_AND_LICENCE = 'Certificate & Licence',
}

export enum CAPABILITY_LEVEL_ENUM {
	BASIC = 'BASIC',
	ENHANCED = 'ENHANCED',
	PREMIUM = 'PREMIUM',
	MARKET_LEADING = 'MARKET_LEADING',
}

export enum CAPABILITY_LEVEL_ENUM_DISPLAY {
	BASIC = 'Basic',
	ENHANCED = 'Enhanced',
	PREMIUM = 'Premium',
	MARKET_LEADING = 'Market Leading',
}

export enum CAPABILITY_STATUS_ENUM {
	RETIRED = 'RETIRED',
	PLANNED = 'PLANNED',
	EXPIRED = 'EXPIRED',
	EXISTING = 'EXISTING',
}

export enum INTERNAL_CAPABILITY_STATUS_ENUM {
	RETIRED = 'RETIRED',
	PLANNED = 'PLANNED',
	EXPIRED = 'EXPIRED',
	EXISTING = 'EXISTING',
}

export enum CAPABILITY_STATUS_ENUM_DISPLAY {
	RETIRED = 'Retired',
	PLANNED = 'Planned',
	EXPIRED = 'Expired',
	EXISTING = 'Existing',
}

export enum PROVIDER_ENUM {
	DPW = 'DPW',
	THIRD_PARTY = 'THIRD_PARTY',
	CUSTOMER = 'CUSTOMER',
}

export enum PROVIDER_ENUM_DISPLAY {
	DPW = 'DPW',
	THIRD_PARTY = 'Third Party',
	CUSTOMER = 'Customer',
}

export enum SYSTEM_AND_TECHNOLOGY_ENUM {
	DPWORLD = 'DPWORLD',
	EXTERNAL = 'EXTERNAL',
	CUSTOMER = 'CUSTOMER',
}

export enum SYSTEM_AND_TECHNOLOGY_ENUM_DISPLAY {
	DPWORLD = 'DP World',
	EXTERNAL = 'External',
	CUSTOMER = 'Customer',
}

export enum MASTER_CAPABILITY_DROPDOWN_TYPES {
	PRODUCT = 'PRODUCT',
	'SUB-CATEGORY' = 'SUB-CATEGORY',
	CATEGORY = 'CATEGORY',
	LEGS = 'LEGS',
}

export enum CAPABILITY_DROPDOWN_LEVEL_ENUM {
	PRODUCT = 'PRODUCT',
	'SUB-CATEGORY' = 'SUB-CATEGORY',
}
