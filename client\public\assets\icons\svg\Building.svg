<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 284.4 199.7">
  <!-- Generator: Adobe Illustrator 29.4.0, SVG Export Plug-In . SVG Version: 2.1.0 Build 152)  -->
  <defs>
    <style>
      .st0 {
        fill: url(#linear-gradient2);
      }

      .st1 {
        fill: url(#linear-gradient10);
      }

      .st2 {
        fill: url(#linear-gradient1);
      }

      .st3 {
        fill: url(#linear-gradient9);
      }

      .st4 {
        fill: url(#linear-gradient8);
      }

      .st5 {
        fill: url(#linear-gradient11);
      }

      .st6 {
        fill: url(#linear-gradient12);
      }

      .st7 {
        fill: url(#linear-gradient7);
      }

      .st8 {
        fill: url(#linear-gradient4);
      }

      .st9 {
        fill: url(#linear-gradient14);
      }

      .st10 {
        fill: url(#linear-gradient5);
      }

      .st11 {
        fill: url(#linear-gradient3);
      }

      .st12 {
        fill: url(#linear-gradient6);
      }

      .st13 {
        fill: url(#linear-gradient13);
      }

      .st14 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="-1027" y1="-922.6" x2="-983.3" y2="-922.6" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient1" x1="-1079.6" y1="-922.6" x2="-1035.9" y2="-922.6" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient2" x1="-1027" y1="-865.2" x2="-983.3" y2="-865.2" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient3" x1="-1022.6" y1="-869.8" x2="-1022.6" y2="-898.3" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient4" x1="-1079.6" y1="-865.2" x2="-1035.9" y2="-865.2" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient5" x1="-1075.2" y1="-869.8" x2="-1075.2" y2="-898.3" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient6" x1="-929.1" y1="-974.3" x2="-876.6" y2="-974.3" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient7" x1="-929.1" y1="-916.9" x2="-876.6" y2="-916.9" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient8" x1="-924.7" y1="-921.5" x2="-924.7" y2="-950" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient9" x1="-929.1" y1="-875.5" x2="-876.6" y2="-875.5" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".2" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient10" x1="-924.7" y1="-880.1" x2="-924.7" y2="-908.7" gradientTransform="translate(-840.2 1037.2) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient11" x1="63" y1="4.8" x2="62.4" y2="140.9" gradientTransform="translate(0 201.2) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="0" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".6" stop-color="#0f0f19" stop-opacity=".8"/>
      <stop offset="1" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient12" x1="270.2" y1="3.7" x2="270.2" y2="131" gradientTransform="translate(0 201.2) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="0" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".6" stop-color="#0f0f19" stop-opacity=".8"/>
      <stop offset="1" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient13" x1="284.3" y1="137.7" x2="119" y2="137.7" gradientTransform="translate(0 201.2) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="0" stop-color="#0f0f19" stop-opacity=".2"/>
      <stop offset=".5" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient14" x1="0" y1="194.5" x2="125.2" y2="194.5" gradientTransform="translate(0 201.2) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="0" stop-color="#0f0f19" stop-opacity=".2"/>
      <stop offset=".5" stop-color="#0f0f19"/>
    </linearGradient>
  </defs>
  <polygon class="st14" points="186.8 135.6 143 135.6 143 126.8 177.9 126.8 177.9 102.4 143 102.4 143 93.5 186.8 93.5 186.8 135.6"/>
  <polygon class="st2" points="239.4 135.6 195.7 135.6 195.7 126.8 230.5 126.8 230.5 102.4 195.7 102.4 195.7 93.5 239.4 93.5 239.4 135.6"/>
  <polygon class="st0" points="186.8 176.6 143 176.6 143 167.7 177.9 167.7 177.9 167.4 186.8 167.4 186.8 176.6"/>
  <rect class="st11" x="177.9" y="138.9" width="8.9" height="28.5"/>
  <polygon class="st8" points="239.4 176.6 195.7 176.6 195.7 167.7 230.5 167.7 230.5 167.4 239.4 167.4 239.4 176.6"/>
  <rect class="st10" x="230.5" y="138.9" width="8.9" height="28.5"/>
  <polygon class="st12" points="88.9 83.9 36.4 83.9 36.4 75.1 80 75.1 80 50.7 36.4 50.7 36.4 41.8 88.9 41.8 88.9 83.9"/>
  <polygon class="st7" points="88.9 124.9 36.4 124.9 36.4 116 80 116 80 115.7 88.9 115.7 88.9 124.9"/>
  <rect class="st4" x="80" y="87.2" width="8.9" height="28.5"/>
  <polygon class="st3" points="88.9 166.2 36.4 166.2 36.4 157.4 80 157.4 80 157.1 88.9 157.1 88.9 166.2"/>
  <rect class="st1" x="80" y="128.5" width="8.9" height="28.5"/>
  <path class="st5" d="M8.8,196.9h8.8V11.2s-8.8,0-8.8,0v185.7ZM107.6,11.2v185.7h8.8V11.2h-8.8Z"/>
  <path class="st6" d="M265.8,68v128.9h8.8V68h-8.8Z"/>
  <rect class="st13" x="112" y="59.1" width="171.3" height="8.8"/>
  <rect class="st9" y="2.3" width="125.2" height="8.8"/>
</svg>