import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
import { varBounce, MotionContainer } from '@/components/animate';
import { useTranslation } from 'react-i18next';
import Button from '@mui/material/Button';

export default function FreshCountrySetup({ onClick }: { onClick: () => void }) {
  const { t } = useTranslation();
  return (
    <MotionContainer
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
      }}
    >
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          {t('messages.setup_country')}
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Button
          type="submit"
          size="large"
          variant="contained"
          onClick={() => {
            onClick();
          }}
        >
          {t('btn_name.setup_now')}
        </Button>
      </m.div>
    </MotionContainer>
  );
}
