import { ReactNode, useState } from 'react';
import { Backdrop, Box, CircularProgress, Typography } from '@mui/material';
import { LoadingContext } from '../contexts';

interface Props {
  children?: ReactNode;
}

const LoadingProvider = ({ children }: Props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<string>(''); 

  const value = { loading, setLoading, message, setMessage };
  return (
    <LoadingContext.Provider value={value}>
      {children}
      <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.modal + 1 }} open={loading}>
        <Box textAlign="center">
          <CircularProgress color="primary" />
          <Typography variant="h6" sx={{ mt: 2 }}>
            {message}
          </Typography>
        </Box>
      </Backdrop>
    </LoadingContext.Provider>
  );
};

export default LoadingProvider;
