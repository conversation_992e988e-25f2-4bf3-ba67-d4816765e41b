
import { Injectable } from '@nestjs/common';
import { HISTORY_ENTITY_TYPE } from 'src/shared/enums';
import { HistoryResponseDto } from './history-response.dto';
import { HistoryApiClient } from 'src/shared/clients';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class HistoryService {

    constructor(
        private readonly historyApiClient: HistoryApiClient,
    ) { }

    async getHistory(id: number, type: HISTORY_ENTITY_TYPE): Promise<HistoryResponseDto[]> {
        const historyList = await this.historyApiClient.getRequestHistory(id, type);
        return plainToInstance(HistoryResponseDto, historyList, {
            excludeExtraneousValues: true,
        });
    }
}