import { useTranslate } from '@/locales/use-locales';
import { getAllLocations } from '@/shared/services';
import { Autocomplete, CircularProgress, Typography, TextField, debounce } from '@mui/material';
import { Box, Grid } from '@mui/system';
import { useState, useMemo, useEffect } from 'react';
import { useQuery } from 'react-query';
import { RHFAUtoCompleteErrorStyling } from '../hook-form/rhf-autocomplete';
import { LocationBasicDetailResponse } from '@/shared/models';

export default function LocationSearchInput({
  validationError,
  onSelect,
}: {
  validationError: string;
  onSelect: (locations: LocationBasicDetailResponse[]) => void;
}) {
  const [searchText, setSearchText] = useState('');
  const [value, setValue] = useState<any>(null);
  const {
    data: locationData,
    isFetching,
    refetch,
  } = useQuery(['location-search', searchText], () => getAllLocations(searchText), {
    enabled: !!searchText,
  });
  const debouncedSetSearchText = useMemo(() => debounce(setSearchText, 500), []);
  useEffect(() => {
    if (searchText.length) {
      refetch();
    }
  }, [refetch, searchText]);
  const { t } = useTranslate();
  return (
    <Autocomplete
      size="medium"
      fullWidth
      disableClearable
      autoComplete
      value={value}
      slots={{
        paper: (props) => (
          <Box {...props} sx={{ borderRadius: 2, boxShadow: 3, p: 1 }}>
            {isFetching ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ marginLeft: 1 }}>{t('label.searching')}</Typography>
              </Box>
            ) : searchText && locationData?.length === 0 ? (
              <Typography sx={{ textAlign: 'center', p: 2, color: 'text.secondary' }}>
                {t('empty_state_messages.no_location_found')}
              </Typography>
            ) : (
              props.children
            )}
          </Box>
        ),
      }}
      includeInputInList
      filterOptions={(x) => x}
      options={
        locationData?.length
          ? locationData?.map((item: any) => ({
              label: item.locationName,
              value: item.id,
              option: item,
            }))
          : []
      }
      isOptionEqualToValue={(option, value) => option.value === value?.value}
      noOptionsText={
        <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
          {searchText ? t('empty_state_messages.no_location_found') : t('messages.type_something_to_search')}
        </Typography>
      }
      //   value={getValues('location') || null}
      onChange={(_, newValue) => {
        setValue(newValue);
        onSelect(newValue.option);
      }}
      onInputChange={(_, value) => {
        debouncedSetSearchText(value);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={
            <span>
              {t('label.location')}
              <span> *</span>
            </span>
          }
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
          error={Boolean(validationError)}
          fullWidth
          sx={RHFAUtoCompleteErrorStyling(Boolean(validationError))}
        />
      )}
      renderOption={(props, option) => (
        <li {...props} key={option?.value}>
          <Grid container alignItems="center">
            <Grid>
              <Typography variant="value">{option.label}</Typography>
            </Grid>
          </Grid>
        </li>
      )}
    />
  );
}
