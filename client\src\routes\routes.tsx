import { lazy, Suspense } from 'react';
import { Outlet, useRoutes } from 'react-router-dom';

import { LoadingScreen } from '../components/loading-screen';
import DashboardLayout from '../layouts/dashboard';

// Paths
import HomePage from '@/modules/home/<USER>/home';
import { paths } from './paths';

const PageNotFound = lazy(() => import('@/components/error/not-found-view'));

export default function Router() {
  return useRoutes([
    {
      path: paths.home.root,
      element: (
        <DashboardLayout>
          <Suspense fallback={<LoadingScreen />}>
            <Outlet />
          </Suspense>
        </DashboardLayout>
      ),
      children: [
        { path: paths.home.root, index: true, element: <HomePage /> },
        { path: '*', element: <PageNotFound /> },
      ],
    },
    { path: '/*', element: <PageNotFound /> },
  ]);
}
