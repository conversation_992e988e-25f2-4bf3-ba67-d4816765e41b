<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 425.55 230.77">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-15);
      }

      .cls-2 {
        fill: url(#linear-gradient-13);
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }

      .cls-4 {
        fill: url(#linear-gradient-10);
      }

      .cls-5 {
        fill: url(#linear-gradient-12);
      }

      .cls-6 {
        fill: url(#linear-gradient-4);
      }

      .cls-7 {
        fill: url(#linear-gradient-3);
      }

      .cls-8 {
        fill: url(#linear-gradient-5);
      }

      .cls-9 {
        fill: url(#linear-gradient-8);
      }

      .cls-10 {
        fill: url(#linear-gradient-14);
      }

      .cls-11 {
        fill: url(#linear-gradient-17);
      }

      .cls-12 {
        fill: url(#linear-gradient-7);
      }

      .cls-13 {
        fill: url(#linear-gradient-9);
      }

      .cls-14 {
        fill: url(#linear-gradient-11);
      }

      .cls-15 {
        fill: url(#linear-gradient-6);
      }

      .cls-16 {
        fill: url(#linear-gradient-16);
      }

      .cls-17 {
        fill: url(#linear-gradient);
      }

      .cls-18 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="377.09" y1="166.57" x2="336.98" y2="166.57" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="413.67" y1="28.81" x2="363.43" y2="28.81" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="416.68" y1="77.6" x2="398.94" y2="77.6" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="416.68" y1="59.84" x2="398.94" y2="59.84" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="363.43" y1="125.58" x2="363.43" y2="152.99" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="363.45" y1="28.7" x2="310.04" y2="28.7" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="150.6" y1="28.7" x2="97.2" y2="28.7" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="97.37" y1="28.7" x2="43.97" y2="28.7" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="159.48" y1="28.81" x2="301.37" y2="28.81" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".03" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".31" stop-color="#0f0f19" stop-opacity=".87"/>
      <stop offset=".35" stop-color="#0f0f19"/>
      <stop offset=".65" stop-color="#0f0f19"/>
      <stop offset=".97" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="310.7" y1="228.34" x2="4.44" y2="228.34" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="332.53" y1="206.5" x2="332.53" y2="73.05" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="319.12" y1="73.1" x2="0" y2="73.1" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".31" stop-color="#0f0f19" stop-opacity=".87"/>
      <stop offset=".35" stop-color="#0f0f19"/>
      <stop offset=".65" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="270.31" y1="206.42" x2="270.31" y2="82.04" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="217.08" y1="206.42" x2="217.08" y2="82.04" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="163.85" y1="206.42" x2="163.85" y2="82.04" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="110.61" y1="206.42" x2="110.61" y2="82.04" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="57.38" y1="206.42" x2="57.38" y2="82.04" gradientTransform="translate(0 232.77) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".36" stop-color="#0f0f19"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <rect class="cls-17" x="336.98" y="61.77" width="40.11" height="8.87"/>
  <polygon class="cls-18" points="425.55 208.4 413.67 208.4 413.67 199.53 416.68 199.53 416.68 143.13 378.5 70.64 377.09 70.64 377.09 61.77 383.86 61.77 425.55 140.94 425.55 208.4"/>
  <rect class="cls-3" x="363.43" y="199.53" width="50.24" height="8.87"/>
  <rect class="cls-7" x="398.94" y="150.74" width="17.74" height="8.87"/>
  <rect class="cls-6" x="398.94" y="168.5" width="17.74" height="8.87"/>
  <rect class="cls-8" x="359" y="79.78" width="8.87" height="27.42"/>
  <path class="cls-18" d="M412.25,133.21h-27.23c-14.36-.02-26-11.65-26.02-26.02h8.87c.01,9.47,7.68,17.14,17.15,17.15h27.23v8.87Z"/>
  <path class="cls-15" d="M336.75,230.77c-14.75,0-26.7-11.96-26.7-26.7,0-14.75,11.96-26.7,26.7-26.7,14.75,0,26.7,11.96,26.7,26.7h0c-.02,14.74-11.96,26.69-26.7,26.7ZM336.75,186.24c-9.85,0-17.83,7.98-17.83,17.83,0,9.85,7.98,17.83,17.83,17.83,9.85,0,17.83-7.98,17.83-17.83h0c-.01-9.84-7.99-17.82-17.83-17.83h0Z"/>
  <path class="cls-12" d="M123.9,230.77c-14.75,0-26.7-11.96-26.7-26.7,0-14.75,11.96-26.7,26.7-26.7,14.75,0,26.7,11.96,26.7,26.7h0c-.02,14.74-11.96,26.69-26.7,26.7ZM123.9,186.24c-9.85,0-17.83,7.98-17.83,17.83,0,9.85,7.98,17.83,17.83,17.83,9.85,0,17.83-7.98,17.83-17.83h0c-.01-9.84-7.99-17.82-17.83-17.83h0Z"/>
  <path class="cls-9" d="M70.67,230.77c-14.75,0-26.7-11.96-26.7-26.7,0-14.75,11.96-26.7,26.7-26.7,14.75,0,26.7,11.96,26.7,26.7h0c-.02,14.74-11.96,26.69-26.7,26.7ZM70.67,186.24c-9.85,0-17.83,7.98-17.83,17.83,0,9.85,7.98,17.83,17.83,17.83,9.85,0,17.83-7.98,17.83-17.83h0c-.01-9.84-7.99-17.82-17.83-17.83h0Z"/>
  <rect class="cls-13" x="159.48" y="199.53" width="141.88" height="8.87"/>
  <rect class="cls-4" x="4.44" width="306.26" height="8.87"/>
  <path class="cls-18" d="M336.97,26.27h-8.87c-.01-9.61-7.79-17.39-17.4-17.4V0c14.5.02,26.25,11.77,26.27,26.27Z"/>
  <rect class="cls-14" x="328.1" y="26.27" width="8.87" height="133.45"/>
  <path class="cls-5" d="M319.12,177.37H25.96c-14.33-.02-25.94-11.63-25.96-25.96v-9.42h8.87v9.42c.01,9.43,7.66,17.08,17.09,17.09h293.16v8.87Z"/>
  <rect class="cls-2" x="265.87" y="26.35" width="8.87" height="124.38"/>
  <rect class="cls-10" x="212.64" y="26.35" width="8.87" height="124.38"/>
  <rect class="cls-1" x="159.41" y="26.35" width="8.87" height="124.38"/>
  <rect class="cls-16" x="106.18" y="26.35" width="8.87" height="124.38"/>
  <rect class="cls-11" x="52.95" y="26.35" width="8.87" height="124.38"/>
</svg>