import { Injectable } from '@nestjs/common';
import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { HttpStatus } from 'src/shared/enums';
import { HttpException } from 'src/shared/exceptions';
import { singleObjectToInstance } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { AttachmentContentResponseDto } from '../dtos';

@Injectable()
export class AttachmentService {
	constructor(
		private readonly attachmentApiClient: AttachmentApiClient,
	) { }

	/**
	 * Get content by id
	 * @returns  GetAttachmentContentResponseDto
	 */
	public async getContentByFileId(fileId: string, currentContext: CurrentContext, taskId?: number): Promise<AttachmentContentResponseDto> {
		const fileContent = await this.attachmentApiClient.getContentByFileId(fileId);
		const { username } = currentContext.user;

		const { meta_data_1: afeProposalId, entity_id: entityId, entity_type: entityType } = fileContent;

		// const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(Number(afeProposalId));
		// if (!proposal) {
		// 	throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		// }
		// const hasUserPermission = await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
		// if (!hasUserPermission) {
		// 	throw new HttpException(`You are not authorized to view this file id content.`, HttpStatus.FORBIDDEN);
		// }

		if (!fileContent) {
			throw new HttpException(`Content not available for file id ${fileId}`, HttpStatus.NOT_FOUND);
		}
		return singleObjectToInstance(AttachmentContentResponseDto, fileContent);
	}
}
