{"name": "client", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": "22.17.0"}, "scripts": {"dev": "vite --force --base=/SCTS/", "build": "cross-env NODE_ENV=production vite build --mode prod --base=/SCTS/", "lint": "eslint . --ext .ts,.tsx", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^3.1.0", "@azure/msal-react": "^2.0.3", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/timeline": "^6.1.15", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^4.1.0", "@iconify/react": "^5.2.0", "@mui/icons-material": "^6.4.8", "@mui/lab": "^6.0.0-beta.28", "@mui/material": "^6.4.5", "@mui/styles": "^6.4.5", "@mui/system": "^6.4.3", "@mui/x-data-grid": "^7.27.0", "@mui/x-date-pickers": "^7.29.4", "@mui/x-tree-view": "^7.26.0", "@react-pdf/renderer": "^4.2.2", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-query": "^5.66.7", "@tanstack/react-query-devtools": "^5.66.7", "@types/leaflet": "^1.9.17", "apexcharts": "^4.4.0", "autosuggest-highlight": "^3.3.4", "aws-amplify": "^6.13.0", "axios": "^1.7.9", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "firebase": "^11.3.1", "formik": "^2.4.6", "framer-motion": "^12.4.4", "highlight.js": "^11.11.1", "history": "^5.3.0", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.3", "lodash": "^4.17.21", "mapbox-gl": "^3.10.0", "mui-one-time-password-input": "^3.0.2", "notistack": "^3.0.2", "nprogress": "^0.2.0", "ol": "^10.5.0", "path": "^0.12.7", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-geolocated": "^4.3.0", "react-google-autocomplete": "^2.7.5", "react-helmet-async": "^2.0.5", "react-hierarchy-chart": "^1.0.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-joyride": "^2.9.3", "react-lazy-load-image-component": "^1.6.3", "react-map-gl": "^8.0.1", "react-markdown": "^9.0.3", "react-openlayers": "^10.2.0", "react-organizational-chart": "^2.2.1", "react-query": "^3.39.3", "react-quill-new": "^3.4.6", "react-router": "^7.2.0", "react-router-dom": "^7.2.0", "react-slick": "^0.30.3", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "simplebar-react": "^3.3.0", "slick-carousel": "^1.8.1", "stylis": "^4.3.6", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.3.2", "yet-another-react-lightbox": "^3.21.7", "yup": "^1.6.1"}, "devDependencies": {"@azure/msal-common": "^14.7.0", "@types/autosuggest-highlight": "^3.2.3", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.15", "@types/node": "^22.13.4", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-lazy-load-image-component": "^1.6.4", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "postcss": "^8.5.2", "prettier": "^3.5.1", "sass": "^1.85.0", "tailwindcss": "^4.0.7", "typescript": "^5.7.3", "vite": "^6.1.1"}}