import { Theme } from '@mui/material/styles';
import { LoadingButtonProps } from '@mui/lab/LoadingButton';
import { buttonClasses } from '@mui/material/Button';

// ----------------------------------------------------------------------

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function loadingButton(theme: Theme) {
  return {
    MuiLoadingButton: {
      styleOverrides: {
        root: ({ ownerState }: { ownerState: LoadingButtonProps }) => ({
          ...(ownerState.variant === 'soft' && {
            [`& .${buttonClasses.startIcon}`]: {
              marginLeft: 10,
            },
            [`& .${buttonClasses.endIcon}`]: {
              marginRight: 14,
            },
            ...(ownerState.size === 'small' && {
              [`& .${buttonClasses.startIcon}`]: {
                marginLeft: 10,
              },
              [`& .${buttonClasses.endIcon}`]: {
                marginRight: 10,
              },
            }),
          }),
        }),
      },
    },
  };
}
