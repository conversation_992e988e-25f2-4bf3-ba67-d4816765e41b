import { Module } from '@nestjs/common';
import { HistoryService } from './history.service';
import { HistoryController } from './history.controller';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { SharedPermissionService } from 'src/shared/services';

@Module({
	providers: [
		HistoryService,
		HistoryApiClient,
		AdminApiClient,
		SharedPermissionService,
	],
	controllers: [HistoryController],
})
export class HistoryModule { }
