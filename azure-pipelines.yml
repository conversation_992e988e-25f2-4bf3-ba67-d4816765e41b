# Docker
# Build a Docker image
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

parameters:
- name: buildapi
  displayName: Build API?
  type: boolean
  default: true
- name: buildclient
  displayName: Build Client?
  type: boolean
  default: true
- name: environment
  displayName: Environment
  type: string
  default: dev
  values:
  - dev
  - uat
  - prod

variables:
  api-image-base-name: 'cdb-api'
  client-image-base-name: 'cdb-client'
  image-env-name: ''
  ${{ if eq(parameters.environment, 'prod') }}:
    containerregisteryconnection: '{"loginServer":"acrhoappsprod.azurecr.io", "id" : "/subscriptions/1691759c-bec8-41b8-a5eb-03c57476ffdb/resourceGroups/Rg-Hoapps-Prod/providers/Microsoft.ContainerRegistry/registries/acrhoappsprod"}'
    containerregistery: 'acrhoappsprod.azurecr.io'
    azure-sub-connection: 'sc_hoappsaks_prod'
  ${{ else }}:
    containerregisteryconnection: '{"loginServer":"acrhoappsuat.azurecr.io", "id" : "/subscriptions/1691759c-bec8-41b8-a5eb-03c57476ffdb/resourceGroups/rg-hoappsuat/providers/Microsoft.ContainerRegistry/registries/acrhoappsuat"}'
    containerregistery: 'acrhoappsuat.azurecr.io'
    azure-sub-connection: 'sc_hoappsaks_uat'
  
  
name: $(Build.BuildId)

trigger:
- release

pool:
  name: Azure Pipelines

steps:
- bash: echo "##vso[build.addbuildtag]${{parameters.environment}}"
  displayName: Set Build Tag

- bash: echo "##vso[task.setvariable variable=image-env-name]${{parameters.environment}}-"
  condition: ne('${{ parameters.environment }}', 'prod')
  displayName: Set Variable Environment Prefix

  
- checkout: git://App Development/SCTS@$(Build.SourceBranch)
  enabled: true
  clean: true

- task: Docker@0
  displayName: 'Build an API image'
  enabled: ${{ parameters.buildapi }}
  inputs:
    containerregistrytype: 'Azure Container Registry'
    azureSubscription: '$(azure-sub-connection)'
    azureContainerRegistry: '$(containerregisteryconnection)'
    action: 'Build an image'
    dockerFile: 'SCTS/api/Dockerfile'
    imageName: '$(image-env-name)$(api-image-base-name):$(Build.BuildId)'
    

- task: Docker@0
  displayName: 'Push an API image'
  enabled: ${{ parameters.buildapi }}
  inputs:
    containerregistrytype: 'Azure Container Registry'
    azureSubscription: '$(azure-sub-connection)'
    azureContainerRegistry: '$(containerregisteryconnection)'
    action: 'Push an image'
    imageName: '$(image-env-name)$(api-image-base-name):$(Build.BuildId)'

- task: Docker@0
  displayName: 'Build Client image'
  enabled: ${{ parameters.buildclient }}
  inputs:
    containerregistrytype: 'Azure Container Registry'
    azureSubscription: '$(azure-sub-connection)'
    azureContainerRegistry: '$(containerregisteryconnection)'
    action: 'Build an image'
    dockerFile: 'SCTS/client/Dockerfile'
    imageName: '$(image-env-name)$(client-image-base-name):$(Build.BuildId)'
    

- task: Docker@0
  displayName: 'Push Client image'
  enabled: ${{ parameters.buildclient }}
  inputs:
    containerregistrytype: 'Azure Container Registry'
    azureSubscription: '$(azure-sub-connection)'
    azureContainerRegistry: '$(containerregisteryconnection)'
    action: 'Push an image'
    imageName: '$(image-env-name)$(client-image-base-name):$(Build.BuildId)'

- checkout: git://App Development/Deployment
  enabled: true
  clean: true

- bash: |
   export image=$(image-env-name)$(api-image-base-name):$(Build.BuildId) export containerregistry='$(containerregistery)'
   envsubst < Deployment/base/deployment/api_template.yaml > Deployment/base/deployment/api.yaml
  displayName: 'set api deployment parameters'
  condition: ${{ parameters.buildapi }}

- bash: |
   envsubst < Deployment/base/service/api_template.yaml > Deployment/base/service/api.yaml
  displayName: 'set api service parameters'
  condition: ${{ parameters.buildapi }}

- bash: |
   export image=$(image-env-name)$(client-image-base-name):$(Build.BuildId) export containerregistry='$(containerregistery)'
   envsubst < Deployment/base/deployment/client_template.yaml > Deployment/base/deployment/client.yaml
  displayName: 'set client deployment parameters'
  condition: ${{ parameters.buildclient }}

- bash: |
   envsubst < Deployment/base/service/client_template.yaml > Deployment/base/service/client.yaml
  displayName: 'set client service parameters'
  condition: ${{ parameters.buildclient }}


- task: PublishBuildArtifacts@1
  enabled: true
  inputs:
    PathtoPublish: Deployment
    ArtifactName: 'drop'
    publishLocation: 'Container'