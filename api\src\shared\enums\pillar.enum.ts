export enum PILLAR {
    LOGISTICS = 'LOGISTICS',
    PORTS_AND_TERMINALS = 'PORTS_AND_TERMINALS',
}

export enum CORES_SOLUTION_ENUM {
    FF = 'FF',
    CL = 'CL',
    MA = 'MA',
    PEZ = 'PEZ',
    RF = 'RF',
    PNT = 'PNT'
}

export enum LOCATION_TYPE_ENUM {
    OPERATION_BRANCH = 'OPERATION_BRANCH',
    AGENT = 'AGENT',
    COMMERCIAL_BUILDING = 'COMMERCIAL_BUILDING',
    SERVICE_CENTER = 'SERVICE_CENTER',
    SALES_OFFICE = 'SALES_OFFICE',
    CUSTOMER_SITE = 'CUSTOMER_SITE',
    DEDICATED_SITE = 'DEDICATED_SITE',
    SHARED_SITE = 'SHARED_SITE',
    CORPORATE_OFFICE = 'CORPORATE_OFFICE',
    BRANCH_OFFICE = 'BRANCH_OFFICE',
    SERVICE_LAND_PLOTS = 'SERVICE_LAND_PLOTS',
    WAREHOUSE = 'WAREHOUSE',
    OFFICE_SPACE = 'OFFICE_SPACE',
    WORKSHOP = 'WORKSHOP',
    PARKING_PLACE = 'PARKING_PLACE',
    CLEANING_STATION = 'CLEANING_STATION'
}