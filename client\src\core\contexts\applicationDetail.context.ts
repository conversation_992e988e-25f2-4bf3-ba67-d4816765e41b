import { ApplicationDetail } from '@/shared/types';
import { createContext, useContext } from 'react';

export const ApplicationDetailContext = createContext<ApplicationDetail>({} as ApplicationDetail);

export const useApplicationContext = () => {
    const context = useContext(ApplicationDetailContext);
    if (!context) throw new Error('ApplicationDetailContext must be use inside ApplicationDetailProvider');
    return context;
};

export const getApplicationName = () => {
    const applicationCtx = useApplicationContext();
    return applicationCtx?.applicationName || '';
}

export const getApplicationDetail = () => {
    const applicationCtx = useApplicationContext();
    return applicationCtx || null;
}