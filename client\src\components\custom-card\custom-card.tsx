import { getIcon } from '@/shared/utils/get-icon';
import { baseGradientColor } from '@/theme/palette';
import { Box, Button, Card, CardContent, Stack, Typography } from '@mui/material';
import React, { ReactElement, ReactNode } from 'react';

interface CustomCardProps extends React.PropsWithChildren {
  id: string | number;
  code?: string | null; // This will be the icon code (e.g., 'ff', 'cl', etc.)
  title?: string | ReactNode;
  children?: ReactElement;
  onClick?: () => void; // Added onClick prop
  [key: string]: any;
  active?: boolean;
}

const CustomCard: React.FC<CustomCardProps> = ({ id, code, title, children, onClick, active = false, ...props }) => {
  const iconPath = getIcon(code || ''); // Get the correct icon path

  return (
    <Card
      key={id}
      onClick={onClick} // Added onClick handler
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 1,
        boxShadow: 3,
        outline: '',
        transition: '0.3s',
        '&:hover': {
          transform: 'scale(1.05)',
          boxShadow: 6,
          cursor: 'pointer',
        },
        minWidth: '90px',
        width: '100%',
        height: '50px',
        background: `${active ? baseGradientColor : 'white'}`,
        ...props.cardStyle,
      }}
    >
      <CardContent sx={{ ...props.cardContentStyle }}>
        <Stack direction="row" spacing={1} alignItems="center" justifyContent="center" sx={{}}>
          {code && (
            <Box
              component="img"
              src={iconPath}
              alt={title?.toString()}
              width={props.iconWidth || 30}
              height={props.iconHeight || 40}
            />
          )}
          {typeof title === 'string' ? (
            <Typography variant="value" sx={{ color: active ? 'white' : 'black' }} fontWeight={900}>
              {title}
            </Typography>
          ) : (
            <Box>{title}</Box>
          )}
          {props.showBackButton && (
            <Box>
              <Button>Back</Button>
            </Box>
          )}
        </Stack>
        {children}
      </CardContent>
    </Card>
  );
};

export default CustomCard;
