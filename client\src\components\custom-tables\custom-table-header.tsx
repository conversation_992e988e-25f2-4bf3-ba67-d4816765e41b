import SvgColor from '@/components/svg-color';
import { useResponsive } from '@/hooks/use-responsive';
import { Stack, Box, Button, Typography } from '@mui/material';
import { Dispatch, SetStateAction } from 'react';
import { useNavigate } from 'react-router-dom';

interface CustomTableHeaderProp {
  heading: string;
  primaryButtonText: string;
  secondaryButtonText: string;
  headingIconPath: string;
  primaryButtonIconPath: string;
  secondaryButtonIconPath: string;
  setShowFilterDrawer: Dispatch<SetStateAction<boolean>>;
  onPrimaryButtonClick?: () => void;
  onSecondaryButtonClick?: () => void;
}

export default function CustomTableHeader({
  heading,
  primaryButtonText,
  secondaryButtonText,
  headingIconPath,
  primaryButtonIconPath,
  secondaryButtonIconPath,
  setShowFilterDrawer,
  onPrimaryButtonClick,
  onSecondaryButtonClick,
}: CustomTableHeaderProp) {
  const navigate = useNavigate();

  const isSmallScreen = useResponsive("down", "sm");

  const handlePrimaryClick = () => {
    if (onPrimaryButtonClick) {
      onPrimaryButtonClick();
    } else {
      navigate(''); // Default behavior
    }
  };

  const handleSecondaryClick = () => {
    if (onSecondaryButtonClick) {
      onSecondaryButtonClick();
    } else if (setShowFilterDrawer) {
      setShowFilterDrawer(true); // Default behavior
    }
  };

  return (
    <Box
      display="flex"
      justifyContent="space-between"
      alignItems="center"
      mb={2}
      sx={{
        flexDirection: isSmallScreen ? 'column' : 'row',
        alignItems: isSmallScreen ? 'flex-start' : 'center',
        gap: isSmallScreen ? 2 : 0,
      }}
    >
      {/* Title Section */}
      <Stack
        direction="row"
        alignItems="center"
        spacing={1}
        sx={{
          flexShrink: 0,
          '& img': {
            transition: 'all 0.3s ease',
          },
        }}
      >
        <SvgColor
          src={headingIconPath || ''}
          sx={{
            width: isSmallScreen ? 32 : 40,
            height: isSmallScreen ? 32 : 40,
            transition: 'all 0.3s ease',
          }}
        />
        <Typography
          variant={isSmallScreen ? 'subtitle2' : 'subtitle1'}
          sx={{
            fontSize: isSmallScreen ? '1rem' : '1.125rem',
            whiteSpace: 'nowrap',
          }}
        >
          {heading}
        </Typography>
      </Stack>

      {/* Buttons Section */}
      <Box sx={{ width: isSmallScreen ? '100%' : 'auto' }}>
        <Stack
          direction="row"
          spacing={1}
          sx={{
            justifyContent: isSmallScreen ? 'space-between' : 'flex-end',
            width: '100%',
          }}
        >
          <Button
            onClick={handlePrimaryClick}
            variant="outlined"
            size={isSmallScreen ? 'small' : 'medium'}
            sx={{
              borderRadius: '24px',
              textTransform: 'none',
              px: isSmallScreen ? 2 : 3,
              whiteSpace: 'nowrap',
              minWidth: isSmallScreen ? '48%' : 'auto',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: 1,
              },
              transition: 'all 0.2s ease',
            }}
            startIcon={
              <SvgColor
                src={primaryButtonIconPath || ''}
                sx={{
                  // width: isSmallScreen ? 16 : 20,
                  // height: isSmallScreen ? 16 : 20,
                  transition: 'all 0.3s ease',
                }}
              />
            }
          >
            {primaryButtonText}
          </Button>

          <Button
            onClick={handleSecondaryClick}
            variant="outlined"
            size={isSmallScreen ? 'small' : 'medium'}
            sx={{
              borderRadius: '24px',
              textTransform: 'none',
              px: isSmallScreen ? 2 : 3,
              whiteSpace: 'nowrap',
              minWidth: isSmallScreen ? '48%' : 'auto',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: 1,
              },
              transition: 'all 0.2s ease',
            }}
            startIcon={
              <SvgColor
                src={secondaryButtonIconPath || ''}
                sx={{
                  // width: isSmallScreen ? 16 : 20,
                  // height: isSmallScreen ? 16 : 20,
                  transition: 'all 0.3s ease',
                }}
              />
            }
          >
            {secondaryButtonText}
          </Button>
        </Stack>
      </Box>
    </Box>
  );
}
