import { GraphUserResponse, UserDetail } from '@/shared/models';
import { getUserDetails, searchGraphUsers } from '@/shared/services';
import getUserLoginId from '@/shared/utils/get-user-login-id';
import { Box, Chip, CircularProgress, debounce } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { Stack } from '@mui/system';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';

type Prop = {
  initialUserIds?: string[];
  onSelect: (users: UserDetail[]) => void;
  required?: boolean;
  validationError?: string;
  placeholder?: string;
};

export default function AdUserSearchInputMulti({
  validationError = '',
  initialUserIds = [],
  onSelect,
  required = false,
  placeholder,
}: Prop) {
  const { t } = useTranslation();

  const [selectedUsers, setSelectedUsers] = useState<GraphUserResponse[]>([]);
  const [searchText, setSearchText] = useState('');
  const debouncedSetSearchText = useMemo(() => debounce(setSearchText, 500), []);
  const [isLoadingInitialUserIds, setIsLoadingInitialUserIds] = useState(false);
  const { data, isFetching, refetch } = useQuery(
    ['adUsers', searchText],
    () => searchGraphUsers(searchText, 'displayName'),
    {
      enabled: !!searchText,
    },
  );

  useEffect(() => {
    if (initialUserIds.length) {
      const getInitialUserValue = async () => {
        setIsLoadingInitialUserIds(true);
        let allInitialUsers = [];
        for (let i = 0; i < initialUserIds.length; i++) {
          const user = await getUserDetails(initialUserIds[i]);
          if (user?.value[user.value.length - 1]) {
            allInitialUsers.push(user?.value[user.value.length - 1]);
          }
        }
        setIsLoadingInitialUserIds(false);
        setSelectedUsers(allInitialUsers);
      };
      getInitialUserValue();
    } else {
      setSelectedUsers([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (searchText.length) {
      refetch();
    }
  }, [refetch, searchText]);
  // TODO: Change type this to UserDetail
  const handleUserSelect = (_: any, newValue: GraphUserResponse[]) => {
    setSelectedUsers(newValue);
    onSelect(
      newValue.map((item) => {
        return {
          firstName: item?.givenName || '',
          lastName: item?.surname || '',
          loginId: getUserLoginId(item),
          email: item.mail ?? '',
          jobTitle: item?.jobTitle ?? '',
          role: '',
          phone: '',
          escalationLevel: -1,
          isAdSearch: true,
        };
      }),
    );
    setSearchText('');
  };

  return (
    <>
      <Autocomplete
        multiple
        loading={isFetching}
        fullWidth
        id="adUserSearchMulti"
        getOptionLabel={(option) => option.displayName}
        filterOptions={(x) => x}
        options={data?.value || []}
        isOptionEqualToValue={(option, value) => option.id === value?.id}
        autoComplete
        includeInputInList
        value={selectedUsers}
        noOptionsText={
          searchText ? (
            <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
              {t('empty_state_messages.no_user_found')}
            </Typography>
          ) : (
            <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
              {t('messages.type_something_to_search')}
            </Typography>
          )
        }
        onChange={handleUserSelect}
        onInputChange={(_event, value) => {
          debouncedSetSearchText(value);
        }}
        slots={{
          paper: ({ key, ...restOfProps }) => (
            <Box {...restOfProps} sx={{ borderRadius: 2, boxShadow: 3, p: 1 }}>
              {isFetching ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 }}>
                  <CircularProgress size={24} />
                  <Typography sx={{ marginLeft: 1 }}>{t('label.searching')}</Typography>
                </Box>
              ) : searchText && data?.value?.length === 0 ? (
                <Typography sx={{ textAlign: 'center', p: 2, color: 'text.secondary' }}>
                  {t('label.no_results_found')}
                </Typography>
              ) : (
                restOfProps.children
              )}
            </Box>
          ),
        }}
        renderInput={(params) => (
          <TextField
            error={Boolean(validationError)}
            {...params}
            fullWidth
            placeholder={isLoadingInitialUserIds ? t('loading') : ''}
            label={
              <span>
                {placeholder || t('label.search_user')}
                {required && <span> *</span>}
              </span>
            }
            slotProps={{
              inputLabel: {
                shrink: true,
              },
            }}
          />
        )}
        renderTags={(value: GraphUserResponse[], getTagProps) => {
          return value.map((option: GraphUserResponse, index: number) => {
            const { key, ...tagProps } = getTagProps({ index });

            return <Chip label={option.displayName} key={key} size="small" {...tagProps} />;
          });
        }}
        renderOption={(props, option) => {
          const { key, ...restOfProps } = props;
          return (
            <li {...restOfProps} key={option.id}>
              <Grid container alignItems="center">
                <Grid sx={{ wordWrap: 'break-word' }}>
                  <Stack flexDirection={'column'}>
                    <Typography variant="subtitle1" color="text.secondary" fontSize={14}>
                      {option.displayName}
                    </Typography>
                    <Typography variant="subtitle2" color="text.secondary" fontSize={12}>
                      {option.mail}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" fontSize={12}>
                      {option.jobTitle}
                    </Typography>
                  </Stack>
                </Grid>
              </Grid>
            </li>
          );
        }}
      />

      {/* <Stack direction="row" spacing={1} mt={2}>
        {selectedUsers.map((user) => (
          <Chip
            key={user.id}
            label={user.displayName}
            onDelete={() =>
              handleUserSelect(
                null,
                selectedUsers.filter((u) => u.id !== user.id),
              )
            }
          />
        ))}
      </Stack> */}
    </>
  );
}
