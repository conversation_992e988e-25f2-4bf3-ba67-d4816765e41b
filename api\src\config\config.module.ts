import { Module, Global } from '@nestjs/common';
import { ConfigController } from './config.controller';
import { ConfigService } from './config.service';
import { AdminApiClient } from 'src/shared/clients';
import { SharedPermissionService } from 'src/shared/services';
import { BusinessEntityService } from 'src/business-entity/services';
@Global()
@Module({
	providers: [
		{
			provide: ConfigService,
			useValue: new ConfigService(),
		},
		AdminApiClient,
		SharedPermissionService,
		BusinessEntityService,
	],
	controllers: [ConfigController],
	imports: [],
	exports: [ConfigService],
})
export class ConfigModule { }
