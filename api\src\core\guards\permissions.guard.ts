import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AdminApiClient } from 'src/shared/clients';
import { PERMISSIONS } from 'src/shared/enums';
import { SharedPermissionService } from 'src/shared/services';

@Injectable()
export class PermissionsGuard implements CanActivate {
	constructor(
		private readonly reflector: Reflector,
		private readonly adminApiClient: AdminApiClient,
		private readonly permissionService: SharedPermissionService,

	) { }

	async canActivate(context: ExecutionContext): Promise<boolean> {
		const routePermission = this.reflector.get<{
			permission: PERMISSIONS | PERMISSIONS[];
			checkEntity?: boolean;
			checkReader?: boolean;
		}>('permissions', context.getHandler());
		let request = context.switchToHttp().getRequest();
		const { body, user, query, params } = request;
		const { permission, checkEntity } = routePermission;
		let entityId = null;

		if (permission === PERMISSIONS.ANY) {
			//Enabling system for all users to have view rights.
			return true;
		}

		/** check if we are getting entityId in the request object and send as a
		 * parameter to check if user as a permission on that entity for the given operation
		 **/
		if (checkEntity && (body?.entityId || query?.entityId || params?.entityId)) {
			entityId = body?.entityId || query?.entityId || params?.entityId;
		}

		/**
		 * Attach the list of locations in the current context
		 * where user has the given permission.
		 */
		if (typeof permission === 'string') {
			const locations = await this.permissionService.getAllLocationIdForGivenPermission(
				user.unique_name,
				permission,
			);

			request.user.locations = locations || [];
			return this.adminApiClient.hasPermissionToUser(user.unique_name, permission, entityId);
		} else if (Array.isArray(permission)) {

			const globalPermissions = permission.filter((p) => !p.startsWith('Local.'));

			if (globalPermissions?.length) {
				const isAllowed = await this.permissionService.checkGlobalPermission(globalPermissions, user, entityId);
				if (isAllowed) {
					return true;
				}
			}
		}
		return false;
	}
}
