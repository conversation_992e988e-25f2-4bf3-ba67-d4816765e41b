import { startCase, toLower } from 'lodash';

export function toUpperUnderscore(str: string): string {
	// 1. Keep alphanumeric characters and anything inside () as words
	// 2. Remove special characters except space and parentheses
	const cleaned = str
		.replace(/[^a-zA-Z0-9 ()]/g, '') // remove special characters but keep space and ()
		.replace(/[()]/g, '') // remove ( and )
		.replace(/\s+/g, ' '); // normalize spaces

	return cleaned.trim().toUpperCase().replace(/ /g, '_');
}

export function convertEnumsToSentenceCase(str: string): string {
	const noUnderscores = str.replace(/_/g, ' ');
	const lowered = toLower(noUnderscores);
	return lowered.charAt(0).toUpperCase() + lowered.slice(1);
}
