<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 122.94 91.92">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
        isolation: isolate;
        stroke-width: 0px;
      }

      .cls-2 {
        fill: none;
        stroke: url(#linear-gradient);
        stroke-miterlimit: 10;
      }
    </style>
    <linearGradient id="linear-gradient" x1="389" y1="58.23" x2="338.15" y2="58.2" gradientTransform="translate(389 104.16) rotate(-180)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#010101"/>
      <stop offset="1" stop-color="#010101" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="266.07" y1="58.2" x2="388.26" y2="58.2" gradientTransform="translate(-265.33 -12.24)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0" stop-color="#f5f5f5"/>
      <stop offset=".05" stop-color="#c7c7c7"/>
      <stop offset=".09" stop-color="#9c9c9c"/>
      <stop offset=".14" stop-color="#777"/>
      <stop offset=".2" stop-color="#565656"/>
      <stop offset=".25" stop-color="#3b3b3b"/>
      <stop offset=".32" stop-color="#252525"/>
      <stop offset=".4" stop-color="#141414"/>
      <stop offset=".49" stop-color="#080808"/>
      <stop offset=".63" stop-color="#010101"/>
      <stop offset="1" stop-color="#000"/>
    </linearGradient>
  </defs>
  <path class="cls-2" d="M50.51.37L.74,45.92l49.74,45.62"/>
  <rect class="cls-1" x=".74" y="45.15" width="122.2" height="1.62" transform="translate(123.68 91.92) rotate(180)"/>
</svg>