<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 119.58 186.97">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="59.78" y1="116.84" x2="59.78" y2="193.38" gradientTransform="translate(0 193.38) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".15" stop-color="#0f0f19" stop-opacity=".34"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".83"/>
      <stop offset=".8" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="59.79" y1="96.6" x2="59.79" y2="6.41" gradientTransform="translate(0 193.38) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".54" stop-color="#0f0f19" stop-opacity=".89"/>
      <stop offset=".8" stop-color="#0f0f19" stop-opacity=".54"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-2" d="M59.79,76.54c-21.14,0-38.27-17.12-38.28-38.26C21.5,17.14,38.63,0,59.77,0c21.14,0,38.27,17.12,38.28,38.26h0c-.02,21.13-17.14,38.25-38.26,38.28ZM59.79,8.88c-16.24,0-29.4,13.15-29.41,29.39,0,16.24,13.15,29.4,29.39,29.41,16.24,0,29.4-13.15,29.41-29.39v-.02c-.02-16.22-13.17-29.37-29.39-29.39Z"/>
  <path class="cls-1" d="M119.58,186.93h-8.87v-40.74c-.04-22.36-18.18-40.48-40.54-40.5h-20.76c-22.38.02-40.52,18.16-40.54,40.54v40.74H0v-40.78c.03-27.27,22.14-49.38,49.41-49.41h20.76c27.27.03,49.38,22.14,49.41,49.41v40.74Z"/>
</svg>