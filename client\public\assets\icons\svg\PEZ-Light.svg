<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 202.24 229.85">
  <defs>
    <style>
      .cls-1 {
        fill: #f5f3f5;
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-3 {
        fill: url(#linear-gradient-10);
      }

      .cls-4 {
        fill: url(#linear-gradient-4);
      }

      .cls-5 {
        fill: url(#linear-gradient-3);
      }

      .cls-6 {
        fill: url(#linear-gradient-5);
      }

      .cls-7 {
        fill: url(#linear-gradient-8);
      }

      .cls-8 {
        fill: url(#linear-gradient-7);
      }

      .cls-9 {
        fill: url(#linear-gradient-9);
      }

      .cls-10 {
        fill: url(#linear-gradient-6);
      }

      .cls-11 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="33.8" y1="115.75" x2="33.8" y2="58.36" gradientTransform="translate(-1.49 234.18) rotate(.98) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f5f3f5"/>
      <stop offset=".09" stop-color="#f5f3f5" stop-opacity=".94"/>
      <stop offset=".27" stop-color="#f5f3f5" stop-opacity=".8"/>
      <stop offset=".51" stop-color="#f5f3f5" stop-opacity=".56"/>
      <stop offset=".8" stop-color="#f5f3f5" stop-opacity=".24"/>
      <stop offset="1" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="5.45" y1="111.52" x2="5.45" y2="4.93" gradientTransform="translate(-52.83 172.26) rotate(88.86) scale(1 -1)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-3" x1="66.88" y1="116.47" x2="66.88" y2="56.81" gradientTransform="translate(0 234.78) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#f5f3f5"/>
      <stop offset=".2" stop-color="#f5f3f5" stop-opacity=".94"/>
      <stop offset=".35" stop-color="#f5f3f5" stop-opacity=".8"/>
      <stop offset=".56" stop-color="#f5f3f5" stop-opacity=".56"/>
      <stop offset=".8" stop-color="#f5f3f5" stop-opacity=".24"/>
      <stop offset=".97" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="113.8" x2="113.8" y2="56.82" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-5" x1="160.71" x2="160.71" y2="56.62" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-6" x1="32.61" y1="5.29" x2="32.61" y2="51.27" gradientTransform="translate(0 234.78) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f5f3f5" stop-opacity="0"/>
      <stop offset=".04" stop-color="#f5f3f5" stop-opacity=".15"/>
      <stop offset=".11" stop-color="#f5f3f5" stop-opacity=".37"/>
      <stop offset=".18" stop-color="#f5f3f5" stop-opacity=".56"/>
      <stop offset=".25" stop-color="#f5f3f5" stop-opacity=".72"/>
      <stop offset=".32" stop-color="#f5f3f5" stop-opacity=".84"/>
      <stop offset=".38" stop-color="#f5f3f5" stop-opacity=".93"/>
      <stop offset=".45" stop-color="#f5f3f5" stop-opacity=".98"/>
      <stop offset=".51" stop-color="#f5f3f5"/>
      <stop offset=".56" stop-color="#f5f3f5" stop-opacity=".98"/>
      <stop offset=".63" stop-color="#f5f3f5" stop-opacity=".93"/>
      <stop offset=".69" stop-color="#f5f3f5" stop-opacity=".83"/>
      <stop offset=".76" stop-color="#f5f3f5" stop-opacity=".71"/>
      <stop offset=".83" stop-color="#f5f3f5" stop-opacity=".54"/>
      <stop offset=".9" stop-color="#f5f3f5" stop-opacity=".34"/>
      <stop offset=".97" stop-color="#f5f3f5" stop-opacity=".1"/>
      <stop offset="1" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="187.83" x2="187.83" xlink:href="#linear-gradient-6"/>
    <linearGradient id="linear-gradient-8" x1="18.22" y1="55.34" x2="202.24" y2="55.34" gradientTransform="translate(0 234.78) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f5f3f5" stop-opacity="0"/>
      <stop offset=".04" stop-color="#f5f3f5" stop-opacity=".19"/>
      <stop offset=".08" stop-color="#f5f3f5" stop-opacity=".38"/>
      <stop offset=".13" stop-color="#f5f3f5" stop-opacity=".55"/>
      <stop offset=".17" stop-color="#f5f3f5" stop-opacity=".69"/>
      <stop offset=".22" stop-color="#f5f3f5" stop-opacity=".8"/>
      <stop offset=".28" stop-color="#f5f3f5" stop-opacity=".89"/>
      <stop offset=".34" stop-color="#f5f3f5" stop-opacity=".95"/>
      <stop offset=".41" stop-color="#f5f3f5" stop-opacity=".99"/>
      <stop offset=".51" stop-color="#f5f3f5"/>
      <stop offset=".62" stop-color="#f5f3f5" stop-opacity=".99"/>
      <stop offset=".69" stop-color="#f5f3f5" stop-opacity=".96"/>
      <stop offset=".75" stop-color="#f5f3f5" stop-opacity=".9"/>
      <stop offset=".8" stop-color="#f5f3f5" stop-opacity=".82"/>
      <stop offset=".84" stop-color="#f5f3f5" stop-opacity=".71"/>
      <stop offset=".88" stop-color="#f5f3f5" stop-opacity=".58"/>
      <stop offset=".92" stop-color="#f5f3f5" stop-opacity=".42"/>
      <stop offset=".96" stop-color="#f5f3f5" stop-opacity=".24"/>
      <stop offset=".99" stop-color="#f5f3f5" stop-opacity=".05"/>
      <stop offset="1" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="69.12" y1="185.6" x2="44.18" y2="185.6" gradientTransform="translate(0 234.78) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#f5f3f5"/>
      <stop offset=".25" stop-color="#f5f3f5" stop-opacity=".93"/>
      <stop offset=".41" stop-color="#f5f3f5" stop-opacity=".75"/>
      <stop offset=".63" stop-color="#f5f3f5" stop-opacity=".47"/>
      <stop offset=".9" stop-color="#f5f3f5" stop-opacity=".08"/>
      <stop offset=".95" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="38.07" y1="210.69" x2="25.07" y2="189.06" gradientTransform="translate(0 234.78) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".14" stop-color="#f5f3f5"/>
      <stop offset=".21" stop-color="#f5f3f5" stop-opacity=".97"/>
      <stop offset=".32" stop-color="#f5f3f5" stop-opacity=".88"/>
      <stop offset=".44" stop-color="#f5f3f5" stop-opacity=".75"/>
      <stop offset=".58" stop-color="#f5f3f5" stop-opacity=".55"/>
      <stop offset=".74" stop-color="#f5f3f5" stop-opacity=".31"/>
      <stop offset=".91" stop-color="#f5f3f5" stop-opacity="0"/>
      <stop offset=".91" stop-color="#f5f3f5" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <rect class="cls-11" x="29.36" y="119.09" width="8.87" height="57.25" transform="translate(-2.52 .6) rotate(-.98)"/>
  <polygon class="cls-1" points="10.96 123.44 2.09 123.21 3.98 50.48 35.98 50.48 37.73 120.35 28.87 120.57 27.33 59.35 12.62 59.35 10.96 123.44"/>
  <rect class="cls-2" x="-47.73" y="172.12" width="106.44" height="8.87" transform="translate(-171.14 178.53) rotate(-88.86)"/>
  <polygon class="cls-5" points="57.84 177.96 48.97 177.66 50.92 118.3 82.86 118.3 84.8 177.46 75.94 177.75 74.27 127.17 59.51 127.17 57.84 177.96"/>
  <polygon class="cls-4" points="104.75 177.95 95.88 177.66 97.83 118.3 129.77 118.3 131.72 177.65 122.85 177.94 121.19 127.17 106.42 127.17 104.75 177.95"/>
  <polygon class="cls-6" points="151.65 178.15 142.79 177.86 144.75 118.3 176.68 118.3 178.63 177.65 169.77 177.95 168.1 127.17 153.33 127.17 151.65 178.15"/>
  <rect class="cls-10" x="28.18" y="183.5" width="8.87" height="45.98"/>
  <rect class="cls-8" x="183.4" y="183.5" width="8.87" height="45.98"/>
  <rect class="cls-7" x="18.22" y="175" width="184.02" height="8.87"/>
  <path class="cls-9" d="M68.33,56.76c-4.57-.48-8.92-2.25-12.53-5.09-3.44,2.18-7.43,3.31-11.5,3.26l-.09-8.87c4.58,0,7.08-1.91,7.59-2.55,1.7-2.18,4.84-2.57,7.02-.87.13.1.25.2.36.31,3.24,3,6.6,4.67,10,5l-.85,8.81Z"/>
  <path class="cls-1" d="M140.52,77.84c-11.21,0-23-4.06-31.35-14.1-9.82,2.59-22.73,1.69-31.52-8.19-2.99,1.09-6.18,1.5-9.35,1.21l.82-8.83c2.47.23,4.96-.27,7.14-1.46,2.24-1.32,5.12-.7,6.62,1.43,8.58,12,23.21,7.64,26.05,6.65,2.12-.73,4.47.02,5.77,1.84,9.07,12.67,25,14.62,36.82,10.93,12.92-4.05,20.73-14.07,20.92-26.8.22-15.53-15-31.21-30.77-31.66-5.64-.04-11.17,1.54-15.95,4.54-.77.52-4.93,3.54-6,4.44-1.81,1.53-4.44,1.61-6.33.18-15.55-11.77-29-.35-31.54,2.06-1.68,1.59-4.22,1.84-6.17.61-3.11-2-6-2.54-8.94-1.82-5.12,1.28-9.8,6.73-12.24,11-1.27,2.19-3.96,3.11-6.31,2.15-1.58-.8-3.46-.75-5,.13l-4.45-7.67c2.93-1.73,6.4-2.31,9.73-1.62,2.81-4.1,8.36-10.64,16.15-12.59,4.46-1.12,9.18-.58,13.28,1.51,9.3-7.54,23.92-11.95,38.45-2.53,1.94-1.45,4.28-3.13,4.73-3.4C127.33,1.95,134.56-.08,141.93,0c20.54.58,39.68,20.34,39.39,40.64-.24,16.51-10.64,30-27.14,35.14-4.42,1.37-9.03,2.06-13.66,2.06ZM111.86,62.92h0ZM107.48,61.54h0ZM46.82,25.46h0ZM75.77,13.64h0Z"/>
  <path class="cls-3" d="M26.56,44.26l-8.44-2.72c.22-.71,2.36-7,7.75-9.11,1.82-.71,3.82-.87,5.73-.45,1.71-3.06,4.16-5.64,7.13-7.5l4.45,7.67c-2.24,1.31-4.09,4.25-5.07,6.57-1.09,2.57-4.07,3.77-6.64,2.67-.18-.07-.35-.16-.51-.25-.52-.4-1.19-.56-1.84-.45-1.01.39-2.15,2.39-2.56,3.57Z"/>
</svg>