import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional, IsString, IsNumber, IsNotEmpty } from 'class-validator';

export class UserDetailResponseDto {
	@ApiProperty()
	@IsBoolean()
	@Expose()
	isAdSearch: boolean;

	@ApiProperty()
	@IsString()
	@IsNotEmpty()
	@Expose()
	firstName: string;

	@ApiProperty()
	@IsString()
	@IsOptional()
	@Expose()
	lastName?: string;

	@ApiProperty()
	@IsString()
	@IsOptional()
	@Expose()
	email?: string;

	@ApiProperty()
	@IsString()
	@IsNotEmpty()
	@Expose()
	loginId: string;

	@ApiProperty()
	@IsString()
	@IsNotEmpty()
	@Expose()
	phone: string;

	@ApiProperty()
	@IsString()
	@IsOptional()
	@Expose()
	jobTitle?: string;

	@ApiProperty()
	@IsString()
	@IsOptional()
	@Expose()
	role?: string;

	@ApiProperty()
	@IsBoolean()
	@IsOptional()
	@Expose()
	isEscalation?: boolean;

	@ApiProperty()
	@IsNumber()
	@IsOptional()
	@Expose()
	escalationLevel?: number;
}
export interface UserDetail {
	isAdSearch: boolean;
	firstName: string;
	lastName?: string;
	loginId: string;
	email?: string;
	phone?: string;
	jobTitle?: string;
	role?: string;
	isEscalation?: boolean;
	escalationLevel?: number;
}

export interface AllowedUpdateUserDetail {
	phone?: string;
	jobTitle?: string;
	role?: string;
	isEscalation?: boolean;
	escalationLevel?: number;
}
