import { <PERSON>, Get, Param, Query, Req, <PERSON>s, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RequestContext } from 'src/shared/types';
import { AttachmentService } from '../services/attachment.service';
import { HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE, PERMISSIONS } from 'src/shared/enums';
import { PermissionsGuard } from 'src/core/guards';
import { Permissions } from 'src/core/decorators';
import { HistoryApiClient } from 'src/shared/clients';
@ApiTags('Attachment API')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('attachment')
export class AttachmentController {
	constructor(private readonly attachmentService: AttachmentService,
		private readonly historyService: HistoryApiClient,
	) { }

	/*
	 * Get document content by id.
	 */
	@Permissions(PERMISSIONS.ANY)
	@UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
	@ApiResponse({
		status: 200,
		description: 'Get document content by id.',
		type: Buffer,
	})
	@Get('content/:fileId')
	public async getContentByFileId(
		@Param('fileId') fileId: string,
		@Req() request: RequestContext,
		@Res() res: any,
	) {
		const data = await this.attachmentService.getContentByFileId(fileId, request.currentContext);

		if (data?.attachment_name) {
			data.attachment_name = data.attachment_name.replace(/[^\w\s-]/g, '');
		}

		await this.historyService.addRequestHistory({
			created_by: request.currentContext.user.username,
			entity_id: -1,
			entity_type: HISTORY_ENTITY_TYPE.EXPORT,
			action_performed: HISTORY_ACTION_TYPE.DOWNLOAD_ATTACHMENT,
			action_date: new Date(),
			comments: `Attachment ${data.attachment_name} downloaded.`,
			additional_info: {
				fileId,
				entityId: data.entity_id,
				attachmentName: data.attachment_name,
				attachmentContentType: data?.attachment_content_type || '',
				metaData1: data?.meta_data_1 || '',
				metaData2: data?.meta_data_2 || '',
				metaData3: data?.meta_data_3 || '',
				additional_info: data?.additional_info || null,
				host: request.headers?.host,
				origin: request.headers?.origin
			}
		});

		res.status(200);
		res.setHeader('Content-Type', data.attachment_content_type);
		res.setHeader('Content-Disposition', 'attachment; filename=' + data.attachment_name);
		const buffer: Buffer = Buffer.from(data.contents.data);
		res.write(buffer);
		res.end();
	}
}
