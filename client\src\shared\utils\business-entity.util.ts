import { TreeViewDataType } from '@/components/custom-treeview/treeview-types';

export const getBusinessUnitById = (hierarchy: TreeViewDataType, id: string): TreeViewDataType | null => {
  // Base case: If the current object's ID matches the target ID, return it
  if (hierarchy.id === id) {
    return hierarchy;
  }

  // Recursive case: Iterate through children and search for the ID
  if (hierarchy.children && hierarchy.children.length > 0) {
    for (let i = 0; i < hierarchy.children.length; i++) {
      const foundObject = getBusinessUnitById(hierarchy.children[i], id);
      if (foundObject) {
        return foundObject; // If found in children, return it
      }
    }
  }
  // If not found, return null
  return null;
};
