import { Injectable } from '@nestjs/common';
import { AdminApiClient } from '../clients';
import { PermissionsResponse } from '../types';
import { PERMISSIONS } from '../enums';

@Injectable()
export class SharedPermissionService {
	constructor(
		private readonly adminApiClient: AdminApiClient,
	) { }

	/**
	 * Get location ids for an given permission for the user.
	 * @param username
	 * @param permission
	 * @returns
	 */
	public async getAllLocationIdForGivenPermission(
		username: string,
		permission: string,
	): Promise<number[] | null> {
		const response = await this.adminApiClient.getListOfUserPermissions(username);
		const locations = response.find(
			(p: PermissionsResponse) => p.permissionName === permission,
		)?.locations.map(Number);
		return locations || null;
	}

	public async checkGlobalPermission(globalPermissions: PERMISSIONS[], user: any, entityId: number) {
		let allLocations = [];

		for (const p of globalPermissions) {
			const locations = await this.getAllLocationIdForGivenPermission(
				user.unique_name,
				p,
			);

			allLocations = locations ? [...allLocations, ...locations] : allLocations;

			if (await this.adminApiClient.hasPermissionToUser(user.unique_name, p, entityId)) {
				user.locations = allLocations;
				return true;
			}
		}

		return false;
	}

	public async checkAnyPermission(permissions: PERMISSIONS[], user: any, entityId: number, locationId: number) {
		const globalPermissions = permissions.filter((p) => !p.startsWith('Local.'));

		if (globalPermissions?.length) {
			const hasGlobalPermission = await this.checkGlobalPermission(globalPermissions, user, entityId);

			if (hasGlobalPermission) {
				return true;
			}
		}

		return false;
	}

}
