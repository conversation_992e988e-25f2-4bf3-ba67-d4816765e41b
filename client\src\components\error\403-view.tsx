import { m } from 'framer-motion';

import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import { RouterLink } from '@/routes/components';

import { ForbiddenIllustration } from '@/assets/illustrations';

import { varBounce, MotionContainer } from '@/components/animate';

// ----------------------------------------------------------------------

export default function View403() {
  return (
    <MotionContainer sx={{ display:'flex', flexDirection:'column', alignItems: 'center', justifyContent: 'center', height: '100%'}}>
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          No permission
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary', textAlign:'center' }}>
          The page you&apos;re trying access has restricted access.
          <br />
          Please refer to your system administrator
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <ForbiddenIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />
      </m.div>

      {/* <Button component={RouterLink} href="/"  variant="contained">
        Go to Home
      </Button> */}
    </MotionContainer>
  );
}
