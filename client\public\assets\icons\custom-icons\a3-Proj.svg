<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 111.99 111.81">
  <defs>
    <style>
      .cls-1 {
        clip-path: url(#clippath-11);
      }

      .cls-2 {
        clip-path: url(#clippath-16);
      }

      .cls-3 {
        clip-path: url(#clippath-2);
      }

      .cls-4 {
        clip-path: url(#clippath-13);
      }

      .cls-5 {
        isolation: isolate;
      }

      .cls-6 {
        clip-path: url(#clippath-15);
      }

      .cls-7 {
        clip-path: url(#clippath-6);
      }

      .cls-8 {
        fill: none;
      }

      .cls-8, .cls-9 {
        stroke-width: 0px;
      }

      .cls-10 {
        clip-path: url(#clippath-7);
      }

      .cls-11 {
        clip-path: url(#clippath-1);
      }

      .cls-12 {
        clip-path: url(#clippath-4);
      }

      .cls-13 {
        clip-path: url(#clippath-12);
      }

      .cls-14 {
        clip-path: url(#clippath-9);
      }

      .cls-15 {
        clip-path: url(#clippath);
      }

      .cls-16 {
        clip-path: url(#clippath-3);
      }

      .cls-17 {
        clip-path: url(#clippath-8);
      }

      .cls-18 {
        clip-path: url(#clippath-14);
      }

      .cls-19 {
        clip-path: url(#clippath-17);
      }

      .cls-20 {
        clip-path: url(#clippath-5);
      }

      .cls-21 {
        clip-path: url(#clippath-10);
      }

      .cls-9 {
        fill: #12121b;
      }
    </style>
    <clipPath id="clippath">
      <rect class="cls-8" width="111.99" height="111.81"/>
    </clipPath>
    <clipPath id="clippath-1">
      <rect class="cls-8" width="111.99" height="111.81"/>
    </clipPath>
    <clipPath id="clippath-2">
      <path class="cls-8" d="M85.73,33.06v4.3c12.11.23,21.74,10.22,21.51,22.33-.22,11.79-9.72,21.29-21.51,21.51v4.3h.07c14.48,0,26.22-11.76,26.21-26.24,0-14.48-11.76-26.22-26.24-26.21h-.03"/>
    </clipPath>
    <clipPath id="clippath-3">
      <rect class="cls-8" width="111.99" height="111.81"/>
    </clipPath>
    <clipPath id="clippath-4">
      <rect class="cls-8" x="83.55" y="27.17" width="6.47" height="4.3" transform="translate(56.88 115.91) rotate(-89.61)"/>
    </clipPath>
    <clipPath id="clippath-5">
      <rect class="cls-8" x="84.5" y="86.09" width="4.3" height="8.19" transform="translate(-.72 .7) rotate(-.46)"/>
    </clipPath>
    <clipPath id="clippath-6">
      <rect class="cls-8" width="111.99" height="111.81"/>
    </clipPath>
    <clipPath id="clippath-7">
      <rect class="cls-8" x="85.99" y="54.42" width="11.23" height="4.3" transform="translate(-13.17 81.35) rotate(-45)"/>
    </clipPath>
    <clipPath id="clippath-8">
      <rect class="cls-8" width="111.99" height="111.81"/>
    </clipPath>
    <clipPath id="clippath-9">
      <polygon class="cls-8" points="0 111.81 82.24 111.81 82.06 98.56 77.76 98.62 77.88 107.51 4.31 107.51 4.44 10.51 16.94 10.63 16.98 6.33 .15 6.15 0 111.81"/>
    </clipPath>
    <clipPath id="clippath-10">
      <rect class="cls-8" x="26.3" y="20.99" width="26.44" height="4.3"/>
    </clipPath>
    <clipPath id="clippath-11">
      <rect class="cls-8" x="26.34" y="33.78" width="29.68" height="4.3"/>
    </clipPath>
    <clipPath id="clippath-12">
      <rect class="cls-8" x="26.34" y="46.86" width="26.41" height="4.3"/>
    </clipPath>
    <clipPath id="clippath-13">
      <rect class="cls-8" x="26.34" y="60.51" width="26.41" height="4.3"/>
    </clipPath>
    <clipPath id="clippath-14">
      <rect class="cls-8" x="26.34" y="73.3" width="26.38" height="4.3"/>
    </clipPath>
    <clipPath id="clippath-15">
      <rect class="cls-8" x="61.59" y="8.63" width="8.52" height="4.3" transform="translate(54.67 76.56) rotate(-89.65)"/>
    </clipPath>
    <clipPath id="clippath-16">
      <rect class="cls-8" width="111.99" height="111.81"/>
    </clipPath>
    <clipPath id="clippath-17">
      <rect class="cls-8" x="76.34" y="19.97" width="4.3" height="8.52" transform="translate(53.78 102.57) rotate(-89.64)"/>
    </clipPath>
    <image id="image" width="55" height="9" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAAKCAYAAAAQP8Y1AAAACXBIWXMAABbCAAAWwgFTLGrhAAAARUlEQVRIiWMUEZT8z0AY/GFgYPgNxb+wsIkVo7X8LwYGhu8MDAzf37x//pOJCI8NWTDquaEKRj03VMGo54YqGNaeG9YAAEIaMRJME90MAAAAAElFTkSuQmCC"/>
  </defs>
  <g class="cls-15">
    <g id="Group_576" data-name="Group 576">
      <g id="Group_543" data-name="Group 543">
        <g class="cls-11">
          <g id="Group_542" data-name="Group 542">
            <path id="Path_519" data-name="Path 519" class="cls-9" d="M85.73,85.51c-14.48,0-26.23-11.74-26.23-26.23s11.74-26.23,26.23-26.23v4.3c-12.11.23-21.74,10.22-21.51,22.33.22,11.79,9.72,21.29,21.51,21.51v4.31Z"/>
          </g>
        </g>
      </g>
      <g id="Group_545" data-name="Group 545">
        <g class="cls-3">
          <g id="Group_544" data-name="Group 544">
            <image id="Rectangle_787" data-name="Rectangle 787" class="cls-5" width="55" height="109" transform="translate(85.37 32.75) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAABtCAYAAAD0xaJaAAAACXBIWXMAABbCAAAWwgFTLGrhAAAAv0lEQVR4nO3PMQpCQQxAwegNBBvvfzgbwdKPgtpYiaiNxcgbCAvJNm+13exu89l1Zi6POT+9r3bvbr/8f56Z08wsh+N+mZlZfxFHK1BXoK5AXYG6AnUF6grUFagrUFegrkBdgboCdQXqCtQVqCtQV6CuQF2BugJ1BeoK1BWoK1BXoK5AXYG6AnUF6grUFagrUFegrkBdgboCdQXqCtQVqCtQV6CuQF2BugJ1BeoK1BWoK1BXoK5AXYG6AnV/H3gHR4Mx2su6OVUAAAAASUVORK5CYII="/>
          </g>
        </g>
      </g>
      <g id="Group_547" data-name="Group 547">
        <g class="cls-16">
          <g id="Group_546" data-name="Group 546">
            <rect id="Rectangle_788" data-name="Rectangle 788" class="cls-9" x="84.94" y="22.13" width="3.72" height="4.3" transform="translate(62.22 111) rotate(-89.8)"/>
          </g>
        </g>
      </g>
      <g id="Group_549" data-name="Group 549">
        <g class="cls-12">
          <g id="Group_548" data-name="Group 548">
            <image id="Rectangle_790" data-name="Rectangle 790" class="cls-5" width="10" height="14" transform="translate(84.41 25.96) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAPCAYAAAAyPTUwAAAACXBIWXMAABbCAAAWwgFTLGrhAAAAM0lEQVQokWMUEZT8z0AEePP+OSMTMQphgCTFLAwMDO9JUfyKZopfDw5nDEXFz4hVTBIAAJttDkLiRqnnAAAAAElFTkSuQmCC"/>
          </g>
        </g>
      </g>
      <g id="Group_551" data-name="Group 551">
        <g class="cls-20">
          <g id="Group_550" data-name="Group 550">
            <image id="Rectangle_792" data-name="Rectangle 792" class="cls-5" width="10" height="18" transform="translate(84.41 85.62) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAASCAYAAACNdSR1AAAACXBIWXMAABbCAAAWwgFTLGrhAAAARElEQVQokWNgoBVgFBGUFCNG4Zv3z1+xMDAwCBNp8CsWBgYGQWKdwcLAwCBEimKSTKad4lE3k62YnVjFTMQqZGBgYAAApTAGD0eAyPUAAAAASUVORK5CYII="/>
          </g>
        </g>
      </g>
      <g id="Group_553" data-name="Group 553">
        <g class="cls-7">
          <g id="Group_552" data-name="Group 552">
            <path id="Path_521" data-name="Path 521" class="cls-9" d="M88.87,98.73H14.38V0h4.3v94.43h65.85v-.13l4.3-.03.03,4.46Z"/>
            <path id="Path_522" data-name="Path 522" class="cls-9" d="M85.81,25.34l-19.63-21.03H17.21V0h50.84l20.9,22.4-3.14,2.94Z"/>
            <rect id="Rectangle_794" data-name="Rectangle 794" class="cls-9" x="66.8" y="18.89" width="4.3" height="10.56" transform="translate(44.36 92.98) rotate(-89.66)"/>
          </g>
        </g>
      </g>
      <g id="Group_555" data-name="Group 555">
        <g class="cls-10">
          <g id="Group_554" data-name="Group 554">
            <image id="Rectangle_796" data-name="Rectangle 796" class="cls-5" width="46" height="46" transform="translate(80.52 45.36) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAAAuCAYAAAC4e0AJAAAACXBIWXMAABbCAAAWwgFTLGrhAAACrElEQVRoge2XUYrUMBjH/2m7agt9CAQhHkG8gXgE8RiCNxBvIHgD8QYL3sAD+OS7T77kJRBway3rzo4PTt1Om7RfvmRWkP1DmQ9m0vx+yTTfDHCXu0SnzHETJfXDpm53/dBd5bgfNcnwSurHAASAsqnbfT90v9KxaEmCV1I/xR9wAChwywJseCX180MpcCMgAFRN3aIfustUuK1UCWPrw6vwXUpqAeDCOrNPQwyHtfJK6pdYrvi8rgCIpm4v+6E7iUA0vJL6Ndahp3UBoGjq9qofuutE1kWi4JXUb+GH/CcCZHgl9fsNyGkdEtj1Q7dLhR5DgldSn6+AUepRoMzZzDbhldSfCGCh9+Z11l6wCq+k/hIBRpXJJhCEV1J/i4QO1aH3kpuZF15J3SWCUcdUTd3i0Aui4UMdtp5NtnbNwWLHAEChpP5unYk6Shcrr6Te46ZDzsHmk+baGVYvOII/gI8RAM4ygFHHRwv8hZ+BT3NGBM4pQGpm5Qb4mCoz5NrnyM2sJIBPBYpbEACIvSD29/y9FbBTnEyFklpYZ3544a0zImL1geUzkEsmOF5JXVhnLhbwAMAQqLDdCzgywR0A4IdnCpQEgehV9o2xznz1ARx95xkCBTb+y6Ze1pnPockXDyxDQABo4FkxT83ZmWC8pw1DAIj7ClFX/WM0fILAAyIYAvUU/MPWZKvnPFPgPlEgKGadeUeZaLNJMQXYzcw684Y6CanDWmcEQPoNNA2lmU1rYZ15FXH/9afZF8Yu7AAMAH4eXr21deZFLEs0PMASuMYx8JGAdeYZh4MFD7AE9vCv+BMuAxseYAkAxyv+KGX+ImXw+CBHpp5cSUla+TGcHWCKHyULPBAnkAMcyAgP0ARygQOZ4ceEJHKCAyeCB5YCucFPHiX1nnmc/v/5DTfzAO++HY/KAAAAAElFTkSuQmCC"/>
          </g>
        </g>
      </g>
      <g id="Group_557" data-name="Group 557">
        <g class="cls-17">
          <g id="Group_556" data-name="Group 556">
            <path id="Path_523" data-name="Path 523" class="cls-9" d="M82.38,68.84l-8.15-8.01,3.02-3.05,5.11,5.02,3.75-3.75,3.05,3.05-6.78,6.74Z"/>
          </g>
        </g>
      </g>
      <g id="Group_559" data-name="Group 559">
        <g class="cls-14">
          <g id="Group_558" data-name="Group 558">
            <image id="Rectangle_799" data-name="Rectangle 799" class="cls-5" width="380" height="382" transform="translate(-50.93 -33.71) scale(.49)" xlink:href="data:image/png;base64,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"/>
          </g>
        </g>
      </g>
      <g id="Group_561" data-name="Group 561">
        <g class="cls-21">
          <g id="Group_560" data-name="Group 560">
            <image id="Rectangle_800" data-name="Rectangle 800" class="cls-5" width="55" height="10" transform="translate(26.2 20.62) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAALCAYAAADbYxWQAAAACXBIWXMAABbCAAAWwgFTLGrhAAAAQ0lEQVRIiWMUEZT8z0AY/GFgYPjNwMDwC0rjYg8G+e8MDAzf37x//pOJCI8NWTDquaEKRj03VMGo54YqGPXcKBiEAACkdjEUx+E2WgAAAABJRU5ErkJggg=="/>
          </g>
        </g>
      </g>
      <g id="Group_563" data-name="Group 563">
        <g class="cls-1">
          <g id="Group_562" data-name="Group 562">
            <image id="Rectangle_802" data-name="Rectangle 802" class="cls-5" width="62" height="10" transform="translate(26.2 33.71) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD4AAAALCAYAAAAndj5aAAAACXBIWXMAABbCAAAWwgFTLGrhAAAAQElEQVRIiWMUEZT8z0Ac+MvAwPAbCf8hwKdEjBb6fsDwm/fP/zIR6elhB0Y9PtLAqMdHGhj1+EgDox4fBSMEAABWBTcU4cYH5AAAAABJRU5ErkJggg=="/>
          </g>
        </g>
      </g>
      <g id="Group_565" data-name="Group 565">
        <g class="cls-13">
          <g id="Group_564" data-name="Group 564">
            <use id="Rectangle_804" data-name="Rectangle 804" class="cls-5" transform="translate(26.2 46.81) scale(.48)" xlink:href="#image"/>
          </g>
        </g>
      </g>
      <g id="Group_567" data-name="Group 567">
        <g class="cls-4">
          <g id="Group_566" data-name="Group 566">
            <use id="Rectangle_806" data-name="Rectangle 806" class="cls-5" transform="translate(26.2 60.39) scale(.49)" xlink:href="#image"/>
          </g>
        </g>
      </g>
      <g id="Group_569" data-name="Group 569">
        <g class="cls-18">
          <g id="Group_568" data-name="Group 568">
            <image id="Rectangle_808" data-name="Rectangle 808" class="cls-5" width="55" height="10" transform="translate(26.2 73.01) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAAKCAYAAAAQP8Y1AAAACXBIWXMAABbCAAAWwgFTLGrhAAAAQUlEQVRIiWMUEZT8z0AY/GFgYPgNxb/QaEJi9NLzC4q/MzAwfH/z/vkvJiI8NmTBqOeGKhj13FAFo54bBaOAvgAAPnIxEOx8mpMAAAAASUVORK5CYII="/>
          </g>
        </g>
      </g>
      <g id="Group_571" data-name="Group 571">
        <g class="cls-6">
          <g id="Group_570" data-name="Group 570">
            <image id="Rectangle_810" data-name="Rectangle 810" class="cls-5" width="10" height="19" transform="translate(63.55 6.06) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAASCAYAAACNdSR1AAAACXBIWXMAABbCAAAWwgFTLGrhAAAAQklEQVQokWNgIAEwighKihKj8M37569ZGBgYhIk0+DULAwODILHOYGFgYBAgRTFJJo86gz4mk6SYn1jFTMQqJFkxALCeBhNajHVyAAAAAElFTkSuQmCC"/>
          </g>
        </g>
      </g>
      <g id="Group_573" data-name="Group 573">
        <g class="cls-2">
          <g id="Group_572" data-name="Group 572">
            <rect id="Rectangle_812" data-name="Rectangle 812" class="cls-9" x="60.22" y="18.47" width="11.17" height="4.3" transform="translate(44.86 86.32) rotate(-89.72)"/>
          </g>
        </g>
      </g>
      <g id="Group_575" data-name="Group 575">
        <g class="cls-19">
          <g id="Group_574" data-name="Group 574">
            <image id="Rectangle_814" data-name="Rectangle 814" class="cls-5" width="18" height="10" transform="translate(74.22 21.59) scale(.49)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAALCAYAAACd1bY6AAAACXBIWXMAABbCAAAWwgFTLGrhAAAAMUlEQVQokWMUEZT8z4AAPxkYGD4wMDC8R6JxsZHFXrx5//wHEwMVwahho4bRwzCqAgCFxxMUYBxpYwAAAABJRU5ErkJggg=="/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>