import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
import { varBounce, MotionContainer } from '@/components/animate';
import { useTranslation } from 'react-i18next';
import Button from '@mui/material/Button';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { checkAnyPermission } from '@/shared/utils';
import { toNumber } from 'lodash';
import { useContext } from 'react';
import { LocationDetailContext, UserPermissionsContext } from '@/core/contexts';

export default function FresthPartnerRelationship({
  onClick,
  countryId,
}: {
  onClick: () => void;
  countryId: string | undefined;
}) {
  const { t } = useTranslation();
  const userPermissions = useContext(UserPermissionsContext);
  const currentlySelected = useContext(LocationDetailContext);
  const hasPermission = checkAnyPermission(
    [PERMISSIONS.GLOBAL_MANAGE, PERMISSIONS.LOCAL_MANAGE_LOCATION],
    userPermissions,
    toNumber(countryId),
    toNumber(currentlySelected.id), // add current location id
  );
  return (
    <MotionContainer
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
      }}
    >
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          {t('messages.setup_partner')}
        </Typography>
      </m.div>

      {hasPermission && (
        <m.div variants={varBounce().in}>
          <Button
            type="submit"
            size="large"
            variant="contained"
            onClick={() => {
              onClick();
            }}
          >
            {t('btn_name.setup_now')}
          </Button>
        </m.div>
      )}
    </MotionContainer>
  );
}
