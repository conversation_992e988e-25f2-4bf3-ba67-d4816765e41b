import { isObject } from "lodash";
import { GraphUserResponse } from "../models"

const getUserLoginId = (userDetail: GraphUserResponse | string): string => {
    if (isObject(userDetail)) {
        const loginId = userDetail.userType == 'Guest' ? userDetail.mail : userDetail.userPrincipalName;
        return loginId || '';

    } else {
        return userDetail;
    }
}

export default getUserLoginId;