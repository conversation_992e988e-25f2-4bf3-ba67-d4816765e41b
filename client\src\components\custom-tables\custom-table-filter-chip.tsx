import { useResponsive } from '@/hooks/use-responsive';
import { useTranslate } from '@/locales/use-locales';
import { KeyValue } from '@/shared/models';
import { trimText } from '@/shared/utils/trim-text.util';
import { Cancel, ExpandLess, ExpandMore, FilterAlt, FilterAltOutlined } from '@mui/icons-material';
import { Box, Button, Chip, Collapse, IconButton, Typography } from '@mui/material';
import { useMemo, useState } from 'react';

interface CustomTableFilterChipProps {
  filters: Record<string, KeyValue[]>;
  onClearFilter: (key: string, value?: string) => void;
  onClearAll: () => void;
  filterConfig: {
    // keys: Record<string, string>;
    displayNames: Record<string, string>;
  };
}

export default function CustomTableFilterChip({
  filters,
  onClearFilter,
  onClearAll,
  filterConfig,
}: CustomTableFilterChipProps) {
  const isSmallScreen = useResponsive('down', 'sm');

  const [expanded, setExpanded] = useState(false);

  const { t } = useTranslate();

  // Memoize filter calculations to prevent unnecessary recalculations
  const { filterCount, activeFilters } = useMemo(() => {
    const activeEntries = Object.entries(filters).filter(([, values]) => values?.length > 0);

    return {
      filterCount: activeEntries.reduce((total, [, values]) => total + values?.length, 0),
      activeFilters: activeEntries.map(([key, values]) => ({
        key,
        values,
        displayName: t(filterConfig.displayNames[key] || key),
      })),
    };
  }, [filters, filterConfig]);

  const toggleExpanded = () => {
    setExpanded((prev) => !prev);
  };

  // Render function for filter chips to avoid duplication
  const renderFilterChips = (fontSize: string) => (
    <>
      {activeFilters?.map(({ key, values, displayName }) => (
        <Box key={key} display="flex" alignItems="center" flexWrap="wrap" gap={1}>
          <Typography variant="value" sx={{ fontSize: 14, whiteSpace: 'nowrap' }}>
            {trimText(displayName?.toString(), 40)}:
          </Typography>
          {values?.map((value) => (
            <Chip
              size="small"
              key={`${key}-${value.id}`}
              label={trimText(value.label)}
              onDelete={() => onClearFilter(key, value.label)}
              deleteIcon={<Cancel />}
              sx={{ fontSize, ...chipStyles }}
              clickable
            />
          ))}
        </Box>
      ))}
    </>
  );

  // Render function for clear button to avoid duplication
  const renderClearButton = (size: 'small' | 'medium', fontSize: string, fullWidth = false) => (
    <Button
      variant="outlined"
      size={size}
      onClick={onClearAll}
      sx={{
        height: '32px',
        px: 1.5,
        py: 0.5,
        mt: expanded ? 1 : 0,
        fontSize,
        width: fullWidth ? '100%' : 'auto',
        bgcolor: 'white',
      }}
    >
      {t('label.clear_filters')}
    </Button>
  );

  return (
    <Box
      display="flex"
      flexDirection={isSmallScreen ? 'column' : 'row'}
      justifyContent="space-between"
      alignItems={isSmallScreen ? 'flex-start' : 'center'}
      gap={1}
      mb={2}
      px={2}
      sx={{
        borderRadius: 1,
        transition: 'all 0.3s ease',
      }}
    >
      {isSmallScreen ? (
        <>
          <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
            <Box display="flex" alignItems="center" mt={1} sx={{ cursor: 'pointer' }} onClick={toggleExpanded}>
              {filterCount > 0 && (
                <IconButton
                  size="small"
                  sx={{ mr: 1 }}
                  aria-label={expanded ? t('label.hide_filter') : t('label.show_filter')}
                >
                  {expanded ? <FilterAlt /> : <FilterAltOutlined />}
                </IconButton>
              )}
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {filterCount > 0 ? `${filterCount} ${t('label.applied_filters')}` : ''}
              </Typography>
            </Box>

            <Box display="flex" alignItems="center">
              {filterCount > 0 && (
                <IconButton onClick={toggleExpanded} size="small">
                  {expanded ? <ExpandLess /> : <ExpandMore />}
                </IconButton>
              )}
            </Box>
          </Box>

          <Collapse in={expanded} sx={{ width: '100%' }}>
            <Box
              display="flex"
              flexDirection="column"
              gap={1}
              sx={{
                p: 1,
                width: '100%',
              }}
            >
              {/* Filter chips */}
              <Box display="flex" flexWrap="wrap" alignItems="center" gap={1}>
                {renderFilterChips('10px')}
              </Box>

              {/* Clear All button aligned right */}
              {filterCount > 0 && (
                <Box display="flex" justifyContent="flex-end" width="100%">
                  {renderClearButton('small', '12px')}
                </Box>
              )}
            </Box>
          </Collapse>
        </>
      ) : (
        // Render for Screens greater than Mobile
        <>
          <Box display="flex" flexWrap="wrap" alignItems="center" gap={1} width="100%">
            {/* All filter chips */}
            {renderFilterChips('12px')}

            {/* Clear Filters button aligned to the right */}
            {filterCount > 0 && <Box sx={{ ml: 'auto' }}>{renderClearButton('medium', '13px')}</Box>}
          </Box>
        </>
      )}
    </Box>
  );
}

// Chip styling configuration to avoid duplication
const chipStyles = {
  '& .MuiChip-deleteIcon': {
    color: 'white',
  },
  borderRadius: '5px',
  border: 1,
};
