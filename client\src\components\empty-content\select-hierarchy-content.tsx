import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
import { TreeViewIllustration } from '@/assets/illustrations';
import { varBounce, MotionContainer } from '@/components/animate';
import { useTranslation } from 'react-i18next';

export default function SelectHierarchyMessage() {
  const { t } = useTranslation();
  return (
    <MotionContainer sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          {t('messages.select_location')}
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary' }}>{t('messages.use_treeview')}</Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <TreeViewIllustration
          sx={{
            height: 150,
            my: { xs: 5, sm: 10 },
          }}
        />
      </m.div>
    </MotionContainer>
  );
}
