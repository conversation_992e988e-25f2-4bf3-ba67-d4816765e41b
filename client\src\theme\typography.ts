// ----------------------------------------------------------------------

import { grey } from './palette';

export function remToPx(value: string) {
  return Math.round(parseFloat(value) * 16);
}

export function pxToRem(value: number) {
  return `${value / 16}rem`;
}

export function responsiveFontSizes({ sm, md, lg }: { sm: number; md: number; lg: number }) {
  return {
    '@media (min-width:600px)': {
      fontSize: pxToRem(sm),
    },
    '@media (min-width:900px)': {
      fontSize: pxToRem(md),
    },
    '@media (min-width:1200px)': {
      fontSize: pxToRem(lg),
    },
  };
}

declare module '@mui/material/styles' {
  interface TypographyVariants {
    fontSecondaryFamily: React.CSSProperties['fontFamily'];
    fontWeightSemiBold: React.CSSProperties['fontWeight'];
    mainTitle: React.CSSProperties;
    subTitle: React.CSSProperties;
    label: React.CSSProperties;
    value: React.CSSProperties;
    valueLigt: React.CSSProperties;
    userSubTitle: React.CSSProperties;
    userTitle: React.CSSProperties;
    tableMainTitile: React.CSSProperties;
    bingoHomeCard: React.CSSProperties;
    partnerBranchTitle: React.CSSProperties;
    partnerBranchBody: React.CSSProperties;
  }

  interface TypographyVariantsOptions {
    mainTitle: React.CSSProperties;
    subTitle: React.CSSProperties;
    label: React.CSSProperties;
    value: React.CSSProperties;
    valueLigt: React.CSSProperties;
    userSubTitle: React.CSSProperties;
    userTitle: React.CSSProperties;
    tableMainTitile: React.CSSProperties;
    bingoHomeCard: React.CSSProperties;
    partnerBranchTitle: React.CSSProperties;
    partnerBranchBody: React.CSSProperties;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    mainTitle: true;
    subTitle: true;
    label: true;
    value: true;
    valueLight: true;
    userSubTitle: true;
    userTitle: true;
    tableMainTitile: true;
    bingoHomeCard: true;
    partnerBranchTitle: true;
    partnerBranchBody: true;
  }
}

export const primaryFont = 'Pilat Demi';
export const secondaryFont = 'Pilat Demi';
export const bolderFont = 'Pilat Heavy';
export const lighterFont = 'Pilat Light';
// ----------------------------------------------------------------------

export const typography: any = {
  fontFamily: primaryFont,
  fontSecondaryFamily: secondaryFont,
  bolderFontFamily: bolderFont,
  lighterFontFamily: lighterFont,
  // fontWeightRegular: 400,
  // fontWeightMedium: 500,
  // fontWeightSemiBold: 600,
  // fontWeightBold: 700,

  mainTitle: {
    fontFamily: bolderFont,
    // lineHeight: 1.5,
    // fontWeight: 600,
    fontSize: pxToRem(21),
    ...responsiveFontSizes({ sm: 18, md: 20, lg: 20 }),
  },
  subTitle: {
    fontWeight: 600,
    fontFamily: primaryFont,
    lineHeight: 22 / 14,
    fontSize: pxToRem(17),
    ...responsiveFontSizes({ sm: 15, md: 16, lg: 16 }),
  },
  label: {
    // lineHeight: 22 / 14,
    fontSize: pxToRem(15),
    fontFamily: primaryFont,
    color: '#87829F!important',
  },
  value: {
    // lineHeight: 18,
    fontSize: pxToRem(15),
    color: 'black',
    fontFamily: primaryFont,
    letterSpacing: 0,
    opacity: 1,
  },
  valueLight: {
    lineHeight: 22 / 14,
    fontSize: pxToRem(14),
    color: grey[600],
  },
  h1: {
    fontWeight: 800,
    lineHeight: 80 / 64,
    fontSize: pxToRem(40),
    ...responsiveFontSizes({ sm: 52, md: 58, lg: 64 }),
  },
  h2: {
    fontWeight: 800,
    lineHeight: 64 / 48,
    fontSize: pxToRem(32),
    ...responsiveFontSizes({ sm: 40, md: 44, lg: 48 }),
  },
  h3: {
    fontWeight: 700,
    lineHeight: 1.5,
    fontSize: pxToRem(24),
    ...responsiveFontSizes({ sm: 26, md: 30, lg: 32 }),
  },
  h4: {
    fontWeight: bolderFont,
    lineHeight: 1.5,
    fontSize: pxToRem(20),
    ...responsiveFontSizes({ sm: 20, md: 24, lg: 24 }),
  },
  h5: {
    fontWeight: 700,
    lineHeight: 1.5,
    fontSize: pxToRem(18),
    ...responsiveFontSizes({ sm: 19, md: 20, lg: 20 }),
  },
  h6: {
    fontWeight: 600,
    lineHeight: 28 / 18,
    fontSize: pxToRem(17),
    ...responsiveFontSizes({ sm: 18, md: 18, lg: 18 }),
  },

  subtitle1: {
    fontFamily: bolderFont,
    lineHeight: 1.5,
    fontSize: pxToRem(17),
    fontWeight: 600,
  },
  partnerBranchTitle: {
    fontFamily: primaryFont,
    lineHeight: 1.5,
    fontSize: pxToRem(16),
  },
  partnerBranchBody: {
    fontFamily: lighterFont,
    lineHeight: 1.3,
    letterSpacing: 0,
    fontSize: pxToRem(12),
    color: '#0F0303',
  },
  subtitle2: {
    fontWeight: 600,
    lineHeight: 22 / 14,
    fontSize: pxToRem(14),
  },

  body1: {
    lineHeight: 1.5,
    fontSize: pxToRem(16),
  },
  body2: {
    lineHeight: 22 / 14,
    fontSize: pxToRem(14),
  },

  caption: {
    lineHeight: 1.5,
    fontSize: pxToRem(14),
    fontFamily: lighterFont,
  },
  overline: {
    fontWeight: 700,
    lineHeight: 1.5,
    fontSize: pxToRem(12),
    textTransform: 'uppercase',
  },
  button: {
    fontWeight: 700,
    lineHeight: 24 / 14,
    fontSize: pxToRem(14),
    textTransform: 'unset',
  },
  userTitle: {
    fontFamily: primaryFont,
    lineHeight: 1.5,
    fontSize: pxToRem(15),
    fontWeight: 600,
  },
  userSubTitle: {
    fontFamily: lighterFont,
    lineHeight: 1.5,
    fontSize: pxToRem(12),
    fontWeight: 600,
    color: grey[600],
  },
  tableMainTitile: {
    fontFamily: secondaryFont,
    lineHeight: 1.5,
    fontSize: pxToRem(15),
    fontWeight: 600,
  },
  bingoHomeCard: {
    // lineHeight: 18,
    fontSize: pxToRem(15),
    fontFamily: primaryFont,
    letterSpacing: 0,
    opacity: 1,
  },
} as const;
