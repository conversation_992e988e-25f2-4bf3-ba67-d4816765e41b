server {
  listen 4200;
  sendfile on;
  include /etc/nginx/mime.types;

  gzip on;
  gzip_min_length   256;
  gzip_vary         on;
  gzip_proxied      any;
  gzip_types        text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript application/ld+json;
  #gzip_comp_level   9;

  root /usr/share/nginx/html;

  location / {
    try_files $uri $uri/ /index.html =404;
  }
}