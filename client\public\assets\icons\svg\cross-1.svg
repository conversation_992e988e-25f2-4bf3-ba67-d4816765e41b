<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 216.7 212.9">
  <!-- Generator: Adobe Illustrator 29.5.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 141)  -->
  <defs>
    <style>
      .st0 {
        fill: url(#linear-gradient2);
      }

      .st1 {
        fill: url(#linear-gradient1);
      }

      .st2 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="6851.1" y1="1838.3" x2="6851.1" y2="2040" gradientTransform="translate(6961.1 -1832.3) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff3c14"/>
      <stop offset=".2" stop-color="#ff3c14" stop-opacity="1"/>
      <stop offset=".3" stop-color="#ff3c14" stop-opacity=".9"/>
      <stop offset=".4" stop-color="#ff3c14" stop-opacity=".9"/>
      <stop offset=".5" stop-color="#ff3c14" stop-opacity=".8"/>
      <stop offset=".6" stop-color="#ff3c14" stop-opacity=".6"/>
      <stop offset=".7" stop-color="#ff3c14" stop-opacity=".5"/>
      <stop offset=".8" stop-color="#ff3c14" stop-opacity=".3"/>
      <stop offset=".9" stop-color="#ff3c14" stop-opacity=".1"/>
      <stop offset="1" stop-color="#ff3c14" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient1" x1="1744.3" y1="-2068.3" x2="1744.3" y2="-1946.7" gradientTransform="translate(2117.5 -1638.6) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff3c14" stop-opacity="0"/>
      <stop offset=".5" stop-color="#ff3c14"/>
      <stop offset=".6" stop-color="#ff3c14" stop-opacity="1"/>
      <stop offset=".6" stop-color="#ff3c14" stop-opacity=".9"/>
      <stop offset=".7" stop-color="#ff3c14" stop-opacity=".8"/>
      <stop offset=".8" stop-color="#ff3c14" stop-opacity=".6"/>
      <stop offset=".9" stop-color="#ff3c14" stop-opacity=".4"/>
      <stop offset=".9" stop-color="#ff3c14" stop-opacity=".2"/>
      <stop offset="1" stop-color="#ff3c14" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient2" x1="6511.1" y1="-774" x2="6511.1" y2="-652.4" gradientTransform="translate(6621.1 819) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff3c14" stop-opacity="0"/>
      <stop offset=".5" stop-color="#ff3c14"/>
      <stop offset=".6" stop-color="#ff3c14" stop-opacity="1"/>
      <stop offset=".6" stop-color="#ff3c14" stop-opacity=".9"/>
      <stop offset=".7" stop-color="#ff3c14" stop-opacity=".8"/>
      <stop offset=".8" stop-color="#ff3c14" stop-opacity=".6"/>
      <stop offset=".9" stop-color="#ff3c14" stop-opacity=".4"/>
      <stop offset=".9" stop-color="#ff3c14" stop-opacity=".2"/>
      <stop offset="1" stop-color="#ff3c14" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="st2" d="M110,207.6c-55.7,0-100.8-45.2-100.8-100.9S54.3,5.9,110,5.9s100.9,45.2,100.9,100.8c0,55.7-45.2,100.8-100.9,100.9ZM110,14.8c-50.8,0-92,41.2-92,92s41.2,92,92,92,92-41.2,92-92h0c0-50.8-41.2-92-92-92Z"/>
  <rect class="st1" x="49.2" y="101.4" width="121.6" height="8.9" transform="translate(-42.6 108.8) rotate(-45)"/>
  <rect class="st0" x="105.6" y="45" width="8.9" height="121.6" transform="translate(-42.6 108.8) rotate(-45)"/>
</svg>