<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 266.29 222.54">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-15);
      }

      .cls-2 {
        fill: url(#linear-gradient-13);
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }

      .cls-4 {
        fill: url(#linear-gradient-10);
      }

      .cls-5 {
        fill: url(#linear-gradient-12);
      }

      .cls-6 {
        fill: url(#linear-gradient-4);
      }

      .cls-7 {
        fill: url(#linear-gradient-3);
      }

      .cls-8 {
        fill: url(#linear-gradient-5);
      }

      .cls-9 {
        fill: url(#linear-gradient-8);
      }

      .cls-10 {
        fill: url(#linear-gradient-14);
      }

      .cls-11 {
        fill: url(#linear-gradient-7);
      }

      .cls-12 {
        fill: url(#linear-gradient-9);
      }

      .cls-13 {
        fill: url(#linear-gradient-11);
      }

      .cls-14 {
        fill: url(#linear-gradient-6);
      }

      .cls-15 {
        fill: url(#linear-gradient);
      }

      .cls-16 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="186" y1="657.41" x2="186" y2="707.59" gradientTransform="translate(29.58 833.94) rotate(-4.64) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".37" stop-color="#0f0f19"/>
      <stop offset=".73" stop-color="#0f0f19" stop-opacity=".44"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="218.27" y1="657.47" x2="218.27" y2="707.63" gradientTransform="translate(-90.19 778.42) rotate(4.64) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".37" stop-color="#0f0f19"/>
      <stop offset=".73" stop-color="#0f0f19" stop-opacity=".44"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="202.14" y1="702.81" x2="202.14" y2="720.34" gradientTransform="translate(510.87 752.74) rotate(-45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".58" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="150.96" y1="784.3" x2="253.31" y2="784.3" gradientTransform="translate(-102.93 839.53) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".08" stop-color="#0f0f19" stop-opacity=".09"/>
      <stop offset=".32" stop-color="#0f0f19" stop-opacity=".76"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".69" stop-color="#0f0f19" stop-opacity=".81"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".27"/>
      <stop offset=".96" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="150.96" y1="725.75" x2="253.31" y2="725.75" gradientTransform="translate(-61.53 822.38) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".08" stop-color="#0f0f19" stop-opacity=".09"/>
      <stop offset=".32" stop-color="#0f0f19" stop-opacity=".76"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".69" stop-color="#0f0f19" stop-opacity=".81"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".27"/>
      <stop offset=".96" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="556.23" y1="-27.9" x2="630.61" y2="-27.9" gradientTransform="translate(109.65 -480.48) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".53"/>
      <stop offset=".28" stop-color="#0f0f19" stop-opacity=".89"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".75" stop-color="#0f0f19" stop-opacity=".9"/>
      <stop offset=".88" stop-color="#0f0f19" stop-opacity=".57"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="1117.98" y1="1264.04" x2="1192.36" y2="1264.04" gradientTransform="translate(1420.96 1192.94) rotate(-90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".12" stop-color="#0f0f19" stop-opacity=".53"/>
      <stop offset=".28" stop-color="#0f0f19" stop-opacity=".89"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".75" stop-color="#0f0f19" stop-opacity=".9"/>
      <stop offset=".88" stop-color="#0f0f19" stop-opacity=".57"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="138.19" y1="788.1" x2="145.53" y2="788.1" gradientTransform="translate(-88.07 883.24) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="138.19" y1="723.69" x2="145.53" y2="723.69" gradientTransform="translate(-42.31 864.42) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="1430.13" y1="226.02" x2="1437.46" y2="226.02" gradientTransform="translate(1618.31 -170.72) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="1430.13" y1="161.57" x2="1437.46" y2="161.57" gradientTransform="translate(1572.72 -152.05) rotate(179.99) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="123.91" y1="36.77" x2="123.91" y2="29.44" gradientTransform="translate(0 228.97) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="13.15" y1="36.77" x2="13.15" y2="29.44" gradientTransform="translate(0 228.97) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="132.63" y1="19.03" x2="132.63" y2="6.44" gradientTransform="translate(0 228.97) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="4.43" y1="19.03" x2="4.43" y2="6.44" gradientTransform="translate(0 228.97) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#0f0f19"/>
      <stop offset=".55" stop-color="#0f0f19" stop-opacity=".61"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <rect class="cls-15" x="155.32" y="113.82" width="8.87" height="49.63" transform="translate(-51.75 136.45) rotate(-40.36)"/>
  <path class="cls-16" d="M237.25,222.27h-.69c-6.89-.1-13.43-3.06-18.05-8.17l-.08-.08-46-53.6,6.73-5.77,45.96,53.54c3,3.27,7.22,5.15,11.66,5.2,11.34.02,20.56-9.16,20.58-20.5,0-.03,0-.05,0-.08-.06-4.44-1.94-8.65-5.2-11.66l-53.57-46,5.77-6.73,53.76,46.07c5.09,4.62,8.04,11.13,8.17,18,.14,16.19-12.82,29.45-29,29.69l-.04.09Z"/>
  <rect class="cls-3" x="178.15" y="90.96" width="8.87" height="49.6" transform="translate(-23.86 179.92) rotate(-49.64)"/>
  <path class="cls-7" d="M133.49,128.91c-4.64,0-9.09-1.84-12.37-5.12l6.27-6.28c3.38,3.38,8.85,3.38,12.23,0h0s21.88-21.89,21.88-21.89c3.38-3.38,3.38-8.85,0-12.23,0,0,0,0,0,0l6.27-6.27c6.83,6.85,6.83,17.94,0,24.79l-21.9,21.88c-3.29,3.28-7.74,5.13-12.39,5.12Z"/>
  <rect class="cls-6" x="48.03" y="50.79" width="102.35" height="8.87" transform="translate(-9.99 86.33) rotate(-45)"/>
  <rect class="cls-8" x="89.43" y="92.19" width="102.35" height="8.87" transform="translate(-27.14 127.73) rotate(-45)"/>
  <rect class="cls-14" x="77.31" y="75.75" width="8.87" height="74.38" transform="translate(-55.92 90.89) rotate(-45)"/>
  <rect class="cls-11" x="152.48" y=".58" width="8.87" height="74.38" transform="translate(19.26 122.02) rotate(-45)"/>
  <rect class="cls-9" x="50.25" y="90.74" width="7.33" height="8.87" transform="translate(-51.51 66.02) rotate(-45.01)"/>
  <path class="cls-16" d="M90.05,150.49h-.91c-4.05-.27-7.86-2.01-10.7-4.9l-29.32-29.4c-2.89-2.84-4.63-6.65-4.9-10.7-.3-4.03,1.16-7.99,4-10.86l6.27,6.28c-1.04,1.08-1.56,2.56-1.43,4.06.13,1.9.96,3.68,2.32,5l29.36,29.35c1.32,1.37,3.1,2.19,5,2.32,1.49.13,2.98-.38,4.06-1.42l6.27,6.27c-2.66,2.63-6.28,4.07-10.02,4Z"/>
  <rect class="cls-12" x="95.78" y="136.27" width="7.33" height="8.87" transform="translate(-70.37 111.5) rotate(-44.99)"/>
  <rect class="cls-4" x="180.85" y="50.86" width="7.33" height="8.87" transform="translate(14.94 146.67) rotate(-45)"/>
  <path class="cls-16" d="M190.24,55.84l-6.27-6.27c1.04-1.09,1.56-2.56,1.43-4.06-.13-1.9-.96-3.68-2.32-5l-29.36-29.31c-1.32-1.36-3.1-2.19-5-2.32-1.5-.13-2.98.39-4.06,1.43l-6.27-6.27c2.87-2.83,6.83-4.29,10.85-4,4.05.27,7.86,2.01,10.7,4.9l29.36,29.36c2.89,2.84,4.63,6.65,4.9,10.7.3,4.01-1.14,7.96-3.96,10.84Z"/>
  <rect class="cls-13" x="135.31" y="5.34" width="7.33" height="8.87" transform="translate(33.77 101.12) rotate(-44.99)"/>
  <rect class="cls-5" x="119.48" y="192.21" width="8.87" height="7.33"/>
  <path class="cls-16" d="M128.35,192.21h-8.87c0-5.26-7.79-5.67-10.18-5.67H27.76c-2.38,0-10.17.41-10.17,5.67h-8.87c0-8.83,7.47-14.54,19-14.54h81.54c11.62,0,19.09,5.71,19.09,14.54Z"/>
  <rect class="cls-2" x="8.72" y="192.21" width="8.87" height="7.33"/>
  <rect class="cls-10" x="128.2" y="209.95" width="8.87" height="12.59"/>
  <path class="cls-16" d="M137.12,209.95h-8.87c0-4.48-7.84-5.67-12.48-5.67H21.35c-4.64,0-12.48,1.19-12.48,5.67H0c0-8.83,8.38-14.54,21.35-14.54h94.37c12.97,0,21.4,5.71,21.4,14.54Z"/>
  <rect class="cls-1" y="209.95" width="8.87" height="12.59"/>
</svg>