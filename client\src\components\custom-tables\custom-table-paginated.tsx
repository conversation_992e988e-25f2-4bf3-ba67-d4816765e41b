import React, { useState } from 'react';
import {
  Box,
  IconButton,
  MenuItem,
  Popover,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
} from '@mui/material';

import { TableNoData } from '../table';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import { useResponsive } from '@/hooks/use-responsive';

interface CustomPaginatedTableProps {
  tableHeaders: string[];
  tableData: object[];
  isPaginated: boolean;
  rowsPerPage?: number;
  page?: number;
  trimColumnName?: string;
  linkableColumnKey?: string;
  noDataMessage?: string;
  handleChangePage?: (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => void;
  handleChangeRowsPerPage?: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any) => void;
  renderCustomTableCell: (
    row: any,
    tableHeaders: string[],
    trimColumnName?: string,
    linkableColumnKey?: string,
  ) => React.ReactNode;
  actionOptions?: Array<{ title: string; icon?: React.ReactNode; onClick: (row: any, rowIndex?: number) => void }>;
  totalRows?: number;
}

const CustomPaginatedTable: React.FC<CustomPaginatedTableProps> = ({
  tableHeaders,
  tableData,
  rowsPerPage = 10,
  page = 0,
  totalRows,
  isPaginated = false,
  trimColumnName,
  handleChangePage,
  handleChangeRowsPerPage,
  renderCustomTableCell = () => null,
  actionOptions = [],
  linkableColumnKey,
  noDataMessage = 'No Data Found',
}) => {
  const isMobile = useResponsive('down', 'sm');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRow, setSelectedRow] = useState<any>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>, row: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedRow(row);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedRow(null);
  };

  const handleActionClick = (action: any, rowIndex: number) => {
    action.onClick?.(selectedRow, rowIndex);
    handleClose();
  };

  return tableData?.length ? (
    <>
      <TableContainer sx={{ overflowX: 'auto' }}>
        <Table sx={{ borderCollapse: 'separate', borderSpacing: '0 2px' }}>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              {tableHeaders?.map((header) => (
                <TableCell
                  key={header}
                  sx={{
                    textWrap: 'nowrap',
                    border: '1px solid #ddd',
                    fontSize: '14px',
                    fontWeight: 'bolder',
                    py: 1,
                    color: 'black',
                  }}
                >
                  {header}
                </TableCell>
              ))}
              {actionOptions?.length > 0 && (
                <TableCell sx={{ border: '1px solid #ddd', fontWeight: 'bold', py: 1, color: 'black' }}>
                  Action
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {tableData?.map((row: any, rowIndex) => (
              <TableRow
                key={rowIndex}
                sx={{
                  '&:hover': { backgroundColor: '#f0f0f0' },
                  '& td': { py: 1 },
                }}
              >
                {renderCustomTableCell(row, tableHeaders, trimColumnName, linkableColumnKey)}
                {actionOptions?.length > 0 && (
                  <TableCell sx={{ py: 1 }}>
                    <IconButton
                      size="small"
                      onClick={(event) => {
                        // Toggle popover if clicking the same button
                        if (anchorEl && selectedRow === row) {
                          handleClose();
                        } else {
                          handleClick(event, row);
                        }
                      }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                    <Popover
                      open={Boolean(anchorEl) && selectedRow === row}
                      anchorEl={anchorEl}
                      onClose={handleClose}
                      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                      transformOrigin={{ vertical: 'top', horizontal: 'right' }}
                      sx={{ '& .MuiPaper-root': { minWidth: 150 } }}
                      // These props ensure proper closing behavior
                      disableRestoreFocus
                      disableScrollLock
                      onClick={(e) => e.stopPropagation()} // Prevent clicks inside from closing
                    >
                      {actionOptions.map((option, actionIndex) => (
                        <MenuItem key={actionIndex} onClick={() => handleActionClick(option, rowIndex)}>
                          {option.title}
                        </MenuItem>
                      ))}
                    </Popover>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {isPaginated && (
        <Box
          display="flex"
          flexDirection={isMobile ? 'column' : 'row'}
          justifyContent="space-between"
          alignItems="center"
          sx={{ borderTop: '1px solid #ddd', mt: 1, px: 1, pt: 1 }}
        >
          <Box display="flex" alignItems="center" sx={{ mb: isMobile ? 1 : 0 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1, whiteSpace: 'nowrap' }}>
              List per page
            </Typography>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={totalRows ?? tableData.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage || (() => {})}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage=""
              sx={{
                '& .MuiTablePagination-toolbar': { paddingLeft: 0, minHeight: '32px' },
                '& .MuiTablePagination-selectLabel': { display: 'none' },
                '& .MuiTablePagination-displayedRows': { display: 'none' },
                '& .MuiTablePagination-actions': { display: 'none' },
              }}
            />
          </Box>
          <Box>
            <TablePagination
              rowsPerPageOptions={[]}
              component="div"
              count={totalRows ?? tableData?.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage || (() => {})}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage=""
              labelDisplayedRows={({ from, to, count }) =>
                `${from} to ${to} record${to === from ? '' : 's'} of ${count}`
              }
              sx={{
                '& .MuiTablePagination-selectLabel': { display: 'none' },
                '& .MuiTablePagination-select': { display: 'none' },
                '& .MuiTablePagination-displayedRows': { marginRight: 1 },
              }}
            />
          </Box>
        </Box>
      )}
    </>
  ) : (
    <Box
      sx={{
        height: '350px',
        justifyContent: 'center',
      }}
    >
      <TableNoData notFound={true} message={noDataMessage} />
    </Box>
  );
};

export default CustomPaginatedTable;
