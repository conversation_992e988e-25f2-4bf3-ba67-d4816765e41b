###################
# BUILD FOR LOCAL DEVELOPMENT
###################

# FROM node:16.16.0-alpine As development
FROM node:22.17.0-alpine3.22  As development

WORKDIR /usr/src/app

COPY --chown=node:node package*.json ./

# Note: this installs the necessary libs to make the bundled version of Chromium that Puppeteer
# installs, work.
RUN apk update && apk add --no-cache \
    nss \
    freetype \
    harfbuzz \
    ca-certificates

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true

RUN npm ci --legacy-peer-deps

COPY --chown=node:node . .

USER node

###################
# BUILD FOR PRODUCTION
###################

FROM node:22.17.0-alpine3.22  As build

WORKDIR /usr/src/app

COPY --chown=node:node package*.json ./

COPY --chown=node:node --from=development /usr/src/app/node_modules ./node_modules

COPY --chown=node:node . .

RUN npm install -g @nestjs/cli

RUN npm run build

ENV NODE_ENV production

# Note: this installs the necessary libs to make the bundled version of Chromium that Puppeteer
# installs, work.
RUN apk update && apk add --no-cache \
    nss \
    freetype \
    harfbuzz \
    ca-certificates

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true

RUN npm ci --legacy-peer-deps --only=production && npm cache clean --force

USER node

###################
# PRODUCTION
###################

FROM node:22.17.0-alpine3.22 As production

ENV NODE_ENV prod

ENV PORT 1210

COPY --chown=node:node --from=build /usr/src/app/node_modules ./node_modules
COPY --chown=node:node --from=build /usr/src/app/dist ./dist

# Note: this installs the necessary libs to make the bundled version of Chromium that Puppeteer
# installs, work.
RUN apk update && apk add --no-cache \
    nss \
    freetype \
    harfbuzz \
    ca-certificates

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true

EXPOSE 1210

CMD [ "node", "dist/main.js" ]