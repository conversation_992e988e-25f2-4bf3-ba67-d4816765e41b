import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { Default } from 'sequelize-typescript';

export class AttachmentResponseDto {
	@ApiProperty({ type: Number })
	@Expose()
	public id: number;

	@ApiProperty({ type: String })
	@Expose()
	@IsOptional()
	attachment_name?: string;

	@ApiProperty({ type: Number })
	@Expose()
	entity_id: number;

	@ApiProperty({ type: String })
	@Expose()
	@IsOptional()
	meta_data_1?: string;

	@ApiProperty({ type: String })
	@Expose()
	@IsOptional()
	meta_data_2?: string;

	@ApiProperty({ type: String })
	@Expose()
	@IsOptional()
	meta_data_3?: string;

	@ApiProperty({ type: String })
	@Expose()
	@IsOptional()
	description?: string;

	@ApiProperty({ type: Object })
	@Expose()
	@IsOptional()
	additional_info?: object;

	@ApiProperty({ type: String })
	@Expose()
	@IsOptional()
	file_id?: string;

	@ApiProperty({ type: Buffer })
	@Expose()
	contents: Buffer;

	@ApiProperty({ type: String })
	@Expose()
	attachment_content_type: string;

	@ApiProperty({ type: String })
	@Expose()
	created_by: string;

	@ApiProperty({ type: Date })
	@Expose()
	created_on: Date;

	@ApiProperty({ type: Boolean })
	@Expose()
	@IsOptional()
	isNew?: boolean;

	@ApiProperty({ type: Boolean })
	@Expose()
	@IsOptional()
	isEdited?: boolean;
}
