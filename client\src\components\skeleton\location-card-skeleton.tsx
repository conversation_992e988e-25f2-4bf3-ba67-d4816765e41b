import { Box, Card, IconButton, Skeleton, Stack } from '@mui/material';
import React from 'react';

interface MobileViewLocationCardSkeletonProps {
  rowsPerPage?: number;
}

const MobileViewLocationCardSkeleton: React.FC<MobileViewLocationCardSkeletonProps> = ({ rowsPerPage = 10 }) => {
  return (
    <Box sx={{ display: { xs: 'block', md: 'none' }, mt: 2, p: 2 }}>
      {Array.from({ length: rowsPerPage }).map((_, index) => (
        <Card
          key={index}
          sx={{
            mb: 2,
            p: 2,
            borderRadius: '7px',
            border: 'solid 1px #E8D6D6',
            position: 'relative',
            '&:hover': { boxShadow: 3 },
          }}
        >
          <IconButton
            disabled
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <Skeleton variant="circular" width={24} height={24} />
          </IconButton>
          <Stack spacing={2}>
            <Box gap={2} display="flex" justifyContent="space-between" alignItems="center">
              <Skeleton width="60%" height={24} />
            </Box>

            {[...Array(4)].map((_, i) => (
              <Box key={i} display="flex" flexDirection="column" gap={1}>
                <Skeleton width="40%" height={16} /> {/* Label */}
                <Skeleton width="80%" height={20} /> {/* Value */}
              </Box>
            ))}

            <Box display="flex" justifyContent="space-between" gap={2}>
              {[...Array(2)].map((_, i) => (
                <Box key={i} width="48%" display="flex" flexDirection="column" gap={1}>
                  <Skeleton width="60%" height={16} /> {/* Label */}
                  <Skeleton width="100%" height={20} /> {/* Value */}
                </Box>
              ))}
            </Box>
          </Stack>
        </Card>
      ))}

      <Box gap={2} sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
        <Skeleton variant="rounded" width={160} height={32} />
      </Box>
    </Box>
  );
};

export default MobileViewLocationCardSkeleton;
