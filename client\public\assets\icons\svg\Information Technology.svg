<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 305.49 230.73">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-10);
      }

      .cls-3 {
        fill: url(#linear-gradient-4);
      }

      .cls-4 {
        fill: url(#linear-gradient-3);
      }

      .cls-5 {
        fill: url(#linear-gradient-5);
      }

      .cls-6 {
        fill: url(#linear-gradient-8);
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: url(#linear-gradient-9);
      }

      .cls-9 {
        fill: url(#linear-gradient-6);
      }

      .cls-10 {
        fill: url(#linear-gradient);
      }

      .cls-11 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="173.23" y1="232.73" x2="173.23" y2="123.18" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="129.02" y1="-9.23" x2="179.08" y2="50.43" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="115.63" y1="153.57" x2="115.63" y2="232.73" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".85" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="115.63" y1="81.16" x2="115.63" y2="2" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".85" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="171.01" y1="80.39" x2="193.17" y2="80.39" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="63.33" y1="80.39" x2="15.14" y2="80.39" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="167.93" y1="154.34" x2="216.01" y2="154.34" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="63.33" y1="154.34" x2="15.14" y2="154.34" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="247.55" y1="133.19" x2="247.55" y2="8.89" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".71" stop-color="#0f0f19" stop-opacity=".38"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="247.55" y1="95.96" x2="247.55" y2="46.13" gradientTransform="translate(0 232.73) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".71" stop-color="#0f0f19" stop-opacity=".38"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-10" d="M221.97,109.55c-3.06-56.48-49.77-100.71-106.34-100.68V0c61.28-.03,111.88,47.88,115.2,109.07l-8.86.48Z"/>
  <path class="cls-11" d="M115.63,230.73C51.92,230.88.15,179.35,0,115.63-.15,51.92,51.39.15,115.1,0c.18,0,.35,0,.53,0v8.87c-58.82-.08-106.56,47.53-106.64,106.35-.08,58.82,47.53,106.56,106.35,106.64.1,0,.2,0,.29,0v8.87Z"/>
  <path class="cls-1" d="M115.63,230.73v-8.87c27.84.06,54.58-10.83,74.44-30.34l6.2,6.34c-21.52,21.13-50.48,32.93-80.64,32.86Z"/>
  <path class="cls-4" d="M163.56,79.16c-7.43-42.04-26.69-70.29-47.93-70.29s-40.5,28.25-47.93,70.29l-8.73-1.54c3.9-22.05,11.05-41.04,20.68-54.89C89.97,7.86,102.42,0,115.63,0s25.66,7.86,35.99,22.72c9.63,13.86,16.78,32.84,20.68,54.89l-8.73,1.54Z"/>
  <path class="cls-3" d="M115.63,230.73c-13.22,0-25.66-7.86-35.99-22.72-9.63-13.86-16.78-32.84-20.68-54.89l8.73-1.54c7.43,42.04,26.69,70.29,47.93,70.29s40.5-28.25,47.93-70.29l8.73,1.54c-3.9,22.05-11.05,41.04-20.68,54.89-10.33,14.87-22.78,22.72-35.99,22.72Z"/>
  <rect class="cls-5" x="171.01" y="147.91" width="22.15" height="8.87"/>
  <rect class="cls-9" x="15.14" y="147.91" width="48.19" height="8.87"/>
  <path class="cls-11" d="M59.61,156.78l-.65-3.66c-4.3-24.98-4.3-50.52,0-75.5l.65-3.66h104.32s0,0,0,0h7.72s.65,3.66.65,3.66c4.3,24.98,4.3,50.51,0,75.5l-.65,3.66H59.61ZM67.09,82.82c-3.3,21.57-3.3,43.51,0,65.08h97.09c3.3-21.57,3.3-43.51,0-65.08h-97.09Z"/>
  <rect class="cls-7" x="167.93" y="73.95" width="48.08" height="8.87"/>
  <rect class="cls-6" x="15.14" y="73.95" width="48.19" height="8.87"/>
  <path class="cls-8" d="M247.55,223.83c-2.6.05-5.21-.13-7.78-.54-3.88-.55-7.03-3.4-7.97-7.21-.51-1.82-1.18-4.34-1.85-6.86-.67-2.49-1.33-4.98-1.83-6.78-.23-.82-.64-1.57-1.19-2.22l-.08-.06c-.33-.28-.68-.53-1.04-.76l-.05-.03c-.38-.2-.78-.38-1.19-.53l-.11-.04c-.83-.14-1.68-.11-2.5.09-1.81.47-4.3,1.14-6.79,1.81-2.52.68-5.04,1.35-6.86,1.82-3.76,1.09-7.81-.22-10.23-3.31-3.2-4.11-5.81-8.64-7.77-13.47-1.46-3.63-.57-7.79,2.26-10.5,1.33-1.35,3.18-3.21,5.04-5.06,1.82-1.81,3.63-3.62,4.93-4.95.59-.61,1.04-1.34,1.33-2.14v-.11c.09-.42.13-.85.15-1.28-.01-.45-.06-.89-.13-1.33l-.02-.14c-.29-.79-.74-1.51-1.32-2.12-1.3-1.33-3.12-3.14-4.93-4.95-1.85-1.85-3.71-3.7-5.04-5.06-2.83-2.71-3.72-6.87-2.25-10.51,1.96-4.82,4.58-9.36,7.78-13.47,2.41-3.09,6.46-4.39,10.22-3.3,1.83.47,4.34,1.15,6.86,1.82,2.49.67,4.99,1.34,6.8,1.81.82.21,1.68.23,2.52.08l.09-.04c.41-.15.8-.32,1.18-.53.38-.23.74-.5,1.09-.78l.11-.08c.54-.65.94-1.4,1.17-2.21.5-1.79,1.17-4.28,1.83-6.77.67-2.52,1.35-5.05,1.86-6.87.94-3.81,4.09-6.66,7.97-7.2,5.16-.71,10.39-.71,15.55,0,3.88.55,7.03,3.4,7.97,7.2.51,1.82,1.18,4.34,1.85,6.87.66,2.49,1.33,4.98,1.83,6.78.23.82.64,1.57,1.19,2.22l.07.06c.66.59,1.44,1.04,2.28,1.32l.12.05c.83.14,1.68.11,2.5-.09,1.81-.46,4.29-1.13,6.78-1.8,2.52-.68,5.04-1.36,6.88-1.83,3.76-1.09,7.81.22,10.23,3.31,3.2,4.11,5.81,8.64,7.77,13.47,1.46,3.63.57,7.79-2.26,10.5-1.31,1.34-3.15,3.17-4.99,5.01-1.83,1.83-3.67,3.66-4.98,5-.59.61-1.04,1.34-1.33,2.14v.1c-.19.86-.19,1.75-.01,2.61l.02.13c.29.79.74,1.51,1.32,2.12,1.31,1.34,3.14,3.17,4.98,5,1.84,1.84,3.68,3.67,5,5.02,2.83,2.71,3.72,6.88,2.25,10.51-1.96,4.82-4.58,9.36-7.78,13.47-2.41,3.08-6.46,4.39-10.22,3.3-1.83-.47-4.35-1.15-6.87-1.83-2.49-.67-4.98-1.34-6.78-1.8-.82-.2-1.68-.23-2.52-.08l-.09.04c-.41.15-.8.32-1.18.53-.39.24-.75.5-1.1.79l-.09.07c-.54.65-.94,1.4-1.18,2.21-.5,1.8-1.17,4.29-1.83,6.78-.67,2.52-1.35,5.04-1.85,6.87-.94,3.81-4.09,6.66-7.98,7.2-2.57.4-5.17.58-7.77.53ZM230.21,191.72c.75.44,1.47.94,2.14,1.5l.1.08c2.03,1.81,3.49,4.16,4.21,6.78.51,1.83,1.18,4.35,1.86,6.88.67,2.49,1.33,4.98,1.83,6.77.19.69.4.76.81.82,4.24.57,8.53.57,12.77,0,.42-.07.63-.13.82-.82.5-1.8,1.17-4.29,1.83-6.78.67-2.52,1.35-5.05,1.86-6.87.72-2.61,2.18-4.96,4.2-6.77l.1-.08c1.34-1.13,2.87-2.02,4.52-2.61l.11-.04c2.58-.86,5.35-.95,7.98-.26,1.83.47,4.36,1.15,6.88,1.83,2.49.67,4.98,1.34,6.78,1.8.7.18.85.03,1.12-.29,2.61-3.39,4.76-7.1,6.39-11.06.15-.4.2-.61-.3-1.12-1.31-1.33-3.12-3.14-4.93-4.95-1.86-1.85-3.71-3.71-5.04-5.07-1.9-1.93-3.21-4.37-3.76-7.02l-.02-.15c-.14-.87-.22-1.74-.23-2.62,0-.86.08-1.72.23-2.56l.02-.13c.55-2.66,1.86-5.11,3.76-7.04,1.33-1.36,3.19-3.22,5.05-5.07,1.81-1.81,3.62-3.62,4.92-4.94.5-.51.46-.72.3-1.11-1.63-3.95-3.77-7.67-6.38-11.06-.27-.33-.43-.48-1.12-.3-1.81.47-4.3,1.14-6.79,1.8-2.52.68-5.04,1.36-6.88,1.83-2.62.68-5.39.6-7.96-.25l-.13-.05c-1.65-.6-3.18-1.48-4.52-2.61l-.09-.07c-2.03-1.8-3.49-4.16-4.21-6.78-.51-1.83-1.18-4.35-1.86-6.87-.66-2.49-1.33-4.98-1.83-6.78-.19-.69-.4-.76-.81-.82-4.24-.57-8.53-.57-12.77,0-.42.07-.63.13-.82.82-.5,1.8-1.17,4.29-1.83,6.78-.67,2.52-1.35,5.05-1.86,6.87-.72,2.61-2.18,4.97-4.2,6.77l-.12.09c-1.34,1.12-2.86,2-4.5,2.6l-.11.04c-2.58.86-5.35.95-7.98.26-1.84-.47-4.37-1.15-6.89-1.83-2.48-.67-4.97-1.34-6.77-1.8-.7-.18-.86-.03-1.12.29-2.61,3.39-4.76,7.1-6.38,11.06-.15.4-.2.61.3,1.12,1.32,1.34,3.15,3.17,4.97,5,1.84,1.84,3.68,3.68,5,5.02,1.9,1.93,3.21,4.37,3.76,7.02l.02.15c.3,1.71.3,3.47,0,5.18l-.02.13c-.55,2.66-1.85,5.11-3.76,7.04-1.32,1.35-3.16,3.18-5,5.02-1.83,1.83-3.66,3.66-4.98,5-.5.51-.46.72-.3,1.11,1.63,3.95,3.77,7.67,6.38,11.06.27.33.42.48,1.12.3,1.81-.47,4.29-1.13,6.78-1.8,2.53-.68,5.05-1.36,6.89-1.83,2.62-.68,5.39-.6,7.96.25l.12.05c.82.31,1.61.67,2.37,1.1Z"/>
  <path class="cls-2" d="M247.55,186.6c-13.76,0-24.91-11.15-24.91-24.91,0-13.76,11.15-24.91,24.91-24.91,13.76,0,24.91,11.15,24.91,24.91h0c-.02,13.75-11.16,24.9-24.91,24.91ZM247.55,145.65c-8.86,0-16.04,7.18-16.04,16.04s7.18,16.04,16.04,16.04c8.86,0,16.04-7.18,16.04-16.04-.01-8.86-7.19-16.03-16.04-16.04h0Z"/>
</svg>