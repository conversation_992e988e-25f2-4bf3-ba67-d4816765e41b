import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';

import { Box, Grid2 as Grid, Button, Typography, InputLabel } from '@mui/material';
import { alpha } from '@mui/material/styles';

import Iconify from '../iconify';
import { UploadProps } from './types';
import MultiFilePreview from './preview-multi-file';
import RejectionFiles from './errors-rejection-files';
import SingleFilePreview from './preview-single-file';
import UploadIllustration from '@/assets/illustrations/upload-illustration';
import EmptyContent from '../empty-content';
import { t } from 'i18next';
import { isArray } from 'lodash';

// ----------------------------------------------------------------------

export default function CustomUpload({
  uploadFilesTitle,
  previewFilesTitle,
  disabled,
  multiple = false,
  error,
  helperText,
  //
  file,
  onDelete,
  //
  files,
  thumbnail,
  onUpload,
  onRemove,
  onRemoveAll,
  sx,
  emptyContentText = 'No document uploaded',
  ...other
}: UploadProps) {
  const [fileRejections, setFileRejections] = useState<any[]>([]);

  const onDropRejected = useCallback((rejectedFiles: any[]) => {
    setFileRejections(rejectedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple,
    disabled,
    onDropRejected,
    ...other,
  });

  const hasFile = !!file && !multiple;
  const hasFiles = !!files && multiple && !!files.length;
  const hasError = !!error || fileRejections.length > 0;

  const renderPlaceholder = (
    <Grid container direction="column" spacing={3} justifyContent="center" alignItems="center">
      <Grid>
        <UploadIllustration sx={{ width: 1, maxWidth: 200 }} />
      </Grid>
      <Grid>
        <Typography variant="h6" align="center">
          {t('label.drop_or_select_document')}
        </Typography>
        <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center' }}>
          {t('label.drop_documnet_here_or_click')}
          <Box
            component="span"
            sx={{
              mx: 0.5,
              color: 'primary.main',
              textDecoration: 'underline',
            }}
          >
            {t('label.browse')}
          </Box>
          {t('label.through_your_machine')}
        </Typography>
      </Grid>
    </Grid>
  );

  const renderSinglePreview = <SingleFilePreview imgUrl={typeof file === 'string' ? file : file?.preview} />;

  const renderMultiPreview = hasFiles && (
    <>
      <Box sx={{ maxHeight: 315, overflow: 'auto' }}>
        <MultiFilePreview
          files={files}
          thumbnail={thumbnail}
          onRemove={onRemove}
          uploadFilesTitle={''}
          previewFilesTitle={''}
        />
      </Box>

      <Grid container justifyContent="flex-end" spacing={1.5}>
        {onRemoveAll && (
          <Grid>
            <Button color="inherit" variant="outlined" size="small" onClick={onRemoveAll}>
              {t('label.remove_all')}
            </Button>
          </Grid>
        )}
        {onUpload && (
          <Grid>
            <Button
              size="small"
              variant="contained"
              onClick={onUpload}
              startIcon={<Iconify icon="eva:cloud-upload-fill" />}
            >
              {t('label.upload')}
            </Button>
          </Grid>
        )}
      </Grid>
    </>
  );

  return (
    <Box sx={{ width: 1, ...sx }}>
      <Grid container spacing={2} sx={{ width: 1 }}>
        {/* Upload Box Section */}
        <Grid mb={1} size={{ xs: 12, md: 6 }}>
          <InputLabel>{uploadFilesTitle}</InputLabel>
          <Box
            {...getRootProps()}
            sx={{
              mt: 1,
              p: 5,
              outline: 'none',
              borderRadius: 1,
              cursor: 'pointer',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              border: (theme) => `1px dashed ${alpha(theme.palette.grey[500], 0.2)}`,
              transition: (theme) => theme.transitions.create(['opacity', 'padding']),
              '&:hover': { opacity: 0.72 },
              ...(isDragActive && { opacity: 0.72 }),
              ...(disabled && {
                opacity: 0.48,
                pointerEvents: 'none',
              }),
              ...(hasError && {
                color: 'error.main',
                borderColor: 'error.main',
                bgcolor: (theme) => alpha(theme.palette.error.main, 0.08),
              }),
            }}
          >
            <input {...getInputProps()} />
            {renderPlaceholder}
          </Box>
        </Grid>

        {/* File Preview Section */}
        <Grid mb={{ xs: 0, md: 1 }} size={{ xs: 12, md: 6 }}>
          <InputLabel>{previewFilesTitle}</InputLabel>
          <Box
            sx={{
              mt: hasFiles ? 0 : 1,
              border: `${hasFiles ? 'none' : '1px solid'}`,
              borderColor: (theme) => alpha(theme.palette.grey[500], 0.2),
              borderRadius: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: hasFiles ? 'flex-start' : 'center',
              overflowY: hasFiles ? 'auto' : 'hidden',
            }}
          >
            {hasFile ? (
              renderSinglePreview
            ) : hasFiles ? (
              renderMultiPreview
            ) : (
              <EmptyContent
                filled={false}
                title={emptyContentText}
                sx={{
                  py: 7,
                  flexShrink: 0,
                  width: { xs: 1, md: 1 },
                  minHeight: 300,
                  position: 'relative',
                  overflow: 'unset',
                }}
              />
            )}
          </Box>
        </Grid>
      </Grid>

      {helperText && helperText}

      {/* Error Handling for File Rejections */}
      {fileRejections.length > 0 && <RejectionFiles fileRejections={fileRejections} />}
    </Box>
  );
}
