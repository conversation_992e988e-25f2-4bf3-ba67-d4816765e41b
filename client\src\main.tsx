import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import { ErrorBoundary } from '@/core/helper-components';
import { HelmetProvider } from 'react-helmet-async';
import { Suspense } from 'react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { AppConfigProvider, LoadingProvider } from './core/providers/index.ts';
import { QueryClientProvider } from 'react-query';
import { queryClient } from './shared/services/index.ts';
import LocalizationProvider from '@/locales/localization-provider';
import { SettingsProvider } from './components/settings/index.ts';
import { MotionLazy } from './components/animate/motion-lazy.tsx';
import SnackbarProvider from './components/snackbar/snackbar-provider.tsx';
import ThemeProvider from '@/theme/index';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <AppConfigProvider>
    <LocalizationProvider>
      <SettingsProvider
        defaultSettings={{
          themeMode: 'light', // 'light' | 'dark'
          themeDirection: 'ltr', //  'rtl' | 'ltr'
          themeContrast: 'bold', // 'default' | 'bold'
          themeLayout: 'vertical', // 'vertical' | 'horizontal' | 'mini'
          themeColorPresets: 'default', // 'default' | 'cyan' | 'purple' | 'blue' | 'orange' | 'red'
          themeStretch: true,
          defaultLanguage: 'en',
        }}
      >
        <ThemeProvider>
          <LoadingProvider>
            <MotionLazy>
              <SnackbarProvider>
                <QueryClientProvider client={queryClient}>
                  <HelmetProvider>
                    <BrowserRouter>
                      <ErrorBoundary>
                        <Suspense>
                          <App />
                        </Suspense>
                      </ErrorBoundary>
                    </BrowserRouter>
                  </HelmetProvider>
                </QueryClientProvider>
              </SnackbarProvider>
            </MotionLazy>
          </LoadingProvider>
        </ThemeProvider>
      </SettingsProvider>
    </LocalizationProvider>
  </AppConfigProvider>,
);
