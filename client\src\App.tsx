import './global.css';
// i18n
import '@/locales/i18n';
import { InteractionType } from '@azure/msal-browser';
import { AuthenticatedTemplate, MsalAuthenticationTemplate } from '@azure/msal-react';
import { AdAuthProvider } from '@/core/providers';
import RequestInterceptor from '@/core/interceptors/Request.interceptor';
import Router from '@/routes/routes';
import { useScrollToTop } from '@/hooks/use-scroll-to-top';
import PermissionProvider from './core/providers/Permission.provider';

function App() {
  useScrollToTop();

  return (
    <AdAuthProvider>
      <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
        <AuthenticatedTemplate>
          <RequestInterceptor>
            <PermissionProvider>
              <Router />
            </PermissionProvider>
          </RequestInterceptor>
        </AuthenticatedTemplate>
      </MsalAuthenticationTemplate>
    </AdAuthProvider>
  );
}

export default App;
