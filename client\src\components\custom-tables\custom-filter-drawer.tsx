import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Stack,
  Chip,
  Select,
  MenuItem,
  OutlinedInput,
  SelectChangeEvent,
  FormControl,
  InputLabel,
  Checkbox,
  ListItemText,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useState } from 'react';
import { trimText } from '@/shared/utils/trim-text.util';
import LoadingButton from '@mui/lab/LoadingButton';
import { useResponsive } from '@/hooks/use-responsive';
import { useTranslate } from '@/locales/use-locales';

interface CustomTableFilterDrawerProps {
  open: boolean;
  onClose: () => void;
  filters: Record<string, string[]>;
  onFilterChange: (key: string, value: string[]) => void;
  onSearch: () => void;
  onCancel: () => void;
  tableHeaders: string[];
  tableData: any[];
}

const CustomTableFilterDrawer: React.FC<CustomTableFilterDrawerProps> = ({
  open,
  onClose,
  filters,
  onFilterChange,
  onSearch,
  onCancel,
  tableHeaders,
  tableData,
}) => {
  const isMobile = useResponsive('down', 'sm');

  const getUniqueValues = (key: string) => {
    return Array.from(new Set(tableData?.map((item) => item[key]?.toString()))).filter(Boolean);
  };

  const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>({});
  const { t } = useTranslate();

  const handleMultiSelectChange = (key: string) => (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    onFilterChange(key, typeof value === 'string' ? value.split(',') : value);
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          sx: {
            width: isMobile ? '100%' : 480, // Full width on mobile, 480px on larger screens
            p: 3,
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
          },
        },
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">{t('placeholder.filter')}</Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </Box>

      <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
        {tableHeaders?.map((header) => {
          const fieldKey = header?.toString().toLowerCase().replace(/\s/g, '_');
          const uniqueValues = getUniqueValues(fieldKey);
          return (
            <FormControl key={fieldKey} fullWidth size="small" margin="dense">
              <InputLabel>{header}</InputLabel>
              <Select
                multiple
                value={filters[fieldKey] || []}
                onChange={handleMultiSelectChange(fieldKey)}
                input={<OutlinedInput label={header} />}
                open={openDropdowns[fieldKey] ?? false}
                onOpen={() => setOpenDropdowns((prev) => ({ ...prev, [fieldKey]: true }))}
                onClose={() => setOpenDropdowns((prev) => ({ ...prev, [fieldKey]: false }))}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} />
                    ))}
                  </Box>
                )}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: 300, // Set a max height for the dropdown
                    },
                  },
                }}
              >
                {uniqueValues.map((value) => (
                  <MenuItem key={value} value={value}>
                    <Checkbox checked={(filters[fieldKey] || []).indexOf(value) > -1} />
                    <ListItemText primary={trimText(value)} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        })}
      </Box>

      <Stack direction="row" justifyContent="end" mt={2} spacing={1}>
        <LoadingButton onClick={onCancel} variant="outlined" fullWidth={isMobile}>
          {t('btn_name.reset')}
        </LoadingButton>
        <LoadingButton onClick={onSearch} type="button" variant="contained" fullWidth={isMobile}>
          {t('placeholder.search')}
        </LoadingButton>
      </Stack>
    </Drawer>
  );
};

export default CustomTableFilterDrawer;
