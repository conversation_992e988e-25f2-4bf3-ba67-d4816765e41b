import { Injectable } from '@nestjs/common';
import { MSGraphApiClient } from 'src/shared/clients';

@Injectable()
export class GraphUserService {
	constructor(private readonly mSGraphApiClient: MSGraphApiClient) {}

	public searchUsers(searchText: string, orderBy: string, count: boolean): Promise<any> {
		return this.mSGraphApiClient.searchUsers(searchText, orderBy, count);
	}

	public getUserDetailsInMsResponse(userId: string): Promise<any> {
		return this.mSGraphApiClient.getUserDetailsInMsResponse(userId);
	}

	public getUsersDetailsFromAdInMsResponse(userIds: string): Promise<any> {
		return this.mSGraphApiClient.getUsersDetailsFromAdInMsResponse(userIds);
	}
}
