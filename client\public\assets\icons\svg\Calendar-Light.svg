<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 266.2 221.87">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: url(#linear-gradient-8);
      }

      .cls-6 {
        fill: url(#linear-gradient-7);
      }

      .cls-7 {
        fill: url(#linear-gradient-6);
      }

      .cls-8 {
        fill: url(#linear-gradient);
      }

      .cls-9 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="4.44" y1="161.74" x2="4.44" y2="6.43" gradientTransform="translate(0 223.87) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".96" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="225.79" y1="6.44" x2="4.44" y2="6.44" gradientTransform="translate(0 223.87) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".97" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="244.77" y1="201.67" x2="204.05" y2="201.67" gradientTransform="translate(0 223.87) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="21.44" y1="201.67" x2="62.08" y2="201.67" gradientTransform="translate(0 223.87) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".94" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="88.7" y1="201.67" x2="177.48" y2="201.67" gradientTransform="translate(0 223.87) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".1" stop-color="#0f0f19" stop-opacity=".35"/>
      <stop offset=".3" stop-color="#0f0f19" stop-opacity=".84"/>
      <stop offset=".5" stop-color="#0f0f19"/>
      <stop offset=".7" stop-color="#0f0f19" stop-opacity=".85"/>
      <stop offset=".9" stop-color="#0f0f19" stop-opacity=".38"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="9" y1="139.58" x2="257.47" y2="139.58" gradientTransform="translate(0 223.87) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".03" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".13" stop-color="#0f0f19" stop-opacity=".43"/>
      <stop offset=".31" stop-color="#0f0f19" stop-opacity=".86"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".69" stop-color="#0f0f19" stop-opacity=".82"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity=".31"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="11364.85" y1="-19416.19" x2="11364.85" y2="-19460.58" gradientTransform="translate(11555.61 19460.58) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".2" stop-color="#0f0f19" stop-opacity=".35"/>
      <stop offset=".6" stop-color="#0f0f19" stop-opacity=".84"/>
      <stop offset="1" stop-color="#0f0f19"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="11480.19" y1="-19416.19" x2="11480.19" y2="-19460.58" gradientTransform="translate(11555.61 19460.58) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".2" stop-color="#0f0f19" stop-opacity=".35"/>
      <stop offset=".6" stop-color="#0f0f19" stop-opacity=".84"/>
      <stop offset="1" stop-color="#0f0f19"/>
    </linearGradient>
  </defs>
  <rect class="cls-8" y="62.14" width="8.87" height="155.3"/>
  <rect class="cls-1" x="4.44" y="213" width="221.35" height="8.87"/>
  <rect class="cls-3" x="204.05" y="17.76" width="40.72" height="8.87"/>
  <path class="cls-9" d="M244.77,221.87h-18.98v-8.87h18.98c6.94,0,12.56-5.63,12.57-12.57V39.2c0-6.94-5.63-12.56-12.57-12.57v-8.87c11.83.01,21.42,9.6,21.44,21.44v161.24c-.01,11.83-9.6,21.42-21.44,21.44Z"/>
  <path class="cls-9" d="M8.87,62.14H0v-22.94c.01-11.83,9.6-21.42,21.44-21.44v8.87c-6.94,0-12.56,5.63-12.57,12.57v22.94Z"/>
  <rect class="cls-2" x="21.44" y="17.76" width="40.64" height="8.87"/>
  <rect class="cls-4" x="88.7" y="17.76" width="88.79" height="8.87"/>
  <rect class="cls-7" x="9" y="79.86" width="248.47" height="8.87"/>
  <rect class="cls-6" x="186.32" width="8.87" height="44.4"/>
  <rect class="cls-5" x="70.99" width="8.87" height="44.4"/>
</svg>