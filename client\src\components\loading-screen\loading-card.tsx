import { Card, SxProps } from '@mui/material';
import { LoadingScreen } from '.';
import { FC } from 'react';

type Props = {
  minHeight?: number;
  sx?: SxProps;
};

const LoadingCard: FC<Props> = ({ minHeight = 300, sx }) => {
  return (
    <Card
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: { minHeight },
        position: 'relative',
        overflow: 'unset',
        ...sx,
      }}
    >
      <LoadingScreen />
    </Card>
  );
};

export default LoadingCard;
