import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
import { MaintenanceIllustration } from '@/assets/illustrations';
import { varBounce, MotionContainer } from '@/components/animate';
import { Button } from '@mui/material';
import { useRedirect } from '@/core/hooks';
import { useLocation } from 'react-router';
import { FormEvent } from 'react';

export default function Page503() {
  const navigate = useRedirect();
  const location = useLocation();

  const handleClick = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Prevent default form submission behavior
    navigate(location.pathname);
    return true;
  };

  return (
    <MotionContainer
      sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}
    >
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          We’ll be back soon!
        </Typography>
      </m.div>

      <m.div style={{ textAlign: 'center' }} variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary' }}>
          Sorry for the inconvenience but we’re performing some maintenance at the moment.
        </Typography>
        <Typography sx={{ color: 'text.secondary' }}>We’ll be back online shortly!</Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <MaintenanceIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />
      </m.div>

      <form onSubmit={handleClick}>
        <Button type="submit" size="large" variant="contained">
          Check Now
        </Button>
      </form>
    </MotionContainer>
  );
}
