<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 282.33 237.05">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-3);
      }

      .cls-3 {
        fill: url(#linear-gradient);
      }

      .cls-4 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="-16376.23" y1="10739.25" x2="-16243.28" y2="10739.25" gradientTransform="translate(-10665.21 16496.74) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".77"/>
      <stop offset=".93" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-10516.17" y1="22800.48" x2="-10383.22" y2="22800.48" gradientTransform="translate(10523.79 22850.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".48" stop-color="#0f0f19" stop-opacity=".77"/>
      <stop offset=".93" stop-color="#0f0f19" stop-opacity=".1"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="11.11" y1="120.05" x2="282.33" y2="120.05" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
  </defs>
  <polygon class="cls-4" points="24.85 144.21 0 119.45 25.15 94.2 31.44 100.46 12.54 119.42 31.11 137.93 24.85 144.21"/>
  <rect class="cls-3" x="69.6" y="120.51" width="8.87" height="132.95" transform="translate(-110.67 107.44) rotate(-45.1)"/>
  <rect class="cls-1" x="8.74" y="45.79" width="132.95" height="8.87" transform="translate(-13.45 68.07) rotate(-45.11)"/>
  <rect class="cls-2" x="11.11" y="115.49" width="271.21" height="9.13"/>
</svg>