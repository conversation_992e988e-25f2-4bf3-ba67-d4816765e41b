import { m } from 'framer-motion';
import Typography from '@mui/material/Typography';
import { PageNotFoundIllustration } from '@/assets/illustrations';
import { varBounce, MotionContainer } from '@/components/animate';
// import { useRedirect } from '@/core/hooks';

// ----------------------------------------------------------------------

export default function NotFoundView() {
  // const navigate = useRedirect();

  // const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
  //   event.preventDefault(); // Prevent default form submission behavior
  //   navigate('/dashboard');
  // };

  return (
    <MotionContainer
      sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}
    >
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          Sorry, Page Not Found!
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary' }}>
          Sorry, we couldn't find the page you're looking for. Perhaps you've mistyped the URL? Be sure to check your
          spelling.
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <PageNotFoundIllustration
          sx={{
            height: 260,
            my: { xs: 5, sm: 10 },
          }}
        />
      </m.div>

      {/* <Button onClick={handleClick} size="large" variant="contained">
        Go to Dashboard
      </Button> */}
    </MotionContainer>
  );
}
