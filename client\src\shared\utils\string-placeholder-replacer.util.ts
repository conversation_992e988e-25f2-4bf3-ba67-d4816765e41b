export const stringPlaceholderReplacer = (str: string, replacements: { [key: string]: string }): string => {
    return str.replace(
        /{(\w+)}/g,
        (placeholderWithDelimiters, placeholderWithoutDelimiters) =>
            Object.prototype.hasOwnProperty.call(replacements, placeholderWithoutDelimiters) ?
                replacements[placeholderWithoutDelimiters] : placeholderWithDelimiters
    );
}