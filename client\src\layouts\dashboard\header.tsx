import { A<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography, useMediaQuery, useTheme } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';

import Logo from '@/components/logo';
import AccountPopover from '../common/account-popover';

import SvgColor from '@/components/svg-color';
import { useActiveLink } from '@/routes/hooks/use-active-link';
import { bgBlur } from '@/theme/css';
import NotificationsPopover from '../common/notifications-popover';
import { useNavData } from '../config-layout';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';

type Props = {
  onOpenNav?: VoidFunction;
};

const Header = ({ onOpenNav }: Props) => {
  const theme = useTheme();
  const { data } = useNavData();
  const { t } = useTranslation();

  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  const handleNavItemClick = (path: string) => {
    navigate(path);
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        width: '100%',
        // backgroundColor: theme.palette.background.paper,
        zIndex: theme.zIndex.appBar + 1,
        transition: theme.transitions.create(['height'], {
          duration: theme.transitions.duration.shorter,
        }),
        background: '#FFFFFF 0% 0% no-repeat padding-box', // ✅ Applies background styling
        boxShadow: '0px 3px 12px #00000029', // ✅ Adds shadow effect
        opacity: 1,
        ...bgBlur({ color: theme.palette.background.paper }),
      }}
    >
      <Toolbar sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {/* Mobile: Hamburger menu on the LEFT of Logo */}
        {isMobile && (
          <IconButton onClick={onOpenNav}>
            <SvgColor src="assets/icons/navbar/ic_menu_item.svg" />
          </IconButton>
        )}
        {/* Logo Section */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Logo />
        </Box>
        {/* Desktop & Tablet Navigation */}
        {(isDesktop || isTablet) && (
          <Stack flexGrow={1} justifyContent="flex-end" alignItems="center" marginRight={5} direction="row" spacing={1}>
            {data.length > 0 &&
              data[0] &&
              data[0].items.map((item) => {
                const isActive = useActiveLink(item.path);
                return (
                  <Typography
                    key={item.path}
                    variant="body1"
                    sx={{
                      color: isActive ? theme.palette.text.primary : theme.palette.text.secondary,
                      cursor: 'pointer',
                      fontWeight: isActive ? 600 : 500,
                      padding: '0.5rem 1rem',
                      borderRadius: '5px',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      whiteSpace: 'nowrap',
                      position: 'relative',
                      fontSize: '0.9rem',
                      letterSpacing: '0.02em',
                      '&:hover': {
                        color: theme.palette.text.primary,
                        backgroundColor: theme.palette.action.hover,
                        transform: 'translateY(-1px)',
                      },
                      backgroundColor: isActive ? theme.palette.action.selected : 'transparent',
                      border: '1px solid transparent',
                    }}
                    onClick={() => handleNavItemClick(item.path)}
                  >
                    {item.title}
                  </Typography>
                );
              })}
          </Stack>
        )}
        <Stack direction="row" spacing={2}>
          <NotificationsPopover />
          <AccountPopover />
        </Stack>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
