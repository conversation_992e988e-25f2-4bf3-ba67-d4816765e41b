<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 100 69.55">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #fff;
        opacity: 0;
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }

      .cls-4 {
        fill: url(#linear-gradient-3);
      }

      .cls-5 {
        fill: url(#linear-gradient-4);
      }

      .cls-6 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="984.49" y1="-980.57" x2="999.06" y2="-966" gradientTransform="translate(-1340.17 23.38) rotate(-45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#0f0f19"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="3472.35" y1="-15531.97" x2="3483.28" y2="-15531.97" gradientTransform="translate(-3422.69 -15517.17) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".39" stop-color="#0f0f19" stop-opacity=".79"/>
      <stop offset=".87" stop-color="#0f0f19" stop-opacity=".18"/>
      <stop offset=".98" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-3796.89" y1="-4572.89" x2="-3785.96" y2="-4572.89" gradientTransform="translate(-4529.36 -3776.62) rotate(-90) scale(1 -1)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-4" x1="11.8" y1="14.32" x2="95.29" y2="37.67" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
  </defs>
  <g>
    <rect class="cls-1" x="47.55" width="3.56" height="20.6"/>
    <polygon class="cls-6" points="49.33 23.12 46.13 19.92 48.65 17.41 49.33 18.09 50 17.41 52.52 19.93 49.33 23.12"/>
    <rect class="cls-3" x="49.66" y="13.02" width="10.94" height="3.56" transform="translate(5.68 43.31) rotate(-45)"/>
    <rect class="cls-4" x="41.75" y="9.33" width="3.56" height="10.94" transform="translate(2.28 35.12) rotate(-45)"/>
  </g>
  <polygon class="cls-5" points="87.25 30.84 14.95 30.84 14.95 16.23 11.41 16.23 11.41 34.4 90.81 34.4 90.81 16.23 87.25 16.23 87.25 30.84"/>
  <rect class="cls-2" y="19.55" width="100" height="50"/>
</svg>