<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 304.84 79.45">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
        isolation: isolate;
        stroke-width: 0px;
      }

      .cls-2 {
        fill: none;
        stroke: url(#linear-gradient);
        stroke-miterlimit: 10;
      }

      .cls-3 {
        fill: #000;
        font-family: <PERSON>lat-DemiBold, Pilat;
        font-size: 72px;
        font-weight: 300;
      }
    </style>
    <linearGradient id="linear-gradient" x1="570.17" y1="62.46" x2="526.13" y2="62.43" gradientTransform="translate(-265.33 102.16) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#010101"/>
      <stop offset="1" stop-color="#010101" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="463.94" y1="62.43" x2="569.43" y2="62.43" gradientTransform="translate(-265.33 102.16) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0" stop-color="#f5f5f5"/>
      <stop offset=".05" stop-color="#c7c7c7"/>
      <stop offset=".09" stop-color="#9c9c9c"/>
      <stop offset=".14" stop-color="#777"/>
      <stop offset=".2" stop-color="#565656"/>
      <stop offset=".25" stop-color="#3b3b3b"/>
      <stop offset=".32" stop-color="#252525"/>
      <stop offset=".4" stop-color="#141414"/>
      <stop offset=".49" stop-color="#080808"/>
      <stop offset=".63" stop-color="#010101"/>
      <stop offset="1" stop-color="#000"/>
    </linearGradient>
  </defs>
  <g>
    <path class="cls-2" d="M261.17,79.08l42.94-39.39L261.14.37"/>
    <rect class="cls-1" x="198.61" y="39.03" width="105.49" height="1.4"/>
  </g>
  <text class="cls-3" transform="translate(0 63.83)"><tspan x="0" y="0">Next</tspan></text>
</svg>