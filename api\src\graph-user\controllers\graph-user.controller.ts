import { Controller, Get, Query, UseGuards, Headers } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GraphUserService } from '../services';

@ApiTags('MS Graph User APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('graph-users')
export class GraphUserController {
	constructor(private readonly graphUserService: GraphUserService) {}

	@ApiResponse({
		status: 200,
		description: 'Search user using graph API.',
	})
	@ApiQuery({
		name: 'searchText',
		type: String,
	})
	@ApiQuery({
		name: 'orderBy',
		type: String,
	})
	@ApiQuery({
		name: 'count',
		type: Boolean,
	})
	@Get('search-users')
	public searchUsers(
		@Query('searchText') searchText: string,
		@Query('orderBy') orderBy: string,
		@Query('count') count: boolean,
	) {
		return this.graphUserService.searchUsers(searchText, orderBy, count);
	}

	@ApiResponse({
		status: 200,
		description: 'Search user detail by id',
	})
	@ApiQuery({
		name: 'userId',
		type: String,
	})
	@Get('')
	public getUserDetailsInMsResponse(@Query('userId') userId: string) {
		return this.graphUserService.getUserDetailsInMsResponse(userId);
	}

	@ApiResponse({
		status: 200,
		description: 'Search users detail by ids',
	})
	@ApiQuery({
		name: 'userIds',
		type: String,
	})
	@Get('by-userIds')
	public getUsersDetailsFromAdInMsResponse(@Query('userIds') userIds: string) {
		return this.graphUserService.getUsersDetailsFromAdInMsResponse(userIds);
	}
}
