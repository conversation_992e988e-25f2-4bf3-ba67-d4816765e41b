import { trimText } from '@/shared/utils/trim-text.util';
import { Tabs, Tab, Box, Stack, Tooltip } from '@mui/material';
import { useEffect, useRef } from 'react';

interface tabsObj {
  label: string;
  icon?: string; // Icon should be passed as a string representing the image path
}

interface CustomTabsProps {
  tabs: tabsObj[];
  tabIndex: number;
  setTabIndex: (index: number) => void;
  showIcon?: boolean;
  sx?: object;
  disableTabs?: boolean;
  trimLength?: number;
  splitLabel?: boolean;
}

export default function CustomTabs({
  tabs,
  tabIndex,
  setTabIndex,
  showIcon = false,
  sx,
  disableTabs = false,
  trimLength = 25,
  splitLabel = true,
}: CustomTabsProps) {
  // Create a reference to the tabs container to limit the scope of the mutation observer
  const tabsRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Check if the tabs container is available
    if (!tabsRef.current) return;

    let frameId: number | null = null;

    // Set up a MutationObserver to monitor the tabs container only
    const observer = new MutationObserver(() => {
      // Ensure that we don't process multiple DOM mutations at once
      // requestAnimationFrame ensures only one DOM update per frame
      if (frameId) return; // Already scheduled a frame update

      // Schedule the DOM update using requestAnimationFrame
      frameId = requestAnimationFrame(() => {
        // Query all the scroll buttons within the tabs container
        const buttons = tabsRef.current?.querySelectorAll('div.MuiTabScrollButton-root') || [];

        // Iterate over each scroll button
        buttons.forEach((btn) => {
          const element = btn as HTMLElement;
          // Check if the button has the disabled class
          const isDisabled = btn.classList.contains('Mui-disabled');

          // If the button is disabled and not hidden, hide it
          if (isDisabled && element?.style.display !== 'none') {
            element.style.display = 'none';
          }
          // If the button is not disabled and it is hidden, show it
          else if (!isDisabled && element.style.display !== '') {
            element.style.display = '';
          }
        });

        // Reset the frameId after the DOM update is complete
        frameId = null;
      });
    });

    // Start observing for mutations in the tabs container
    // We only need to observe changes to child elements, attributes, and class changes
    observer.observe(tabsRef.current, {
      childList: true, // Observe added or removed child elements
      subtree: true, // Observe the entire subtree of the tabs container
      attributes: true, // Observe attribute changes (e.g., class change)
      attributeFilter: ['class'], // Only track changes to the 'class' attribute
    });

    // Cleanup the observer and cancel any scheduled frame update when the component unmounts
    return () => {
      if (frameId) cancelAnimationFrame(frameId); // Cancel any pending frame update
      observer.disconnect(); // Disconnect the observer to stop watching for changes
    };
  }, []);

  const renderTootlTip = (value: { label: string }) => {
    return (
      <Tooltip title={value.label} arrow>
        <Box>{trimText(value.label, trimLength)}</Box>
      </Tooltip>
    );
  };
  return (
    <Tabs
      ref={tabsRef}
      value={tabIndex}
      onChange={(_, newIndex) => setTabIndex(newIndex)}
      sx={{
        '.MuiTabs-flexContainer': {},
        p: 0,
        mr: 0,
      }}
    >
      {tabs?.map((value, index) => (
        <Tab
          sx={tabIndex == index && showIcon && value.icon ? {} : { p: 0, m: 0, marginRight: 0 }}
          disabled={disableTabs}
          key={index}
          label={
            <Stack direction="row" spacing={0} alignItems="center" textAlign={'center'} margin={0}>
              {showIcon && value.icon && (
                <Box
                  margin={0}
                  component="img"
                  src={value.icon}
                  alt={value.label}
                  sx={{
                    width: 30,
                    height: 25,
                    flexShrink: 0,
                    ...sx,
                  }}
                />
              )}
              {/* Split words into two lines */}
              {value?.label && (
                <Box
                  margin={0}
                  sx={{
                    textAlign: showIcon && value.icon ? 'center' : '',
                    ml: showIcon && value.icon ? 1 : '',
                    justifySelf: 'center',
                  }}
                >
                  {showIcon && value.icon && splitLabel
                    ? value.label.split(' ').map((word, idx) => <div key={idx}>{word}</div>)
                    : value.label.length > trimLength
                      ? renderTootlTip(value)
                      : value.label}
                </Box>
              )}
            </Stack>
          }
        />
      ))}
    </Tabs>
  );
}
