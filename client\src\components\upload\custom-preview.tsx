import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import MultiFilePreview from './preview-multi-file';
import SingleFilePreview from './preview-single-file';

export default function PreviewBox({
  file,
  files,
  onRemove,
  onRemoveAll,
  onUpload,
  thumbnail,
  multiple
}: {
  file: any;
  files: any[];
  onRemove: () => void;
  onRemoveAll: () => void;
  onUpload: () => void;
  multiple: boolean;
  thumbnail?: boolean;
}) {
  const hasFile = !!file && !multiple;

  const hasFiles = !!files && multiple && !!files.length;

  const renderSinglePreview = hasFile && <SingleFilePreview imgUrl={typeof file === 'string' ? file : file?.preview} />;

  const renderMultiPreview = hasFiles && (
    <>
      <Box sx={{ my: 3 }}>
        <MultiFilePreview files={files} thumbnail={thumbnail} onRemove={onRemove} uploadFilesTitle={''} previewFilesTitle={''} />
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1.5 }}>
        {onRemoveAll && (
          <Typography
            variant="button"
            component="button"
            color="inherit"
            onClick={onRemoveAll}
            sx={{
              border: '1px solid',
              padding: '4px 8px',
              borderRadius: 1,
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': { backgroundColor: 'lightgray' }, // TODO: update this
            }}
          >
            Remove All
          </Typography>
        )}

        {onUpload && (
          <Typography
            variant="button"
            component="button"
            onClick={onUpload}
            sx={{
              backgroundColor: 'primary.main',
              color: 'white',
              padding: '4px 8px',
              borderRadius: 1,
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': { backgroundColor: 'primary.dark' },
            }}
          >
            Upload
          </Typography>
        )}
      </Box>
    </>
  );

  return (
    <Box
      sx={{
        flex: 1,
        border: '1px solid',
        borderColor: (theme) => theme.palette.grey[300],
        borderRadius: 1,
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: hasFiles ? 'flex-start' : 'center',
        height: '100%', // Ensures consistent height with UploadBox
        overflowY: hasFiles ? 'auto' : 'hidden', // Add scroll only for large file lists
      }}
    >
      {hasFile ? (
        renderSinglePreview // Render single preview
      ) : hasFiles ? (
        renderMultiPreview // Render multiple previews
      ) : (
        <Typography variant="body2" sx={{ textAlign: 'center' }}>
          No files uploaded
        </Typography>
      )}
    </Box>
  );
}
