import { GraphUserResponse, UserDetail } from '@/shared/models';
import { getUserDetails, searchGraphUsers } from '@/shared/services';
import getUserLoginId from '@/shared/utils/get-user-login-id';
import { Box, CircularProgress } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { Stack } from '@mui/system';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { RHFAUtoCompleteErrorStyling } from '../hook-form/rhf-autocomplete';

type Prop = {
  initialUserId?: string | null;
  onSelect: (user: UserDetail | null) => void;
  handleClear?: () => void;
  required?: boolean;
  validationError?: string;
  placeholder?: string;
  size?: 'small' | 'medium';
  isDisabled?: boolean;
};

export default function AdUserSearchInput({
  validationError = '',
  initialUserId = null,
  onSelect,
  required = false,
  placeholder,
  size = 'medium',
  isDisabled = false,
  handleClear,
}: Prop) {
  const { t } = useTranslation();
  const [value, setValue] = useState<GraphUserResponse | null>(null);
  const [searchText, setSearchText] = useState('');
  const { data, isFetching, refetch } = useQuery(
    ['adUsers', searchText],
    () => searchGraphUsers(searchText, 'displayName'),
    {
      enabled: !!searchText,
    },
  );
  useEffect(() => {
    if (initialUserId) {
      const getInitialUserValue = async () => {
        const user = await getUserDetails(initialUserId);
        if (user?.value[user.value.length - 1]) {
          const details = user?.value[user.value.length - 1];
          setValue(details);
          onSelect({
            firstName: details?.givenName || '',
            lastName: details?.surname || '',
            loginId: getUserLoginId(details),
            jobTitle: details?.jobTitle ?? '',
            email: details.mail ?? '',
            role: '',
            phone: '',
            escalationLevel: -1,
            isAdSearch: true,
          });
        }
      };

      getInitialUserValue();
    } else {
      setValue(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialUserId]);

  const debouncedSetSearchText = useMemo(() => debounce(setSearchText, 500), []);
  const formContext = useFormContext();
  const errors = formContext ? useFormState().errors : {};
  useEffect(() => {
    if (searchText.length) {
      refetch();
    }
  }, [refetch, searchText]);

  return (
    <Autocomplete
      size={size}
      loading={isFetching}
      fullWidth
      id="adUserSearch"
      getOptionLabel={(option) => option.displayName}
      filterOptions={(x) => x}
      options={data?.value || []}
      disabled={!!initialUserId && isDisabled}
      isOptionEqualToValue={(option, value) => option.id === value?.id}
      autoComplete
      includeInputInList
      value={value}
      sx={RHFAUtoCompleteErrorStyling(Boolean(errors?.userDetails))}
      noOptionsText={
        searchText ? (
          <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
            {t('empty_state_messages.no_user_found')}
          </Typography>
        ) : (
          <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
            {t('messages.type_something_to_search')}
          </Typography>
        )
      }
      onChange={(_, newValue: GraphUserResponse | null) => {
        setValue(newValue);
        if (newValue) {
          onSelect({
            firstName: newValue?.givenName || '',
            lastName: newValue?.surname || '',
            loginId: getUserLoginId(newValue),
            jobTitle: newValue?.jobTitle ?? '',
            role: '',
            email: newValue.mail ?? '',
            phone: '',
            escalationLevel: -1,
            isAdSearch: true,
          });
        } else {
          handleClear?.();
        }
      }}
      slots={{
        paper: (props) => (
          <Box {...props} sx={{ borderRadius: 2, boxShadow: 3, p: 1 }}>
            {isFetching ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ marginLeft: 1 }}>{t('label.searching')}</Typography>
              </Box>
            ) : searchText && data?.value?.length === 0 ? (
              <Typography sx={{ textAlign: 'center', p: 2, color: 'text.secondary' }}>
                {t('label.no_results_found')}
              </Typography>
            ) : (
              props.children
            )}
          </Box>
        ),
      }}
      onInputChange={(_event, value) => {
        debouncedSetSearchText(value);
      }}
      renderInput={(params) => (
        <TextField
          error={Boolean(validationError)}
          helperText={Boolean(validationError) ? validationError : null}
          {...params}
          label={
            <span>
              {placeholder || t('label.search_user')}
              {required && <span> *</span>}
            </span>
          }
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
          fullWidth
          // sx={{
          //   '& .MuiOutlinedInput-root': {
          //     '& fieldset': {
          //       borderColor: validationError ? 'red' : 'default', // Ensures red border when validationError is active
          //     },
          //     '&:hover fieldset': {
          //       borderColor: validationError ? 'red' : 'default', // Prevents black border on hover when validationError exists
          //     },
          //     '&.Mui-focused fieldset': {
          //       borderColor: validationError ? 'red' : 'default', // Red when validationError, blue on valid focus
          //     },
          //   },
          //   '& .MuiFormHelperText-root': {
          //     color: validationError ? 'red !important' : 'default', // Explicitly set red color on validationError
          //     fontWeight: validationError ? 'bold' : 'normal',
          //   },
          //   '& .MuiInputLabel-root': {
          //     color: validationError ? 'red !important' : 'default', // Label turns red on validationError
          //   },
          //   '& .MuiInputBase-input': {
          //     color: validationError ? 'red' : 'default', // Placeholder and text turn red when error exists
          //   },
          // }}
        />
      )}
      renderOption={(props, option) => {
        return (
          <li {...props} key={option.id}>
            <Grid container alignItems="center">
              <Grid sx={{ wordWrap: 'break-word' }}>
                <Stack flexDirection={'column'}>
                  <Typography variant="subtitle1" color="text.secondary" fontSize={14}>
                    {option.displayName}
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary" fontSize={12}>
                    {option.mail}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" fontSize={12}>
                    {option.jobTitle}
                  </Typography>
                </Stack>
              </Grid>
            </Grid>
          </li>
        );
      }}
    />
  );
}
