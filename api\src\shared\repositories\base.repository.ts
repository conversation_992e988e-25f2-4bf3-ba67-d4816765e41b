import * as _ from 'lodash';
import { GroupedCountResultItem, Model, ModelStatic, QueryTypes, WhereOptions } from 'sequelize';
import { HttpStatus } from '../enums';
import { HttpException } from '../exceptions';
import { IBaseRepo } from '../interfaces';
import { CurrentContext, FindParameters, MutationParameters, SearchOptions } from '../types';

export abstract class BaseRepository<M extends Model> implements IBaseRepo<M> {
	constructor(
		private model: ModelStatic<M>,
		private options: { useSoftDelete: boolean } = { useSoftDelete: true },
	) {
		this.model = model;
		this.options = { ...options };
	}

	/**
	 * Insert new entry in the database
	 * @param model
	 * @param throwException
	 * @returns
	 */
	async save(
		model: M,
		currentContext: CurrentContext,
		params: MutationParameters = { throwException: true },
	): Promise<M | null> {
		const { throwException } = params;
		const { username } = currentContext.user;
		model.setDataValue('createdBy', username.toLowerCase());
		model.setDataValue('updatedBy', username.toLowerCase());

		const result = await model.save();

		if (!result && throwException) {
			throw new HttpException(
				'Something went wrong with the database query.',
				HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
		return result ? result.get({ plain: true }) : null;
	}

	/**
	 * Return record by its primary key
	 * Wrapper around model's findById. Default (but overridable) error handling
	 * @param id
	 * @param throwException
	 * @returns
	 */
	async findById(
		id: number,
		params: FindParameters = {
			throwException: true,
			includeDeleted: false,
			includeInactive: false,
		},
	): Promise<M | null> {
		const condition = { where: { id } };
		return this.findOne(condition, params);
	}

	/**
	 * Find all the records by ids
	 * @param ids
	 * @param throwException
	 * @returns
	 */
	async findByIds(
		ids: number[],
		params: FindParameters = {
			throwException: true,
			includeDeleted: false,
			includeInactive: false,
		},
	): Promise<M[] | null> {
		const condition = { where: { id: ids } };
		return this.findAll(condition, params);
	}

	/**
	 * Wrapper around model's findOne. Default (but overridable) error handling
	 * @param options
	 * @param throwException
	 * @returns
	 */
	async findOne(
		findOptions,
		params: FindParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: false,
		},
	): Promise<M | null> {
		const { includeDeleted, includeInactive, throwException } = params;
		//Check filter if we need deleted and inactive records
		let whereCondition: WhereOptions = findOptions && findOptions.where ? findOptions.where : {};
		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}

		findOptions.where = whereCondition;

		const entity = await this.model.findOne(findOptions);
		if (!entity && throwException) {
			throw new HttpException('Record not found.', HttpStatus.NOT_FOUND);
		}
		return entity ? entity.get({ plain: true }) : null;
	}

	/**
	 * Wrapper around model's findAll. Default (but overridable) error handling
	 * @param options
	 * @param throwException
	 * @returns
	 */
	async findAll(
		findOptions: any = {},
		params: FindParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: true,
		},
		raw = false,
	): Promise<M[] | null | any> {
		//Check filter if we need deleted and inactive records
		let whereCondition: WhereOptions = findOptions && findOptions?.where ? findOptions.where : {};
		const { includeDeleted, includeInactive, throwException } = params;

		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}

		findOptions.where = whereCondition;

		const entities = await this.model.findAll(findOptions);
		if (!entities && throwException) {
			throw new HttpException('Record not found.', HttpStatus.NOT_FOUND);
		}

		if (raw) {
			return entities;
		}

		return entities.length ? entities.map(entity => entity.get({ plain: true })) : [];
	}

	/**
	 * Wrapper around model's findAll. Default (but overridable) error handling
	 * @param options
	 * @param throwException
	 * @returns
	 */
	async findAndCountAll(
		findOptions: any = {},
		params: FindParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: true,
		},
	): Promise<{ rows: M[]; count: number } | null> {
		//Check filter if we need deleted and inactive records
		let whereCondition: WhereOptions = findOptions && findOptions?.where ? findOptions.where : {};
		const { includeDeleted, includeInactive, throwException } = params;

		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}

		findOptions.where = whereCondition;
		const records = await this.model.findAndCountAll(findOptions);
		if (!records && throwException) {
			throw new HttpException('Record not found.', HttpStatus.NOT_FOUND);
		}
		const plainRecords = records.rows.length
			? records.rows.map(entity => entity.get({ plain: true }))
			: [];

		let totalRecord = 0;

		if (isNaN(records.count)) {
			const totalSets = records.count as unknown as GroupedCountResultItem[];
			totalRecord = totalSets.length;
		} else {
			totalRecord = records.count;
		}

		return {
			rows: plainRecords,
			count: totalRecord,
		};
	}

	/**
	 * Wrapper around model's bulkCreate. Default (but overridable) error handling
	 * @param records
	 * @param options
	 * @param throwException
	 * @returns
	 */
	async insertMany(
		records: any[],
		currentContext: CurrentContext,
		options?: any,
		params: MutationParameters = { throwException: false },
	): Promise<M[]> {
		const { throwException } = params;
		const { username } = currentContext.user;
		records = records.map(record => ({
			...record,
			createdBy: username.toLowerCase(),
			updatedBy: username.toLowerCase(),
		}));
		const response = await this.model.bulkCreate(records, options);
		if (!response && throwException) {
			throw new HttpException(
				'Something went wrong with the database query.',
				HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
		return response.length ? response.map(r => r.get({ plain: true })) : null;
	}

	/**
	 * Wrapper around model's update. Default (but overridable) error handling
	 * @param values
	 * @param options
	 * @param throwException
	 * @returns
	 */
	async update(
		values: any,
		currentContext: CurrentContext,
		options: any,
		params: MutationParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: true,
		},
	): Promise<number | null> {
		let whereCondition: WhereOptions = options && options?.where ? options.where : {};
		const { includeDeleted, includeInactive, throwException } = params;

		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}

		options.where = whereCondition;

		const { username } = currentContext.user;
		values.updatedBy = username.toLowerCase();

		const result = await this.model.update(values, options);
		if (!result && throwException) {
			throw new HttpException(
				'Something went wrong with the database query.',
				HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
		return result[0] || null;
	}

	/**
	 * Wrapper around model's upsert. Default (but overridable) error handling
	 * @param values
	 * @param options
	 * @param throwException
	 * @returns
	 */
	async upsert(
		values: any,
		currentContext: CurrentContext,
		options: any,
		params: MutationParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: true,
		},
	): Promise<[M, boolean | null]> {
		let whereCondition: WhereOptions = options && options?.where ? options.where : {};
		const { includeDeleted, includeInactive, throwException } = params;

		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}

		options.where = whereCondition;

		const { username } = currentContext.user;
		values.updatedBy = username.toLowerCase();
		if (!values.createdBy) {
			values.createdBy = username.toLowerCase();
		}
		const result = await this.model.upsert(values, options);
		if (!result && throwException) {
			throw new HttpException(
				'Something went wrong with the database query.',
				HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}

		return result || null;
	}

	/**
	 * Wrapper around model's update without user for public apis. Default (but overridable) error handling
	 * @param values
	 * @param options
	 * @param throwException
	 * @returns
	 */
	async updateWithoutUser(
		values: any,
		options: any,
		params: MutationParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: true,
		},
	): Promise<number | null> {
		let whereCondition: WhereOptions = options && options?.where ? options.where : {};
		const { includeDeleted, includeInactive, throwException } = params;

		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}

		options.where = whereCondition;

		const result = await this.model.update(values, options);
		if (!result && throwException) {
			throw new HttpException(
				'Something went wrong with the database query.',
				HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
		return result[0] || null;
	}

	/**
	 * Delete record from the database by its primary key
	 * @param id
	 * @param throwException
	 * @returns
	 */
	async deleteByCondition(
		condition: any,
		currentContext: CurrentContext,
		params: MutationParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: true,
		},
	): Promise<boolean> {
		// Pass condition with where object { where: { id } }
		let whereCondition: WhereOptions = condition && condition?.where ? condition.where : {};
		const { includeDeleted, includeInactive, throwException } = params;

		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}

		const { username } = currentContext.user;
		const currentTimestamp = Date.now();
		condition = { ...condition, deleted: false };

		let result;
		if (this.options.useSoftDelete) {
			result = await this.model.update(
				{
					deleted: true,
					updatedBy: username.toLowerCase(),
					updatedOn: currentTimestamp,
				},
				{
					where: condition,
				},
			);
		} else {
			result = await this.model.destroy({ where: condition, force: true });
		}

		if (!result && throwException) {
			throw new HttpException(
				'Something went wrong with the database query.',
				HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
		return true;
	}

	/**
	 * Delete record from the database by its primary key
	 * @param id
	 * @param throwException
	 * @returns
	 */
	async deleteById(
		id: number,
		currentContext: CurrentContext,
		params: MutationParameters = { throwException: true },
	): Promise<boolean> {
		const condition: WhereOptions = { id };
		const entity = await this.findById(id);
		if (!entity) {
			throw new HttpException("Id doesn't exist.", HttpStatus.NOT_FOUND);
		}
		return this.deleteByCondition(condition, currentContext, params);
	}

	/**
	 * Delete records by primary keys
	 * @param ids
	 * @param throwException
	 * @returns
	 */
	async deleteByIds(
		ids: number[],
		currentContext: CurrentContext,
		params: MutationParameters = { throwException: true },
	): Promise<boolean> {
		const condition: WhereOptions = { id: ids };
		return this.deleteByCondition(condition, currentContext, params);
	}

	/**
	 * Count number of record
	 * @param ids
	 * @param throwException
	 * @returns
	 */
	async count(filter: any): Promise<number> {
		let conditions = {};
		if (filter) {
			conditions = { where: { ...filter } };
		}
		const count = await this.model.count(conditions);
		return count;
	}

	/**
	 * Check if the record exists with following condition
	 * @param findOptions
	 * @param params
	 * @returns
	 */
	async isRecordExist(
		findOptions,
		params: FindParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: false,
		},
	): Promise<boolean> {
		const { includeDeleted, includeInactive } = params;
		let whereCondition: WhereOptions = findOptions && findOptions.where ? findOptions.where : {};
		if (!includeInactive) {
			whereCondition = { ...whereCondition, active: !includeInactive };
		}
		if (!includeDeleted) {
			whereCondition = { ...whereCondition, deleted: includeDeleted };
		}
		findOptions.where = whereCondition;
		const count = await this.model.count(findOptions);
		return +count > 0;
	}

	/**
	 * Increment the column value.
	 * @param col
	 * @param condition
	 * @param params
	 * @returns
	 */
	async increment(
		col: string,
		incrementBy: number,
		condition,
		params: FindParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: false,
		},
	): Promise<any> {
		const { includeDeleted, includeInactive } = params;
		let whereCondition: WhereOptions = condition && condition.where ? condition.where : {};
		whereCondition = { ...whereCondition, deleted: includeDeleted, active: !includeInactive };
		condition.where = whereCondition;
		return this.model.increment(col, { by: incrementBy, ...condition });
	}

	/**
	 * Maxium value of the column.
	 * @param col
	 * @param condition
	 * @param params
	 * @returns
	 */
	async max(
		col: string,
		condition,
		params: FindParameters = {
			includeDeleted: false,
			includeInactive: false,
			throwException: false,
		},
	): Promise<number> {
		const { includeDeleted, includeInactive } = params;
		let whereCondition: WhereOptions = condition && condition.where ? condition.where : {};
		whereCondition = { ...whereCondition, deleted: includeDeleted, active: !includeInactive };
		condition.where = whereCondition;
		return this.model.max(col, condition);
	}

	/**
	 *
	 * @param condition
	 * @param options
	 * @returns
	 */
	async search(condition: any = {}, options?: SearchOptions): Promise<M[]> {
		const { limit, startId, order, include } = options;
		let filters = {};
		let argument = { raw: true };

		if (condition && !_.isEmpty(condition)) {
			condition = { where: condition };
		}

		if (limit) {
			filters = { ...filters, limit };
		}

		if (order) {
			filters = { ...filters, order };
		}

		if (startId) {
			filters = { ...filters, offset: startId };
		}

		if (include) {
			filters = { ...filters, include };
		}
		argument = { ...argument, ...condition, ...filters };
		return this.model.findAll(argument);
	}

	/**
	 * Execute raw SQL query.
	 * @param query
	 * @param replacements
	 * @returns
	 */
	async executeQuery(query: string, replacements: any = {}) {
		return this.model.sequelize.query(query, {
			type: QueryTypes.SELECT,
			replacements,
		});
	}
}
