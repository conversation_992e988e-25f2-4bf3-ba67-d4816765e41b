export interface TreeViewDataType {
    id: string;
    code: string;
    shortName: string;
    fullName: string;
    parentId: string | null;
    entityType: string;
    isNode: boolean;
    active: boolean;
    countryId: number;
    mapCenter: string;
    utcTimeDiff: string;
    other_info: { [key: string]: string } | null;
    children: TreeViewDataType[];
    disabled?: boolean;
    showCheckbox?: boolean;
    locations?: any[];
    locationCount: number;
    capabilityCount: number;
    capabilityList: any[];
  }