import { ReplaceUrlVariable } from '@/routes/paths';
import * as http from '@/shared/services';
import { API_PATHS } from '../constants';
import { HistoryListRequestParams, HistoryListResponse } from '../models/history.model';

export const getHistoryList = ({ type, id }: HistoryListRequestParams): Promise<HistoryListResponse[]> => {
  return http.get<HistoryListResponse[]>(ReplaceUrlVariable(API_PATHS.HISTORY.GET_HISTORY_LIST, { type, id }));
};
