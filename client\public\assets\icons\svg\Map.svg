<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 167.92 233.73">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="83.96" y1="190.19" x2="83.96" y2="113.34" gradientTransform="translate(0 235.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="16.93" y1="235.72" x2="16.93" y2="2" gradientTransform="translate(67.03 235.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-2" d="M83.96,122.38c-21.22,0-38.42-17.2-38.42-38.42,0-21.22,17.2-38.42,38.42-38.42,21.22,0,38.42,17.2,38.42,38.42h0c-.02,21.21-17.21,38.4-38.42,38.42ZM83.96,54.41c-16.32,0-29.55,13.23-29.55,29.55s13.23,29.55,29.55,29.55c16.32,0,29.55-13.23,29.55-29.55h0c-.02-16.31-13.24-29.53-29.55-29.55Z"/>
  <path class="cls-1" d="M83.96,233.73l-3.54-4.68c-.36-.48-36.83-48.84-67.4-100.1C4.75,115.07,0,98.67,0,83.96,0,37.58,37.6,0,83.97,0c46.37,0,83.95,37.59,83.96,83.96,0,14.72-4.75,31.12-13.02,45-30.57,51.26-67.04,99.61-67.4,100.1l-3.54,4.68ZM83.96,8.87c-41.45.04-75.05,33.64-75.09,75.09,0,13.16,4.29,27.91,11.77,40.45,24.77,41.54,53.46,81.17,63.32,94.53,9.86-13.37,38.55-53,63.32-94.53,7.48-12.55,11.77-27.29,11.77-40.45-.05-41.45-33.64-75.04-75.09-75.09h0Z"/>
</svg>