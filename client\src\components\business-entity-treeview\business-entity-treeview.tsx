import Iconify from '@/components/iconify/iconify';
import { useOutsideClick } from '@/hooks/use-outside-click';
import { Box, InputAdornment, Paper, Stack, TextField } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';
import { useTranslate } from '@/locales/use-locales';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import SearchNotFound from '../search-not-found';
import { bgBlur } from '@/theme/css';

type Props = {
  isMultiSelect: boolean;
  data: TreeViewDataType[];
  onNodeSelection: (node: TreeViewDataType) => void;
  defaultValue?: TreeViewDataType | null;
};

const BusinessEntityTreeView = ({ isMultiSelect = false, data, onNodeSelection, defaultValue }: Props) => {
  const { t } = useTranslate();

  const [selectedNode, setSelectedNode] = useState<TreeViewDataType | null>(() => defaultValue || null);
  const [searchTerm, setSearchTerm] = useState('');
  const [openTreeview, setOpenTreeview] = useState(false);
  const filteredData: TreeViewDataType[] = searchNodeItem(searchTerm);
  const [expandedItems, setExpandedItems] = useState<string[]>([data[0]?.id]);

  useEffect(() => {
    if (defaultValue) {
      setSelectedNode(defaultValue);
    }
  }, [defaultValue]);

  const getAllItemsWithChildrenItemIds = () => {
    const itemIds: string[] = [];
    const registerItemId = (item: TreeViewDataType) => {
      if (item.children?.length) {
        itemIds.push(item.id);
        item.children.forEach(registerItemId);
      }
    };
    filteredData.forEach(registerItemId);
    return itemIds;
  };

  const handleItemSelectionToggle = (_event: React.SyntheticEvent, itemId: string, isSelected: boolean) => {
    if (isSelected) {
      const selectedNodeItem = findNodeById(filteredData[filteredData.length - 1], itemId);
      if (selectedNodeItem && selectedNodeItem.isNode) {
        setOpenTreeview(false);
        setSelectedNode(selectedNodeItem);
        onNodeSelection(selectedNodeItem);
      }
    }
  };

  /**
   * Depth first search.
   * @param node
   * @param term
   * @param foundIds
   * @returns
   */
  function dfs(node: TreeViewDataType, term: string, foundIds: string[]) {
    // Implement your search functionality
    let isMatching = node.fullName && node.fullName.toLowerCase().indexOf(term.toLowerCase()) > -1;

    if (Array.isArray(node.children)) {
      node.children.forEach((child: TreeViewDataType) => {
        const hasMatchingChild = dfs(child, term, foundIds);
        isMatching = isMatching || hasMatchingChild;
      });
    }
    // We will add any item if it matches our search term or if it has a children that matches our term
    if (isMatching && node.id) {
      foundIds.push(node.id);
    }
    return isMatching;
  }

  /**
   * Return all those tree nodes which matche with matchedIDS
   * @param data
   * @param matchedIDS
   * @returns
   */
  function filter(data: TreeViewDataType[], matchedIDS: string[]): TreeViewDataType[] {
    return data
      .filter((item) => matchedIDS.indexOf(item.id) > -1)
      .map((item) => ({
        ...item,
        children: item.children ? filter(item.children, matchedIDS) : [],
      }));
  }

  /**
   * Search the tree leaf nodes that matches with searh term
   * @param term
   * @returns
   */
  function searchNodeItem(term: string): TreeViewDataType[] {
    // Wrap data in an object to match the node shape
    const dataNode = {
      children: data,
    } as TreeViewDataType;

    const matchedIDS: string[] = [];
    // find all items IDs that matches our search (or their children does)
    dfs(dataNode, term, matchedIDS);

    // filter the original data so that only matching items (and their fathers if they have) are returned
    return filter(data, matchedIDS);
  }

  /**
   * Find the node with given id.
   * @param root
   * @param id
   * @returns
   */
  function findNodeById(root: TreeViewDataType | undefined, id: string): TreeViewDataType | undefined {
    if (!root) return undefined; // If the root is undefined, return undefined

    // Check if the root node matches the ID
    if (root.id === id) {
      return root;
    }

    // If the root has children, recursively search in each child
    if (root.children && root.children.length > 0) {
      for (const child of root.children) {
        const foundNode = findNodeById(child, id);
        if (foundNode) {
          return foundNode;
        }
      }
    }
    // If the node is not found in the current root or its children, return undefined
    return undefined;
  }

  const onSearchTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setExpandedItems(event.target.value ? getAllItemsWithChildrenItemIds() : [data[0]?.id]);
  };

  const handleExpandedItemsChange = (_event: React.SyntheticEvent, itemIds: string[]) => {
    setExpandedItems(itemIds);
  };

  const ref = useOutsideClick(() => {
    setOpenTreeview(false);
  });

  return (
    <>
      <Box ref={ref}>
        <Stack spacing={1} flexDirection="row" justifyContent="space-between" position="relative">
          <TextField
            variant="outlined"
            value={!selectedNode ? `${t('placeholder.select_business_entity')}` : `${selectedNode.fullName}`}
            sx={{
              backgroundColor: (theme) => `${theme.palette.grey[400]}`,
              borderRadius: 1,
              input: { cursor: 'pointer' },
              caretColor: 'transparent',
            }}
            fullWidth
            onClick={() => setOpenTreeview(!openTreeview)}
            size="medium"
            InputProps={{
              endAdornment: (
                <InputAdornment position="start">
                  <Iconify
                    icon={!openTreeview ? 'eva:arrow-ios-downward-fill' : 'eva:arrow-ios-upward-fill'}
                    sx={{ color: 'text.disabled' }}
                  />
                </InputAdornment>
              ),
            }}
          />
        </Stack>
        <Box sx={{ position: 'relative', width: '100%' }}>
          <Paper
            elevation={3}
            sx={{
              position: 'absolute',
              zIndex: 1,
              width: '100%',
              top: '0px',
              bgcolor: 'unset',
              ...bgBlur({
                blur: 20,
                opacity: 0.9,
                color: '#FFFFFF',
              }),
              backgroundImage: `url(assets/cyan-blur.png), url(assets/red-blur.png)`,
              backgroundRepeat: 'no-repeat, no-repeat',
              backgroundPosition: 'top right, left bottom',
              backgroundSize: '50%, 50%',
              boxShadow: (theme) => theme.customShadows.dropdown,
            }}
          >
            <Stack spacing={1}>
              {openTreeview && (
                <Box sx={{ p: 1, overflowY: 'scroll', maxHeight: '300px' }}>
                  <TextField
                    placeholder={t('placeholder.filter')}
                    fullWidth
                    sx={{ mb: 1 }}
                    value={searchTerm}
                    onChange={onSearchTextChange}
                    size="small"
                  />
                  {filteredData.length ? (
                    <RichTreeView
                      items={filteredData}
                      onItemSelectionToggle={handleItemSelectionToggle}
                      defaultExpandedItems={['fullName']}
                      getItemLabel={(item) => item.fullName}
                      expandedItems={expandedItems}
                      multiSelect={isMultiSelect}
                      onExpandedItemsChange={handleExpandedItemsChange}
                      sx={{ padding: (theme) => theme.spacing(0.5) }}
                    />
                  ) : (
                    <SearchNotFound query={searchTerm} sx={{ px: 2, pt: 4, pb: 4 }} />
                  )}
                </Box>
              )}
            </Stack>
          </Paper>
        </Box>
      </Box>
    </>
  );
};

export default BusinessEntityTreeView;
