import { Controller, useFormContext } from 'react-hook-form';

import Autocomplete, { AutocompleteProps } from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';

import { Checkbox, Stack, Typography } from '@mui/material';

export const RHFAUtoCompleteErrorStyling = (error: boolean) => ({
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      borderColor: error ? '#FF5630' : 'default', // Ensures #FF5630 border when erroris active
    },
    '&:hover fieldset': {
      borderColor: error ? '#FF5630' : 'default', // Prevents black border on hover when errorexists
    },
    '&.Mui-focused fieldset': {
      borderColor: error ? 'red' : 'default', // Red when isSubmitted && !getValues(`number${index}`), blue on valid focus
    },
  },
  '& .MuiFormHelperText-root': {
    color: error ? 'red !important' : 'default', // Explicitly set red color on isSubmitted && !getValues(`number${index}`)
    fontWeight: error ? 'bold' : 'normal',
  },
  '& .MuiInputLabel-root': {
    color: error ? 'red !important' : 'default', // Label turns red on isSubmitted && !getValues(`number${index}`)
  },
  '& .MuiInputBase-input': {
    color: error ? 'red' : 'default', // Placeholder and text turn red when error exists
  },
});
// ----------------------------------------------------------------------

interface Props<
  T,
  Multiple extends boolean | undefined,
  DisableClearable extends boolean | undefined,
  FreeSolo extends boolean | undefined,
> extends AutocompleteProps<T, Multiple, DisableClearable, FreeSolo> {
  name: string;
  label?: string;
  placeholder?: string;
  type?: 'country' | string;
  helperText?: React.ReactNode;
  onValueUpdate?: () => void;
}

export default function RHFAutocomplete<
  T,
  Multiple extends boolean | undefined,
  DisableClearable extends boolean | undefined,
  FreeSolo extends boolean | undefined,
>({
  name,
  label,
  type,
  helperText,
  placeholder,
  onValueUpdate,
  ...other
}: Omit<Props<T, Multiple, DisableClearable, FreeSolo>, 'renderInput'>) {
  const { control, setValue, getValues } = useFormContext();
  const { multiple } = other;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => {
        return (
          <Autocomplete
            {...field}
            onChange={(event, newValue) => {
              setValue(name, newValue, { shouldValidate: true });
              onValueUpdate && onValueUpdate();
            }}
            renderInput={(params) => (
              <>
                <TextField
                  {...params}
                  label={label}
                  placeholder={placeholder}
                  error={!!error}
                  autoComplete="new-password"
                  helperText={error ? error?.message : helperText}
                  slotProps={{
                    inputLabel: {
                      shrink: true,
                    },
                  }}
                  inputProps={{
                    ...params.inputProps,
                    autoComplete: 'new-password',
                  }}
                />
              </>
            )}
            renderOption={(params, option: any, state) => {
              return (
                <Stack direction="row" spacing={0} key={option.key ?? params.key}>
                  {multiple && (
                    <Checkbox
                      onClick={() => {
                        const currentValue = getValues(name) ?? [];
                        const optionIndex = currentValue.findIndex((item: any) => item.value == option['value']);
                        if (optionIndex == -1) {
                          setValue(name, [...currentValue, option], { shouldValidate: true });
                        } else {
                          currentValue.splice(optionIndex, 1);
                          setValue(name, [...currentValue], { shouldValidate: true });
                        }
                        onValueUpdate && onValueUpdate();
                      }}
                      size="small"
                      disableRipple
                      checked={state.selected}
                    />
                  )}

                  <Typography sx={{ mt: 0.8 }} variant="value">
                    {params.key}
                  </Typography>
                </Stack>
              );
            }}
            {...other}
          />
        );
      }}
    />
  );
}

// ----------------------------------------------------------------------
