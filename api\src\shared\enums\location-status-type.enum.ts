export enum LOCATION_STATUS_TYPE {
    PLANNED = "PLANNED",
    IN_OPERATION = "IN_OPERATION",
    IN_ACTIVE = "IN_ACTIVE",
    OTHER = "OTHER"
}

export enum LOCATION_STATUS_TYPE_DISPLAY {
    PLANNED = "Planned",
    IN_OPERATION = "In Operation",
    IN_ACTIVE = "In Active",
    OTHER = "Other"
}

export enum LOCATION_STATUS_COLOR_ENUM {
    PLANNED = '#fd7e14', // orange
    IN_OPERATION = '#28a745', // green
    OTHER = '#007bff', // blue
    IN_ACTIVE = '#dc3545', // red
}

export enum LEASE_OWNERSHIP_STATUS_TYPE {
    OWNED = "OWNED",
    LEASED = "LEASED",
    IMPLANT = "IMPLANT",
    OTHER = "OTHER",
    CUSTOMER = "CUSTOMER"
}

export enum LEASE_OWNERSHIP_STATUS_TYPE_DISPLAY {
    OWNED = 'Owned',
    LEASED = 'Leased',
    IMPLANT = 'Implant',
    OTHER = 'Other',
    CUSTOMER = 'Customer'
}


export enum PARTNER_BRANCH_STATUS_TYPE {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED"
}

export enum PARTNER_BRANCH_ACTION_TYPE {
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED"
}