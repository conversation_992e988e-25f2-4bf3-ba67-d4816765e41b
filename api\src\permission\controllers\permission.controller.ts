import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RequestContext } from 'src/shared/types';
import { PermissionResponseDto } from '../dtos';
import { PermissionService } from '../services';

@ApiTags('Permission APIs')
@ApiBearerAuth()
@Controller('permissions')
export class PermissionController {
	constructor(public readonly permissionService: PermissionService) { }

	@UseGuards(AuthGuard('oauth-bearer'))
	@ApiResponse({
		status: 200,
		description: 'Get list of permissions with locations for the user.',
		type: [PermissionResponseDto],
	})
	@Get('/app-permission')
	public getListOfUserPermissions(
		@Req() request: RequestContext,
	): Promise<PermissionResponseDto[]> {
		return this.permissionService.getListOfUserPermissions(request.currentContext);
	}
}
