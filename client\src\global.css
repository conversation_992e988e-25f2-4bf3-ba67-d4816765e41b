/* scrollbar */
@import 'simplebar-react/dist/simplebar.min.css';

/* lazy image */
@import 'react-lazy-load-image-component/src/effects/blur.css';

/* map */
@import 'mapbox-gl/dist/mapbox-gl.css';

/* editor */
@import 'react-quill-new/dist/quill.snow.css';

/* carousel */
@import 'slick-carousel/slick/slick.css';
@import 'slick-carousel/slick/slick-theme.css';

/* lightbox */
@import 'yet-another-react-lightbox/styles.css';
@import 'yet-another-react-lightbox/plugins/captions.css';
@import 'yet-another-react-lightbox/plugins/thumbnails.css';

@font-face {
  font-family: 'Pilat Demi';
  src: url('../public/assets/fonts/Pilat_Demi.ttf');
}

@font-face {
  font-family: 'Pilat Light';
  src: url('../public/assets/fonts/Pilat_Light.ttf');
}

@font-face {
  font-family: 'Pilat Heavy';
  src: url('../public/assets/fonts/Pilat_Wide_Heavy.ttf');
}

a {
  text-decoration: none;
}

.leaflet-container {
  height: 50vh;
  width: 50wh;
}
