import { Controller, useFormContext } from 'react-hook-form';

import TextField, { TextFieldProps } from '@mui/material/TextField';

// ----------------------------------------------------------------------

type Props = TextFieldProps & {
  name: string;
};

export default function RHFTextField({ name, helperText, type, ...other }: Props) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          fullWidth
          type={type}
          value={type === 'number' && field.value === 0 ? '' : field.value || ''}
          onChange={(event) => {
            if (type === 'number') {
              field.onChange(Number(event.target.value));
            } else {
              field.onChange(event.target.value);
            }
          }}
          slotProps={{
            inputLabel: {
              shrink: true, // Keeps label in floating position by default
              sx: { color: error ? 'red' : 'default' }, // Label turns red on error
            },
          }}
          error={!!error}
          helperText={error ? error?.message : helperText}
          // sx={{
          //   '& .MuiOutlinedInput-root': {
          //     '& fieldset': {
          //       borderColor: error ? 'red' : 'default', // Ensures red border when error is active
          //     },
          //     '&:hover fieldset': {
          //       borderColor: error ? 'red' : 'default', // Prevents black border on hover when error exists
          //     },
          //     '&.Mui-focused fieldset': {
          //       borderColor: error ? 'red' : 'default', // Red when error, blue on valid focus
          //     },
          //   },
          //   '& .MuiFormHelperText-root': {
          //     color: error ? 'red !important' : 'default', // Explicitly set red color on error
          //     fontWeight: error ? 'bold' : 'normal',
          //   },
          //   '& .MuiInputLabel-root': {
          //     color: error ? 'red !important' : 'default', // Label turns red on error
          //   },
          //   '& .MuiInputBase-input': {
          //     color: error ? 'red' : 'default', // Placeholder and text turn red when error exists
          //   },
          // }}
          {...other}
        />
      )}
    />
  );
}
