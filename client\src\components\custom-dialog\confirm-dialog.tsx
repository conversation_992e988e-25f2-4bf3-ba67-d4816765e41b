import { useTranslate } from '@/locales/use-locales';
import { Box, IconButton } from '@mui/material';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { CSSProperties } from 'react';
import Iconify from '../iconify';
import { ConfirmDialogProps } from './types';

// ----------------------------------------------------------------------

type ExtendedConfirmDialogProps = ConfirmDialogProps & {
  showCloseButton?: boolean;
  showCancelButton?: boolean;
  titleStyle?: CSSProperties;
  contentStyle?: CSSProperties;
};

export default function ConfirmDialog({
  title,
  content,
  action,
  open,
  onClose,
  cancelBtnText = 'btn_name.cancel',
  showCloseButton = false,
  showCancelButton = true,
  titleStyle,
  contentStyle,
  ...other
}: ExtendedConfirmDialogProps) {
  const { t } = useTranslate();

  return (
    <Dialog fullWidth open={open} onClose={onClose} {...other}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pr: 1,
          pl: 3,
          pt: 2,
        }}
      >
        <DialogTitle sx={{ p: 0, ...titleStyle }}>{title}</DialogTitle>
        {showCloseButton && (
          <IconButton onClick={onClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        )}
      </Box>

      {content && <DialogContent sx={{ typography: 'body2', ...contentStyle }}>{content}</DialogContent>}

      <DialogActions>
        {action}

        {showCancelButton && cancelBtnText && (
          <Button variant="outlined" color="inherit" onClick={onClose}>
            {t(cancelBtnText)}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
}
