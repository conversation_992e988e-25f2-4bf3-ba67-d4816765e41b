import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';
import { LocationDetailContext } from '@/core/contexts';
import { NEW_CARD_STYLE } from '@/modules/capabilities/components/capability-detail/sections';
import { useParams } from '@/routes/hooks';
import { paths } from '@/routes/paths';
import { HIERARCHY_ENTITY_TYPE, LOCATION_STATUS_COLOR_ENUM } from '@/shared/enum';
import { SelectedLocation } from '@/shared/models';
import AddBoxIcon from '@mui/icons-material/AddBox';
import IndeterminateCheckBoxIcon from '@mui/icons-material/IndeterminateCheckBox';
import { Card, CircularProgress, IconButton, TextField, Typography } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { useTreeItem2Utils, useTreeViewApiRef } from '@mui/x-tree-view/hooks';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import { TreeItem, TreeItemProps } from '@mui/x-tree-view/TreeItem';
import { isEmpty } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { matchPath, useLocation } from 'react-router';

type Props = {
  locations: TreeViewDataType;
  selectedLocation?: SelectedLocation | undefined;
  onChange: (value: any) => void;
  isLoading?: boolean;
  error?: Error | null; // Add error prop
  searchPath?: boolean;
  isRefetched?: boolean;
  enableSearchByName?: boolean;
};

export const findPathToEntity = (locations: TreeViewDataType, idToFind: string, type?: string | null) => {
  let path: TreeViewDataType[] = [];
  function dfs(node: TreeViewDataType) {
    if (!node) return false;

    path.push(node);

    if (node.id == idToFind) {
      const findType = Object.values(HIERARCHY_ENTITY_TYPE).find((item) => item == type);
      if (type && findType && findType.length != 0 && node.entityType == type) {
        return true;
      } else if (!type || !findType) {
        return true;
      }
    }

    if (node.children) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        if (dfs(child)) return true;
      }
    }
    path.pop();
    return false;
  }
  if (dfs(locations)) {
    // If we dont have top level access we remove the first item as
    if (parseInt(path[0].id) == -1) path.splice(0, 1);
    return path;
  }

  return null;
};

export default function LocationHierarchy({
  onChange,
  isLoading = false,
  error = null,
  locations,
  searchPath = false,
  selectedLocation,
  isRefetched = false,
  enableSearchByName = false,
}: Props) {
  const apiRef = useTreeViewApiRef();
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const onSearchTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value);
    setLocationPath(event.target.value ? getAllItemsWithChildrenItemIds() : [locations.id]);
  };
  const getAllItemsWithChildrenItemIds = () => {
    const itemIds: string[] = [];
    const registerItemId = (item: TreeViewDataType) => {
      if (item.children?.length) {
        itemIds.push(item.id);
        item.children.forEach(registerItemId);
      }
    };
    filteredData?.forEach(registerItemId);
    return itemIds;
  };

  const filteredData: TreeViewDataType[] | null = searchNodeItem(searchText);
  const isError = error || (locations && locations?.id === '-1');
  const params = useParams();
  /**
   * Return all those tree nodes which matche with matchedIDS
   * @param data
   * @param matchedIDS
   * @returns
   */
  function filter(data: TreeViewDataType[], matchedIDS: string[]): TreeViewDataType[] {
    return data
      .filter((item) => matchedIDS.indexOf(item?.id) > -1)
      .map((item) => ({
        ...item,
        children: item?.children ? filter(item.children, matchedIDS) : [],
      }));
  }

  /**
   * Depth first search.
   * @param node
   * @param term
   * @param foundIds
   * @returns
   */
  function dfs(node: TreeViewDataType, term: string, foundIds: string[]) {
    // Implement your search functionality
    let isMatching = node?.fullName && node?.fullName.toLowerCase().indexOf(term.toLowerCase()) > -1;

    if (Array.isArray(node?.children)) {
      node.children.forEach((child: TreeViewDataType) => {
        const hasMatchingChild = dfs(child, term, foundIds);
        isMatching = isMatching || hasMatchingChild;
      });
    }
    // We will add any item if it matches our search term or if it has a children that matches our term
    if (isMatching && node?.id) {
      foundIds.push(node.id);
    }
    return isMatching;
  }
  /**
   * Search the tree leaf nodes that matches with searh term
   * @param term
   * @returns
   */
  function searchNodeItem(term: string): TreeViewDataType[] | null {
    // Wrap data in an object to match the node shape
    if (term == '') return null;
    const dataNode = {
      children: [locations],
    } as TreeViewDataType;

    const matchedIDS: string[] = [];
    // find all items IDs that matches our search (or their children does)
    dfs(dataNode, term, matchedIDS);

    // filter the original data so that only matching items (and their fathers if they have) are returned
    return filter([locations], matchedIDS);
  }

  const [locationPath, setLocationPath] = useState<string[]>([]);
  const [selectedItem, setSelectedItem] = useState<any>();
  const returnFirstValidLocation = () => {
    return +locations?.id == -1 && locations.children.length > 0 ? locations.children[0] : locations;
  };

  useEffect(() => {
    setSelectedItem(selectedLocation);
  }, [selectedLocation]);
  const currentLocation = useContext(LocationDetailContext);
  const currentRoute = useLocation();
  useEffect(() => {
    if (!locations) return;
    const firstValidLocation = returnFirstValidLocation();
    if (params.id && (params.id != currentLocation?.id || !isRefetched)) {
      const path = findPathToEntity(locations, params?.id ?? '');
      // We could not find the id in our tree so we just set the expanded path to first item
      if (!path) {
        onChange(null);
        setLocationPath([firstValidLocation.id.toString()]);
      } else {
        // We found the item but the user does not have global access so we remove the first item as it
        // is -1
        if (+path[0]?.id === -1) path.shift();
        let foundLocation = path[path.length - 1];
        const lastItemId = foundLocation.id.toString();
        const isSetupLocation = !!matchPath(
          { path: paths.locationSetup.root + paths.locationSetup.setupLocation, end: true },
          currentRoute.pathname,
        );
        // Case if some one has directly accessed location setup with id not as country or area which is invalid
        // So we set it as null
        if (
          isSetupLocation &&
          foundLocation.entityType != HIERARCHY_ENTITY_TYPE.COUNTRY &&
          foundLocation.entityType != HIERARCHY_ENTITY_TYPE.AREA
        ) {
          setSelectedItem(null);
        } else {
          setSelectedItem(lastItemId);
        }
        setLocationPath(path.map((item) => item.id.toString()) ?? []);
        onChange(foundLocation);
      }
      return;
    }
    // OPENING THE DEFAULT LOCATION IF PARAMS is not provided
    if ((locationPath.length === 0 || !params.id) && params['*'] !== paths.locationSetup.setupLocation) {
      const defaultLocation = firstValidLocation;
      setLocationPath([defaultLocation.id]);
      if (searchPath) {
        // If we have paramsId we dont update the view, other wise we go with the default location
        const locationToChange = params.id ? null : defaultLocation;
        onChange(locationToChange);
        setSelectedItem(defaultLocation.id);
      }
    }
  }, [params.id, locations, isRefetched]);

  useEffect(() => {
    if (isError) {
      enqueueSnackbar(t(error?.message || 'error_messages.something_went_wrong_business_units'), { variant: 'error' });
    }
  }, [isError, error, t]);
  const renderContent = () => {
    if (isLoading) {
      return (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '46vh',
          }}
        >
          <CircularProgress />
        </Box>
      );
    }

    if (isError || isEmpty(locations)) {
      // Error State
      return (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '46vh',
            textAlign: 'center',
            color: 'text.secondary',
            flexDirection: 'column',
            gap: 1,
          }}
        >
          <Typography variant="body1">{t('empty_state_messages.no_location_found')}</Typography>
        </Box>
      );
    }
    if (locations && locations.children.length > 0) {
      return (
        <Box>
          {enableSearchByName && (
            <TextField
              size="small"
              placeholder="Search"
              sx={{ mb: 2 }}
              value={searchText}
              onChange={onSearchTextChange}
              onClick={(e) => e.stopPropagation()}
              onKeyDown={(e) => e.stopPropagation()}
              fullWidth
            />
          )}

          <Box
            sx={{
              overflowX: 'auto',
              whiteSpace: 'nowrap',
            }}
          >
            <RichTreeView
              sx={{
                width: '100%',
                '& .MuiTreeItem-content:hover': {
                  width: 'auto',
                  minWidth: 'max-content',
                },
                '& .MuiTreeItem-content.Mui-selected': {
                  width: 'auto',
                  minWidth: 'max-content',
                },
              }}
              expansionTrigger="iconContainer"
              items={
                filteredData ??
                (+locations?.id == -1 ? (locations.children.length > 0 ? locations.children : []) : [locations])
              }
              getItemLabel={(item) => `${item.shortName}`}
              getItemId={(item) => item.id.toString()}
              expandedItems={locationPath ?? []}
              apiRef={apiRef}
              onItemExpansionToggle={(event, itemId, iselected) => {
                const path = findPathToEntity(locations, itemId);
                if ((event.target as HTMLElement).tagName !== 'DIV') {
                  if (path && iselected) {
                    setLocationPath([...path.map((item) => item.id.toString())]);
                  } else {
                    // as item has been deselected it needs to be removed from expanded list
                    path?.splice(
                      path.findIndex((item) => item.id == itemId),
                      1,
                    );
                    if (path) setLocationPath([...path.map((item) => item.id.toString())]);
                  }
                }
              }}
              onSelectedItemsChange={(event, itemIds) => {
                const path = findPathToEntity(locations, itemIds ?? '');
                if (
                  path &&
                  ((event.target as HTMLElement).tagName === 'P' || (event.target as HTMLElement).tagName === 'DIV')
                ) {
                  setSelectedItem(path[path.length - 1]);
                  onChange(path[path.length - 1]);
                  setSearchText('');
                }
              }}
              selectedItems={selectedItem?.id ? selectedItem?.id.toString() : (selectedItem ?? '')}
              slots={{
                item: CustomTreeItem,
                expandIcon: AddBoxIcon,
                collapseIcon: IndeterminateCheckBoxIcon,
              }}
            />
          </Box>
        </Box>
      );
    }

    return (
      <Box alignContent={'center'}>
        <Typography variant="value"> {t('empty_state_messages.no_location_access')}</Typography>
      </Box>
    );
  };

  return (
    <Card sx={NEW_CARD_STYLE}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="mainTitle">{t('geostructure')}</Typography>
      </Box>

      {renderContent()}
    </Card>
  );
}

const CustomTreeItem = React.forwardRef(function CustomTreeItem(props: TreeItemProps, ref: React.Ref<HTMLLIElement>) {
  const { publicAPI } = useTreeItem2Utils({
    itemId: props.itemID ?? '',
    children: props.children,
  });
  const handleClick = (event: React.MouseEvent) => {
    publicAPI.selectItem({ event, itemId: props.itemID ?? '' });
  };
  const itemDetails = publicAPI.getItem(props.itemId ?? '');
  return (
    <TreeItem
      onClick={handleClick}
      {...props}
      ref={ref}
      label={<CustomLabel {...props} itemDetails={itemDetails} onClick={handleClick} />}
    />
  );
});

function CustomLabel({ children, status, onClick, ...props }: any) {
  const { itemDetails, restOfProps } = props;

  return (
    <Stack direction="row" alignItems="center" flexGrow={1} {...restOfProps} onClick={onClick}>
      {itemDetails?.other_info?.status && (
        <IconButton onClick={onClick} aria-label="select item" size="small">
          <Box
            sx={{
              borderRadius: '50%',
              //@ts-ignore
              background: LOCATION_STATUS_COLOR_ENUM[props?.itemDetails?.other_info?.status],
              width: '10px',
              height: '10px',
            }}
          ></Box>
        </IconButton>
      )}
      <Box onClick={onClick}>
        <Typography>{props.label}</Typography>
      </Box>
    </Stack>
  );
}
