import { HistoryListResponse } from '@/shared/models/history.model';
import { fDateTime } from '@/shared/utils/format-time';
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  timelineItemClasses,
  TimelineSeparator,
} from '@mui/lab';
import { Typography } from '@mui/material';

const HistoryContent = ({ actionByKey, historyList }: { historyList: HistoryListResponse[]; actionByKey: string }) => {
  return (
    <Timeline
      sx={{
        [`& .${timelineItemClasses.root}:before`]: {
          flex: 0,
          padding: 0,
        },
      }}
    >
      {historyList?.map((item, index) => {
        const { comment, actionDate, actionBy } = item;
        return (
          <TimelineItem key={index}>
            <TimelineSeparator>
              <TimelineDot sx={{ mt: 1.8 }} />
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent>
              <Typography variant="value">{comment}</Typography>
              <Typography variant="body2" color="text.secondary">
                {fDateTime(actionDate)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {actionByKey} {actionBy}
              </Typography>
            </TimelineContent>
          </TimelineItem>
        );
      })}
    </Timeline>
  );
};

export default HistoryContent;
