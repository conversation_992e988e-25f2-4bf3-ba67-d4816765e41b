import { forwardRef } from 'react';

import Box, { BoxProps } from '@mui/material/Box';
import Link from '@mui/material/Link';

import { RouterLink } from '@/routes/components';
import { paths } from '@/routes/paths';
import { useMediaQuery, useTheme } from '@mui/material';

// ----------------------------------------------------------------------

export interface LogoProps extends BoxProps {
  disabledLink?: boolean;
  openNav?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const Logo = forwardRef<HTMLDivElement, LogoProps>(({ disabledLink = false, sx }, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md')); // Mobile-specific breakpoint

  // OR using local (public folder)
  // -------------------------------------------------------
  const logo = (
    <Box
      component="img"
      src={isMobile ? `assets/logo/logo.png` : `assets/logo/logo.png`}
      sx={{ width: 150, cursor: 'pointer', ...sx }}
    />
  );

  if (disabledLink) {
    return logo;
  }

  return (
    <Link component={RouterLink} href={paths.applicationRoot()} sx={{ display: 'contents' }}>
      {logo}
    </Link>
  );
});

export default Logo;
