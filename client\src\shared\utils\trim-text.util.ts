export const trimText = (text: string, maxLength = 50) =>
  text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;

export const normalizeKey = (filterKey: string, dataSample: any): string => {
  const lowerKey = filterKey.toLowerCase().replace(/\s+/g, '_');

  // Try direct match first
  if (dataSample.hasOwnProperty(lowerKey)) return lowerKey;

  // Fallback: try to find a close match
  const matchingKey = Object.keys(dataSample).find(
    (key) => key.toLowerCase().replace(/[^a-z0-9]/g, '') === lowerKey.replace(/[^a-z0-9]/g, ''),
  );

  return matchingKey || lowerKey;
};
