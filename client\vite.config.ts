import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import * as path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/SCTS/',
  server: {
    port: 4200,
    cors: true,
    open: true,
  },
  optimizeDeps: {
    exclude: ['@azure/msal-react'],
    include: ['ol/source', 'ol/proj', 'ol/Feature', 'ol/geom/Point'],
  },
  build: {
    commonjsOptions: {
      include: [/node_modules/],
    },
    rollupOptions: {
      external: [],
    },
  },
  resolve: {
    alias: {
      '@/assets': path.resolve(__dirname, 'src/assets'),
      '@/config': path.resolve(__dirname, 'src/config'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/modules': path.resolve(__dirname, 'src/modules'),
      '@/routes': path.resolve(__dirname, 'src/routes'),
      '@/shared': path.resolve(__dirname, 'src/shared'),
      '@/layouts': path.resolve(__dirname, 'src/layouts'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/theme': path.resolve(__dirname, 'src/theme'),
      '@/locales': path.resolve(__dirname, 'src/locales'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/hooks': path.resolve(__dirname, 'src/hooks'),
      '@/core': path.resolve(__dirname, 'src/core'),
    },
  },
});
