export const formatDateString = (dateStr: string | Date): string => {
    const date = new Date(dateStr);
    const formattedDate = date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
    });
    return formattedDate;
}

export const currentDateInDDMMYYYY = (): string => {
	// Get current date
	const currentDate = new Date();
	// Extract day, month, and year components
	const day = currentDate.getDate();
	const month = currentDate.getMonth() + 1; // Months are zero-based, so we add 1
	const year = currentDate.getFullYear();
	return (day < 10 ? '0' : '') + day + '/' + (month < 10 ? '0' : '') + month + '/' + year;
};
