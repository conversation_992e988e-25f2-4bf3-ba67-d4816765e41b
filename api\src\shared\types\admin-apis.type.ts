import { HIERARCHY_ENTITY_TYPE } from "../enums";

export interface BusinessEntityLevel {
	levelname?: string;
	isnodetype?: boolean;
	order_number?: number;
}

export interface BusinessEntity {
	id: number;
	code: string;
	short_name: string;
	full_name: string;
	parent_id?: number;
	entity_type: HIERARCHY_ENTITY_TYPE;
	is_node: boolean;
	active: boolean;
	country_id?: number;
	map_center?: string;
	utc_time_diff?: string;
}

export interface BusinessEntityWithChildren extends BusinessEntity {
	children?: BusinessEntityWithChildren[];
}

export interface BusinessEntityWithChildIds extends BusinessEntity {
	allchilds?: number[];
}

export interface User {
	user_name: string;
}

export interface PermissionsResponse {
	permissionName: string;
	applicationId: string;
	locations: number[];
}

export interface UserRoles {
	roleName: string;
    applicationId: number,
	locations: number[];
}
export interface RoleWithBusinessUnitId {
	user_name: string;
	business_entity_id: string;
}