import { registerDecorator, ValidationOptions, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint({ async: false })
export class IsNotEmptyStringArrayConstraint implements ValidatorConstraintInterface {
    validate(users: string[]) {
        return Array.isArray(users) && users.every(user => typeof user === 'string' && user.trim().length > 0);
    }

    defaultMessage() {
        return 'Each user must be a non-empty string';
    }
}

export function IsNotEmptyStringArray(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsNotEmptyStringArrayConstraint,
        });
    };
}
