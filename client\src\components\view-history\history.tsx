import CustomModal from '@/components/custom-modal/custom-modal';
import CircularLoader from '@/components/loading-screen/circular-loader';
import { HISTORY_TYPE } from '@/shared/enum';
import { getHistoryList } from '@/shared/services/history.service';
import { Box, Stack, Typography } from '@mui/material';
import { enqueueSnackbar } from 'notistack';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { CloseIcon } from 'yet-another-react-lightbox';
import HistoryContent from './history-content';

const PAGE_SIZE = 25;

interface HistoryProps {
  id: string | undefined;
  type: HISTORY_TYPE;
  viewHistoryOpen: boolean;
  setViewHistoryOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ViewHistory = ({ viewHistoryOpen, setViewHistoryOpen, type, id }: HistoryProps) => {
  const hasFetchedHistory = useRef(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [visibleCount, setVisibleCount] = useState(PAGE_SIZE);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { t } = useTranslation();

  const { data, isFetching } = useQuery({
    enabled: viewHistoryOpen && !hasFetchedHistory.current,
    queryKey: ['get-history-list', type, id],
    queryFn: () => getHistoryList({ id, type }),
    onSuccess: () => {
      hasFetchedHistory.current = true;
    },
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
    keepPreviousData: false,
  });

  const fullHistoryList = data ?? [];

  const loadMore = useCallback(() => {
    if (visibleCount < fullHistoryList.length) {
      setIsLoadingMore(true);
      setTimeout(() => {
        setVisibleCount((prev) => Math.min(prev + PAGE_SIZE, fullHistoryList.length));
        setIsLoadingMore(false);
      }, 500);
    }
  }, [visibleCount, fullHistoryList.length]);

  const handleScroll = () => {
    const container = scrollContainerRef.current;
    if (container) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollTop + clientHeight >= scrollHeight - 50 && !isLoadingMore) {
        loadMore();
      }
    }
  };

  useEffect(() => {
    setVisibleCount(PAGE_SIZE);
    setIsLoadingMore(false);
  }, [data, viewHistoryOpen]);

  const renderedHistory = fullHistoryList.slice(0, visibleCount);

  return (
    <CustomModal isOpen={viewHistoryOpen} handleClose={() => setViewHistoryOpen(false)}>
      <Box display={'flex'} justifyContent={'space-between'} alignItems={'center'}>
        <Typography variant="mainTitle" component="h2" style={{ verticalAlign: 'middle' }}>
          {t('label.history')}
        </Typography>
        <div
          onClick={() => {
            setViewHistoryOpen(false);
          }}
          style={{ cursor: 'pointer' }}
        >
          <CloseIcon />
        </div>
      </Box>
      {isFetching ? (
        <Box display={'flex'} justifyContent={'center'} alignItems={'center'} minHeight={200} width="100%">
          <CircularLoader />
        </Box>
      ) : fullHistoryList.length == 0 ? (
        <Stack flexGrow={1} alignItems="center" justifyContent="center">
          <Typography variant="h6" component="span" sx={{ color: 'text.disabled', textAlign: 'center' }}>
            {t('empty_state_messages.no_history_found')}
          </Typography>
        </Stack>
      ) : (
        <div ref={scrollContainerRef} onScroll={handleScroll} style={{ maxHeight: 400, overflowY: 'auto' }}>
          <HistoryContent actionByKey={t('label.by')} historyList={renderedHistory} />
          {isLoadingMore && (
            <Box display="flex" justifyContent="center" alignItems={'center'} minHeight={200} width="100%">
              <CircularLoader size={20} />
            </Box>
          )}
        </div>
      )}
    </CustomModal>
  );
};

export default ViewHistory;
