// ----------------------------------------------------------------------

import { useTranslate } from '@/locales/use-locales';
import { paths } from '@/routes/paths';
import { useMemo } from 'react';

export const HEADER = {
  H_MOBILE: 64,
  H_DESKTOP: 80,
  H_DESKTOP_OFFSET: 80 - 16,
};

export const NAV = {
  W_VERTICAL: 280,
  W_MINI: 88,
};
export function useNavData() {
  const { t } = useTranslate();

  const navItems = [
    { label: 'label.home', path: paths.home.root },
  ];

  const data = useMemo(
    () => [
      {
        items: navItems.map((item) => ({
          title: t(item.label),
          path: item.path,
          // icon: ICONS.external, // Placeholder icon, replace as needed
        })),
      },
    ],
    [t],
  );

  // const moreNavItems = [];

  // const moreNavData = useMemo(
  //   () => [
  //     {
  //       items: moreNavItems.map((item) => ({
  //         title: t(item.label),
  //         path: item.path,
  //         // icon: ICONS.external, // Placeholder icon, replace as needed
  //       })),
  //     },
  //   ],
  //   [t],
  // );

  const moreNavData: any = [];

  return { data, moreNavData };
}
