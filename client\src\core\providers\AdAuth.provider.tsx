import {
  AuthenticationResult,
  BrowserCacheLocation,
  Configuration,
  EventMessage,
  EventType,
  LogLevel,
  PublicClientApplication,
} from '@azure/msal-browser';
import { MsalProvider } from '@azure/msal-react';
import { ReactNode, useContext, useEffect, useState } from 'react';
import { AppConfigContext } from '../contexts';

const isIE = window.navigator.userAgent.indexOf('MSIE ') > -1 || window.navigator.userAgent.indexOf('Trident/') > -1;

interface Props {
  children?: ReactNode;
}

const AdAuthProvider = (props: Props) => {
  const appConfig = useContext(AppConfigContext);
  const msalConfig: Configuration = {
    auth: {
      clientId: appConfig.msDetail.clientId,
      authority: appConfig.msDetail.authority,
      // redirectUri: appConfig.msDetail.redirectUrl+'/'+applicationId,
      redirectUri: appConfig.msDetail.redirectUrl,
      postLogoutRedirectUri: '/',
      navigateToLoginRequestUrl: false,
    },
    cache: {
      cacheLocation: BrowserCacheLocation.LocalStorage,
      storeAuthStateInCookie: isIE, // set to true for IE 11.
    },
    system: {
      loggerOptions: {
        loggerCallback: (_level, _message, containsPii) => {
          if (containsPii) {
            return;
          }
        },
        logLevel: LogLevel.Info,
        piiLoggingEnabled: false,
      },
      allowNativeBroker: false,
    },
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [pca, _] = useState<PublicClientApplication>(() => new PublicClientApplication(msalConfig));

  useEffect(() => {
    pca.initialize().then(() => {
      // Default to using the first account if no account is active on page load
      if (!pca.getActiveAccount() && pca.getAllAccounts().length > 0) {
        // Account selection logic is app dependent. Adjust as needed for different use cases.
        pca.setActiveAccount(pca.getAllAccounts()[0]);
      }

      // This will update account state if a user signs in from another tab or window
      pca.enableAccountStorageEvents();

      pca.addEventCallback((event: EventMessage) => {
        if (event.eventType === EventType.LOGIN_SUCCESS && event.payload) {
          const payload = event.payload as AuthenticationResult;
          const account = payload.account;
          pca.setActiveAccount(account);
        }
      });
    });
  }, [pca]);

  return <MsalProvider instance={pca}>{props.children}</MsalProvider>;
};

export default AdAuthProvider;
