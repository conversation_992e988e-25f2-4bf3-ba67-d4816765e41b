import React, { useContext } from 'react';
import { useMsal, useAccount } from '@azure/msal-react';
import { AppConfigContext } from '../contexts';
import axios from '@/shared/utils/axios';

interface RequestInterceptorProps {
  children: JSX.Element;
}

const RequestInterceptor: React.FC<RequestInterceptorProps> = ({ children }: RequestInterceptorProps) => {
  const { instance, accounts } = useMsal();
  const appConfig = useContext(AppConfigContext);
  const account = useAccount(accounts[0]);

  /* eslint-disable no-param-reassign */
  axios.interceptors.request.use(async (config) => {
    if (!account) {
      throw Error('No active account! Verify a user has been signed in.');
    }

    const response = await instance.acquireTokenSilent({
      scopes: appConfig.msDetail.scope,
      account,
    });

    const bearer = `Bearer ${response.accessToken}`;
    config.headers.Authorization = bearer;

    return config;
  });
  /* eslint-enable no-param-reassign */
  return <>{children}</>;
};

export default RequestInterceptor;
