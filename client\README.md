# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default {
  // other rules...
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: ['./tsconfig.json', './tsconfig.node.json'],
    tsconfigRootDir: __dirname,
  },
};
```

- Replace `plugin:@typescript-eslint/recommended` to `plugin:@typescript-eslint/recommended-type-checked` or `plugin:@typescript-eslint/strict-type-checked`
- Optionally add `plugin:@typescript-eslint/stylistic-type-checked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and add `plugin:react/recommended` & `plugin:react/jsx-runtime` to the `extends` list

```
client
├─ .env
├─ .env.dev
├─ .env.prod
├─ .env.staging
├─ .eslintignore
├─ .eslintrc.cjs
├─ .prettierignore
├─ .prettierrc
├─ Dockerfile
├─ index.html
├─ nginx.conf
├─ package-lock.json
├─ package.json
├─ public
│  ├─ assets
│  │  ├─ cyan-blur.png
│  │  ├─ fonts
│  │  │  ├─ Pilat_Demi.ttf
│  │  │  ├─ Pilat_Light.ttf
│  │  │  └─ Pilat_Wide_Heavy.ttf
│  │  ├─ icons
│  │  │  ├─ app
│  │  │  │  ├─ ic_chrome.svg
│  │  │  │  ├─ ic_drive.svg
│  │  │  │  ├─ ic_dropbox.svg
│  │  │  │  ├─ ic_evernote.svg
│  │  │  │  ├─ ic_github.svg
│  │  │  │  └─ ic_onedrive.svg
│  │  │  ├─ arrowMore.svg
│  │  │  ├─ arrowPrevious.svg
│  │  │  ├─ auth
│  │  │  │  ├─ ic_amplify.svg
│  │  │  │  ├─ ic_auth0.svg
│  │  │  │  ├─ ic_firebase.svg
│  │  │  │  ├─ ic_jwt.svg
│  │  │  │  └─ ic_supabase.svg
│  │  │  ├─ brands
│  │  │  │  ├─ ic_brand_amazon.svg
│  │  │  │  ├─ ic_brand_hbo.svg
│  │  │  │  ├─ ic_brand_ibm.svg
│  │  │  │  ├─ ic_brand_lya.svg
│  │  │  │  ├─ ic_brand_netflix.svg
│  │  │  │  └─ ic_brand_spotify.svg
│  │  │  ├─ components
│  │  │  │  ├─ ic_accordion.svg
│  │  │  │  ├─ ic_alert.svg
│  │  │  │  ├─ ic_autocomplete.svg
│  │  │  │  ├─ ic_avatar.svg
│  │  │  │  ├─ ic_badge.svg
│  │  │  │  ├─ ic_breadcrumbs.svg
│  │  │  │  ├─ ic_buttons.svg
│  │  │  │  ├─ ic_checkbox.svg
│  │  │  │  ├─ ic_chip.svg
│  │  │  │  ├─ ic_colors.svg
│  │  │  │  ├─ ic_data_grid.svg
│  │  │  │  ├─ ic_dialog.svg
│  │  │  │  ├─ ic_extra_animate.svg
│  │  │  │  ├─ ic_extra_carousel.svg
│  │  │  │  ├─ ic_extra_chart.svg
│  │  │  │  ├─ ic_extra_copy_to_clipboard.svg
│  │  │  │  ├─ ic_extra_editor.svg
│  │  │  │  ├─ ic_extra_form_validation.svg
│  │  │  │  ├─ ic_extra_image.svg
│  │  │  │  ├─ ic_extra_label.svg
│  │  │  │  ├─ ic_extra_lightbox.svg
│  │  │  │  ├─ ic_extra_map.svg
│  │  │  │  ├─ ic_extra_markdown.svg
│  │  │  │  ├─ ic_extra_mega_menu.svg
│  │  │  │  ├─ ic_extra_multi_language.svg
│  │  │  │  ├─ ic_extra_navigation_bar.svg
│  │  │  │  ├─ ic_extra_organization_chart.svg
│  │  │  │  ├─ ic_extra_scroll.svg
│  │  │  │  ├─ ic_extra_scroll_progress.svg
│  │  │  │  ├─ ic_extra_snackbar.svg
│  │  │  │  ├─ ic_extra_text_max_line.svg
│  │  │  │  ├─ ic_extra_upload.svg
│  │  │  │  ├─ ic_extra_walktour.svg
│  │  │  │  ├─ ic_grid.svg
│  │  │  │  ├─ ic_icons.svg
│  │  │  │  ├─ ic_list.svg
│  │  │  │  ├─ ic_menu.svg
│  │  │  │  ├─ ic_pagination.svg
│  │  │  │  ├─ ic_pickers.svg
│  │  │  │  ├─ ic_popover.svg
│  │  │  │  ├─ ic_progress.svg
│  │  │  │  ├─ ic_radio_button.svg
│  │  │  │  ├─ ic_rating.svg
│  │  │  │  ├─ ic_shadows.svg
│  │  │  │  ├─ ic_slider.svg
│  │  │  │  ├─ ic_stepper.svg
│  │  │  │  ├─ ic_switch.svg
│  │  │  │  ├─ ic_table.svg
│  │  │  │  ├─ ic_tabs.svg
│  │  │  │  ├─ ic_textfield.svg
│  │  │  │  ├─ ic_timeline.svg
│  │  │  │  ├─ ic_tooltip.svg
│  │  │  │  ├─ ic_transfer_list.svg
│  │  │  │  ├─ ic_tree_view.svg
│  │  │  │  └─ ic_typography.svg
│  │  │  ├─ custom-icons
│  │  │  │  ├─ a3-Proj.svg
│  │  │  │  ├─ arrowBack.svg
│  │  │  │  ├─ arrowNext.svg
│  │  │  │  ├─ bell.svg
│  │  │  │  ├─ calendar.svg
│  │  │  │  ├─ cancleBtnBg.svg
│  │  │  │  ├─ Cat_More.svg
│  │  │  │  ├─ clock.svg
│  │  │  │  ├─ handShake.svg
│  │  │  │  ├─ information.svg
│  │  │  │  ├─ LEANO.png
│  │  │  │  ├─ navBack.svg
│  │  │  │  ├─ notification.svg
│  │  │  │  ├─ okBtnBG.svg
│  │  │  │  ├─ star.svg
│  │  │  │  ├─ user.svg
│  │  │  │  └─ world.svg
│  │  │  ├─ empty
│  │  │  │  ├─ ic_cart.svg
│  │  │  │  ├─ ic_content.svg
│  │  │  │  ├─ ic_email_disabled.svg
│  │  │  │  ├─ ic_email_selected.svg
│  │  │  │  ├─ ic_folder_empty.svg
│  │  │  │  └─ ic_mail.svg
│  │  │  ├─ faqs
│  │  │  │  ├─ ic_account.svg
│  │  │  │  ├─ ic_assurances.svg
│  │  │  │  ├─ ic_delivery.svg
│  │  │  │  ├─ ic_package.svg
│  │  │  │  ├─ ic_payment.svg
│  │  │  │  └─ ic_refund.svg
│  │  │  ├─ files
│  │  │  │  ├─ ic_ai.svg
│  │  │  │  ├─ ic_audio.svg
│  │  │  │  ├─ ic_document.svg
│  │  │  │  ├─ ic_excel.svg
│  │  │  │  ├─ ic_file.svg
│  │  │  │  ├─ ic_folder.svg
│  │  │  │  ├─ ic_img.svg
│  │  │  │  ├─ ic_js.svg
│  │  │  │  ├─ ic_pdf.svg
│  │  │  │  ├─ ic_power_point.svg
│  │  │  │  ├─ ic_pts.svg
│  │  │  │  ├─ ic_txt.svg
│  │  │  │  ├─ ic_video.svg
│  │  │  │  ├─ ic_word.svg
│  │  │  │  └─ ic_zip.svg
│  │  │  ├─ glass
│  │  │  │  ├─ ic_glass_bag.png
│  │  │  │  ├─ ic_glass_buy.png
│  │  │  │  ├─ ic_glass_message.png
│  │  │  │  └─ ic_glass_users.png
│  │  │  ├─ home
│  │  │  │  ├─ ic_design.svg
│  │  │  │  ├─ ic_development.svg
│  │  │  │  └─ ic_make_brand.svg
│  │  │  ├─ kaizen-cards
│  │  │  │  ├─ dustbin.png
│  │  │  │  ├─ edit.png
│  │  │  │  └─ eye.png
│  │  │  ├─ navbar
│  │  │  │  ├─ ic_analytics.svg
│  │  │  │  ├─ ic_banking.svg
│  │  │  │  ├─ ic_blank.svg
│  │  │  │  ├─ ic_blog.svg
│  │  │  │  ├─ ic_booking.svg
│  │  │  │  ├─ ic_calendar.svg
│  │  │  │  ├─ ic_chat.svg
│  │  │  │  ├─ ic_dashboard.svg
│  │  │  │  ├─ ic_disabled.svg
│  │  │  │  ├─ ic_ecommerce.svg
│  │  │  │  ├─ ic_external.svg
│  │  │  │  ├─ ic_file.svg
│  │  │  │  ├─ ic_folder.svg
│  │  │  │  ├─ ic_invoice.svg
│  │  │  │  ├─ ic_job.svg
│  │  │  │  ├─ ic_kanban.svg
│  │  │  │  ├─ ic_label.svg
│  │  │  │  ├─ ic_lock.svg
│  │  │  │  ├─ ic_mail.svg
│  │  │  │  ├─ ic_menu_item.svg
│  │  │  │  ├─ ic_order.svg
│  │  │  │  ├─ ic_product.svg
│  │  │  │  ├─ ic_tour.svg
│  │  │  │  ├─ ic_user.svg
│  │  │  │  └─ kaizen_card.svg
│  │  │  ├─ notification
│  │  │  │  ├─ ic_chat.svg
│  │  │  │  ├─ ic_delivery.svg
│  │  │  │  ├─ ic_mail.svg
│  │  │  │  └─ ic_order.svg
│  │  │  ├─ platforms
│  │  │  │  ├─ ic_figma.svg
│  │  │  │  ├─ ic_js.svg
│  │  │  │  ├─ ic_nextjs.svg
│  │  │  │  ├─ ic_ts.svg
│  │  │  │  └─ ic_vite.svg
│  │  │  ├─ pointArrow.png
│  │  │  ├─ setting
│  │  │  │  ├─ ic_align_left.svg
│  │  │  │  ├─ ic_align_right.svg
│  │  │  │  ├─ ic_collapse.svg
│  │  │  │  ├─ ic_contrast.svg
│  │  │  │  ├─ ic_contrast_bold.svg
│  │  │  │  ├─ ic_exit_full_screen.svg
│  │  │  │  ├─ ic_full_screen.svg
│  │  │  │  ├─ ic_moon.svg
│  │  │  │  ├─ ic_setting.svg
│  │  │  │  └─ ic_sun.svg
│  │  │  └─ sidebar
│  │  │     ├─ a3_projects.png
│  │  │     ├─ annoucement.png
│  │  │     ├─ arrowM.png
│  │  │     ├─ arrowMore.svg
│  │  │     ├─ auditing.png
│  │  │     ├─ Calendar-v1.png
│  │  │     ├─ Calendar-v1.svg
│  │  │     ├─ calendar.png
│  │  │     ├─ Clock.png
│  │  │     ├─ Clock.svg
│  │  │     ├─ dashboard.png
│  │  │     ├─ dropdown.png
│  │  │     ├─ edit.svg
│  │  │     ├─ export.png
│  │  │     ├─ export.svg
│  │  │     ├─ Eye.svg
│  │  │     ├─ Filter.png
│  │  │     ├─ Filter.svg
│  │  │     ├─ innovations.png
│  │  │     ├─ kaizen_card.png
│  │  │     ├─ Leano.png
│  │  │     ├─ Lsw.png
│  │  │     ├─ next.svg
│  │  │     ├─ notification.svg
│  │  │     ├─ previous.svg
│  │  │     ├─ previousIcon.svg
│  │  │     ├─ report.png
│  │  │     ├─ rewards.png
│  │  │     ├─ subBtn.svg
│  │  │     ├─ SubmitBtn_BG.png
│  │  │     ├─ submitBtn_BG.svg
│  │  │     ├─ templates.png
│  │  │     ├─ userTopIcon.svg
│  │  │     └─ user_profile.png
│  │  ├─ logo
│  │  │  ├─ logo.png
│  │  │  └─ logo_small.png
│  │  ├─ media
│  │  │  ├─ images
│  │  │  │  ├─ Arrow_back.png
│  │  │  │  ├─ Bell.png
│  │  │  │  ├─ Buildings.png
│  │  │  │  ├─ Chat.png
│  │  │  │  ├─ Checklist.png
│  │  │  │  ├─ Check_list_white.png
│  │  │  │  ├─ Chemical tank.png
│  │  │  │  ├─ Clock.png
│  │  │  │  ├─ Clock1.png
│  │  │  │  ├─ Cogs.png
│  │  │  │  ├─ Communication.png
│  │  │  │  ├─ Communication1.png
│  │  │  │  ├─ Communication_w.png
│  │  │  │  ├─ Community.png
│  │  │  │  ├─ Community_black.png
│  │  │  │  ├─ Computer.png
│  │  │  │  ├─ Contact.png
│  │  │  │  ├─ Conversation.png
│  │  │  │  ├─ Customs.png
│  │  │  │  ├─ Dashboards.png
│  │  │  │  ├─ Document.png
│  │  │  │  ├─ Door with caution.png
│  │  │  │  ├─ Education.png
│  │  │  │  ├─ Envelope.png
│  │  │  │  ├─ Eye.png
│  │  │  │  ├─ Eye1.png
│  │  │  │  ├─ Facility Management .png
│  │  │  │  ├─ Fast.png
│  │  │  │  ├─ Files.png
│  │  │  │  ├─ Foggy_weather.png
│  │  │  │  ├─ Group_of_people.png
│  │  │  │  ├─ Gyminstructor.png
│  │  │  │  ├─ Home-black.png
│  │  │  │  ├─ Home-red.png
│  │  │  │  ├─ Home-white.png
│  │  │  │  ├─ Home-white1.png
│  │  │  │  ├─ interns.png
│  │  │  │  ├─ investigation.png
│  │  │  │  ├─ Learn.png
│  │  │  │  ├─ Learning.png
│  │  │  │  ├─ Link.png
│  │  │  │  ├─ Location.png
│  │  │  │  ├─ meetings.png
│  │  │  │  ├─ Menu.png
│  │  │  │  ├─ Menu1.png
│  │  │  │  ├─ Notebook.png
│  │  │  │  ├─ person-check.png
│  │  │  │  ├─ Person.png
│  │  │  │  ├─ Person1.png
│  │  │  │  ├─ Pie_chart.png
│  │  │  │  ├─ Pie_chart1.png
│  │  │  │  ├─ Pie_chart_document.png
│  │  │  │  ├─ Play.png
│  │  │  │  ├─ Printer.png
│  │  │  │  ├─ Reply.png
│  │  │  │  ├─ Ribbon award.png
│  │  │  │  ├─ Sign-out.png
│  │  │  │  ├─ Sore throat.png
│  │  │  │  ├─ Star award.png
│  │  │  │  ├─ Suspicious person.png
│  │  │  │  ├─ Target.png
│  │  │  │  ├─ Thumbs up.png
│  │  │  │  ├─ Tick-black.png
│  │  │  │  ├─ Tick-black1.png
│  │  │  │  ├─ Tick-green.png
│  │  │  │  ├─ Tick-green1.png
│  │  │  │  ├─ Tick-green2.png
│  │  │  │  └─ user.png
│  │  │  └─ logos
│  │  │     ├─ dpw_small_logo.png
│  │  │     ├─ favicon.ico
│  │  │     ├─ logo-dark.png
│  │  │     └─ logo-light.png
│  │  ├─ placeholder.jpg
│  │  ├─ placeholder.svg
│  │  ├─ red-blur.png
│  │  └─ transparent.png
│  └─ favicon.ico
├─ README.md
├─ src
│  ├─ App.tsx
│  ├─ assets
│  │  └─ illustrations
│  │     ├─ avatar-shape.tsx
│  │     ├─ background-shape.tsx
│  │     ├─ booking-illustration.tsx
│  │     ├─ check-in-illustration.tsx
│  │     ├─ check-out-illustration.tsx
│  │     ├─ coming-soon-illustration.tsx
│  │     ├─ forbidden-illustration.tsx
│  │     ├─ index.ts
│  │     ├─ maintenance-illustration.tsx
│  │     ├─ motivation-illustration.tsx
│  │     ├─ order-complete-illustration.tsx
│  │     ├─ page-not-found-illustration.tsx
│  │     ├─ seo-illustration.tsx
│  │     ├─ sever-error-illustration.tsx
│  │     ├─ unauthorised-illustration.tsx
│  │     ├─ upgrade-storage-illustration.tsx
│  │     └─ upload-illustration.tsx
│  ├─ components
│  │  ├─ ad-user-search-input
│  │  │  ├─ ad-user-search-input-multi.tsx
│  │  │  ├─ ad-user-search-input.tsx
│  │  │  └─ index.ts
│  │  ├─ animate
│  │  │  ├─ index.ts
│  │  │  ├─ motion-container.tsx
│  │  │  ├─ motion-lazy.tsx
│  │  │  ├─ motion-viewport.tsx
│  │  │  ├─ types.ts
│  │  │  └─ variants
│  │  │     ├─ actions.ts
│  │  │     ├─ background.ts
│  │  │     ├─ bounce.ts
│  │  │     ├─ container.ts
│  │  │     ├─ fade.ts
│  │  │     ├─ flip.ts
│  │  │     ├─ index.ts
│  │  │     ├─ path.ts
│  │  │     ├─ rotate.ts
│  │  │     ├─ scale.ts
│  │  │     ├─ slide.ts
│  │  │     ├─ transition.ts
│  │  │     └─ zoom.ts
│  │  ├─ business-entity-treeview
│  │  │  ├─ business-entity-treeview-multi.tsx
│  │  │  ├─ business-entity-treeview.tsx
│  │  │  ├─ index.ts
│  │  │  └─ treeview-types.tsx
│  │  ├─ carousel
│  │  │  ├─ arrow-icons.tsx
│  │  │  ├─ carousel-arrow-index.tsx
│  │  │  ├─ carousel-arrows.tsx
│  │  │  ├─ carousel-dots.tsx
│  │  │  ├─ index.ts
│  │  │  └─ use-carousel.ts
│  │  ├─ chart
│  │  │  ├─ chart.tsx
│  │  │  ├─ index.ts
│  │  │  └─ use-chart.ts
│  │  ├─ color-utils
│  │  │  ├─ color-picker.tsx
│  │  │  ├─ color-preview.tsx
│  │  │  ├─ index.ts
│  │  │  └─ types.ts
│  │  ├─ country-select
│  │  │  ├─ country-select.tsx
│  │  │  └─ index.ts
│  │  ├─ custom-breadcrumbs
│  │  │  ├─ custom-breadcrumbs.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ link-item.tsx
│  │  │  └─ types.ts
│  │  ├─ custom-date-range-picker
│  │  │  ├─ custom-date-range-picker.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ types.ts
│  │  │  ├─ use-date-range-picker.ts
│  │  │  └─ utils.ts
│  │  ├─ custom-dialog
│  │  │  ├─ confirm-dialog.tsx
│  │  │  ├─ index.ts
│  │  │  └─ types.ts
│  │  ├─ custom-popover
│  │  │  ├─ custom-popover.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ styles.tsx
│  │  │  ├─ types.ts
│  │  │  ├─ use-popover.ts
│  │  │  └─ utils.ts
│  │  ├─ editor
│  │  │  ├─ editor.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ styles.ts
│  │  │  ├─ toolbar.tsx
│  │  │  └─ types.ts
│  │  ├─ empty-content
│  │  │  ├─ empty-content.tsx
│  │  │  └─ index.ts
│  │  ├─ error
│  │  │  ├─ 403-view.tsx
│  │  │  ├─ 500-view.tsx
│  │  │  ├─ 503-view.tsx
│  │  │  ├─ custom-message-view.tsx
│  │  │  ├─ index.ts
│  │  │  └─ not-found-view.tsx
│  │  ├─ file-thumbnail
│  │  │  ├─ download-button.tsx
│  │  │  ├─ file-thumbnail.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ types.ts
│  │  │  └─ utils.ts
│  │  ├─ hook-form
│  │  │  ├─ form-provider.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ rhf-autocomplete.tsx
│  │  │  ├─ rhf-checkbox.tsx
│  │  │  ├─ rhf-code.tsx
│  │  │  ├─ rhf-editor.tsx
│  │  │  ├─ rhf-radio-group.tsx
│  │  │  ├─ rhf-select.tsx
│  │  │  ├─ rhf-slider.tsx
│  │  │  ├─ rhf-switch.tsx
│  │  │  ├─ rhf-text-field.tsx
│  │  │  └─ rhf-upload.tsx
│  │  ├─ iconify
│  │  │  ├─ iconify.tsx
│  │  │  ├─ index.ts
│  │  │  └─ types.ts
│  │  ├─ image
│  │  │  ├─ image.tsx
│  │  │  ├─ imageIcon.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ types.ts
│  │  │  └─ utils.ts
│  │  ├─ label
│  │  │  ├─ index.ts
│  │  │  ├─ label.tsx
│  │  │  ├─ styles.ts
│  │  │  └─ types.ts
│  │  ├─ lightbox
│  │  │  ├─ index.ts
│  │  │  ├─ lightbox.tsx
│  │  │  ├─ styles.tsx
│  │  │  ├─ types.ts
│  │  │  └─ use-light-box.ts
│  │  ├─ loading-screen
│  │  │  ├─ index.ts
│  │  │  ├─ loading-card.tsx
│  │  │  ├─ loading-screen.tsx
│  │  │  └─ splash-screen.tsx
│  │  ├─ logo
│  │  │  ├─ index.ts
│  │  │  └─ logo.tsx
│  │  ├─ map
│  │  │  ├─ index.ts
│  │  │  ├─ map-control.tsx
│  │  │  ├─ map-marker.tsx
│  │  │  ├─ map-popup.tsx
│  │  │  ├─ styles.tsx
│  │  │  └─ types.ts
│  │  ├─ markdown
│  │  │  ├─ index.ts
│  │  │  ├─ markdown.tsx
│  │  │  ├─ styles.ts
│  │  │  └─ types.ts
│  │  ├─ mega-menu
│  │  │  ├─ common
│  │  │  │  ├─ menu-more-link.tsx
│  │  │  │  ├─ menu-products.tsx
│  │  │  │  ├─ menu-tags.tsx
│  │  │  │  └─ nav-sub-list.tsx
│  │  │  ├─ horizontal
│  │  │  │  ├─ mega-menu-desktop-horizontal.tsx
│  │  │  │  ├─ nav-item.tsx
│  │  │  │  └─ nav-list.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ mobile
│  │  │  │  ├─ mega-menu-mobile.tsx
│  │  │  │  ├─ nav-item.tsx
│  │  │  │  └─ nav-list.tsx
│  │  │  ├─ types.ts
│  │  │  └─ vertical
│  │  │     ├─ mega-menu-desktop-vertical.tsx
│  │  │     ├─ nav-item.tsx
│  │  │     └─ nav-list.tsx
│  │  ├─ nav-basic
│  │  │  ├─ desktop
│  │  │  │  ├─ nav-basic-desktop.tsx
│  │  │  │  ├─ nav-item.tsx
│  │  │  │  └─ nav-list.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ mobile
│  │  │  │  ├─ nav-basic-mobile.tsx
│  │  │  │  ├─ nav-item.tsx
│  │  │  │  └─ nav-list.tsx
│  │  │  └─ types.ts
│  │  ├─ nav-section
│  │  │  ├─ horizontal
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ nav-item.tsx
│  │  │  │  ├─ nav-list.tsx
│  │  │  │  └─ nav-section-horizontal.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ mini
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ nav-item.tsx
│  │  │  │  ├─ nav-list.tsx
│  │  │  │  └─ nav-section-mini.tsx
│  │  │  ├─ types.ts
│  │  │  └─ vertical
│  │  │     ├─ index.ts
│  │  │     ├─ nav-item.tsx
│  │  │     ├─ nav-list.tsx
│  │  │     └─ nav-section-vertical.tsx
│  │  ├─ organizational-chart
│  │  │  ├─ common
│  │  │  │  ├─ group-node.tsx
│  │  │  │  ├─ simple-node.tsx
│  │  │  │  └─ standard-node.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ organizational-chart.tsx
│  │  │  └─ types.ts
│  │  ├─ progress-bar
│  │  │  ├─ index.ts
│  │  │  ├─ progress-bar.tsx
│  │  │  └─ styles.tsx
│  │  ├─ scroll-progress
│  │  │  ├─ index.ts
│  │  │  └─ scroll-progress.tsx
│  │  ├─ scrollbar
│  │  │  ├─ index.ts
│  │  │  ├─ scrollbar.tsx
│  │  │  ├─ styles.ts
│  │  │  └─ types.ts
│  │  ├─ search-not-found
│  │  │  ├─ index.ts
│  │  │  └─ search-not-found.tsx
│  │  ├─ settings
│  │  │  ├─ context
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ settings-context.tsx
│  │  │  │  └─ settings-provider.tsx
│  │  │  ├─ drawer
│  │  │  │  ├─ base-option.tsx
│  │  │  │  ├─ fullscreen-option.tsx
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ layout-options.tsx
│  │  │  │  ├─ presets-options.tsx
│  │  │  │  ├─ settings-drawer.tsx
│  │  │  │  └─ stretch-options.tsx
│  │  │  ├─ index.ts
│  │  │  └─ types.ts
│  │  ├─ skeleton
│  │  │  └─ detail-page-skeleton.tsx
│  │  ├─ snackbar
│  │  │  ├─ index.ts
│  │  │  ├─ snackbar-provider.tsx
│  │  │  └─ styles.ts
│  │  ├─ svg-color
│  │  │  ├─ index.ts
│  │  │  └─ svg-color.tsx
│  │  ├─ table
│  │  │  ├─ index.ts
│  │  │  ├─ table-empty-rows.tsx
│  │  │  ├─ table-head-custom.tsx
│  │  │  ├─ table-no-data.tsx
│  │  │  ├─ table-pagination-custom.tsx
│  │  │  ├─ table-selected-action.tsx
│  │  │  ├─ table-skeleton.tsx
│  │  │  ├─ types.ts
│  │  │  ├─ use-table.ts
│  │  │  └─ utils.ts
│  │  ├─ text-max-line
│  │  │  ├─ index.ts
│  │  │  ├─ text-max-line.tsx
│  │  │  ├─ types.ts
│  │  │  └─ use-typography.ts
│  │  ├─ upload
│  │  │  ├─ errors-rejection-files.tsx
│  │  │  ├─ index.ts
│  │  │  ├─ preview-multi-file.tsx
│  │  │  ├─ preview-single-file.tsx
│  │  │  ├─ types.ts
│  │  │  ├─ upload-avatar.tsx
│  │  │  ├─ upload-box.tsx
│  │  │  └─ upload.tsx
│  │  └─ walktour
│  │     ├─ index.ts
│  │     ├─ use-walktour.tsx
│  │     ├─ walktour-progress-bar.tsx
│  │     └─ walktour.tsx
│  ├─ config
│  │  ├─ app.config.ts
│  │  └─ index.ts
│  ├─ core
│  │  ├─ contexts
│  │  │  ├─ appConfig.context.ts
│  │  │  ├─ applicationDetail.context.ts
│  │  │  ├─ index.ts
│  │  │  ├─ loading.context.ts
│  │  │  ├─ userDetail.context.ts
│  │  │  └─ userPermissions.context.ts
│  │  ├─ helper-components
│  │  │  ├─ ErrorBoundary.tsx
│  │  │  └─ index.tsx
│  │  ├─ hooks
│  │  │  ├─ index.ts
│  │  │  └─ useRedirect.hook.tsx
│  │  ├─ interceptors
│  │  │  ├─ Application.interceptor.tsx
│  │  │  ├─ index.ts
│  │  │  └─ Request.interceptor.tsx
│  │  └─ providers
│  │     ├─ AdAuth.provider.tsx
│  │     ├─ AppConfig.provider.tsx
│  │     ├─ Application.provider.tsx
│  │     ├─ index.ts
│  │     ├─ Loader.provider.tsx
│  │     └─ Permission.provider.tsx
│  ├─ features
│  │  ├─ check-user-access
│  │  │  ├─ components
│  │  │  │  ├─ header.tsx
│  │  │  │  ├─ user-access-filter.tsx
│  │  │  │  └─ user-summary.tsx
│  │  │  ├─ routes
│  │  │  │  └─ checkUserAccessRoutes.tsx
│  │  │  └─ views
│  │  │     └─ check-user-access.view.tsx
│  │  ├─ dashboard
│  │  │  ├─ components
│  │  │  │  ├─ app-widget.tsx
│  │  │  │  └─ dashboard-card-widget-summary.tsx
│  │  │  ├─ routes
│  │  │  │  └─ dashboardRoutes.tsx
│  │  │  └─ views
│  │  │     └─ dashboard.tsx
│  │  └─ role-management
│  │     ├─ components
│  │     │  ├─ role-filter.tsx
│  │     │  ├─ role-list.tsx
│  │     │  ├─ role-management-header.tsx
│  │     │  ├─ role-user-list.tsx
│  │     │  ├─ user-form.tsx
│  │     │  ├─ user-list-filter.tsx
│  │     │  ├─ user-list-header.tsx
│  │     │  └─ user-table-row.tsx
│  │     ├─ models
│  │     │  ├─ filter-and-page.model.ts
│  │     │  ├─ index.ts
│  │     │  ├─ new-user-request.ts
│  │     │  ├─ role-detail-response.ts
│  │     │  └─ user-list-response.ts
│  │     ├─ routes
│  │     │  └─ roleManagementRoutes.tsx
│  │     └─ views
│  │        ├─ role-management.view.tsx
│  │        └─ user-list.view.tsx
│  ├─ global.css
│  ├─ hooks
│  │  ├─ use-boolean.ts
│  │  ├─ use-copy-to-clipboard.ts
│  │  ├─ use-countdown.ts
│  │  ├─ use-debounce.ts
│  │  ├─ use-double-click.ts
│  │  ├─ use-event-listener.ts
│  │  ├─ use-loading.ts
│  │  ├─ use-local-storage.ts
│  │  ├─ use-mocked-user.ts
│  │  ├─ use-off-set-top.ts
│  │  ├─ use-outside-click.ts
│  │  ├─ use-responsive.ts
│  │  └─ use-scroll-to-top.ts
│  ├─ layouts
│  │  ├─ common
│  │  │  ├─ account-popover.tsx
│  │  │  ├─ clock.tsx
│  │  │  ├─ contacts-popover.tsx
│  │  │  ├─ header-shadow.tsx
│  │  │  ├─ header-simple.tsx
│  │  │  ├─ language-popover.tsx
│  │  │  ├─ login-button.tsx
│  │  │  ├─ nav-toggle-button.tsx
│  │  │  ├─ notifications-popover
│  │  │  │  ├─ index.tsx
│  │  │  │  └─ notification-item.tsx
│  │  │  ├─ searchbar
│  │  │  │  ├─ index.tsx
│  │  │  │  ├─ result-item.tsx
│  │  │  │  └─ utils.ts
│  │  │  └─ settings-button.tsx
│  │  ├─ config-layout.ts
│  │  └─ dashboard
│  │     ├─ config-navigation.tsx
│  │     ├─ header.tsx
│  │     ├─ index.tsx
│  │     ├─ main.tsx
│  │     ├─ nav-horizontal.tsx
│  │     ├─ nav-mini.tsx
│  │     └─ nav-vertical.tsx
│  ├─ locales
│  │  ├─ config-lang.ts
│  │  ├─ i18n.ts
│  │  ├─ index.ts
│  │  ├─ langs
│  │  │  ├─ ar.json
│  │  │  ├─ cn.json
│  │  │  ├─ en.json
│  │  │  ├─ fr.json
│  │  │  └─ vi.json
│  │  ├─ localization-provider.tsx
│  │  └─ use-locales.ts
│  ├─ main.tsx
│  ├─ routes
│  │  ├─ components
│  │  │  ├─ index.ts
│  │  │  └─ router-link.tsx
│  │  ├─ hooks
│  │  │  ├─ index.ts
│  │  │  ├─ use-active-link.ts
│  │  │  ├─ use-params.ts
│  │  │  ├─ use-pathname.ts
│  │  │  ├─ use-router.ts
│  │  │  └─ use-search-params.ts
│  │  ├─ paths.ts
│  │  └─ routes.tsx
│  ├─ shared
│  │  ├─ constants
│  │  │  ├─ api-paths.constant.ts
│  │  │  ├─ basic.constant.ts
│  │  │  ├─ index.ts
│  │  │  └─ path.constant.ts
│  │  ├─ data
│  │  │  ├─ countries.ts
│  │  │  └─ index.ts
│  │  ├─ enum
│  │  │  ├─ data-catalog-type.enum.ts
│  │  │  ├─ history-action-type.enum.ts
│  │  │  ├─ http-status.enum.ts
│  │  │  ├─ index.ts
│  │  │  ├─ permission.enum.ts
│  │  │  └─ user-type.enum.ts
│  │  ├─ mapping
│  │  │  ├─ data-catalog-type-key.mapping.ts
│  │  │  └─ index.ts
│  │  ├─ models
│  │  │  ├─ application-detail-response.model.ts
│  │  │  ├─ attachment-response.model.ts
│  │  │  ├─ common-response.model.ts
│  │  │  ├─ graph-user-response.model.ts
│  │  │  ├─ history-response.model.ts
│  │  │  ├─ index.ts
│  │  │  ├─ message-response.model.ts
│  │  │  ├─ user-permissions-response.model.ts
│  │  │  └─ user-summary-response.model.ts
│  │  ├─ services
│  │  │  ├─ application.service.ts
│  │  │  ├─ business-entity.service.ts
│  │  │  ├─ calendar.ts
│  │  │  ├─ http.service.ts
│  │  │  ├─ index.ts
│  │  │  ├─ reactQueryClient.service.ts
│  │  │  ├─ role-management.service.ts
│  │  │  └─ user.service.ts
│  │  ├─ types
│  │  │  ├─ accessible-role.type.ts
│  │  │  ├─ add-users-payload.ts
│  │  │  ├─ app-config.type.ts
│  │  │  ├─ application-detail.type.ts
│  │  │  ├─ calendar.ts
│  │  │  ├─ get-user-list-payload.ts
│  │  │  ├─ http-method.type.ts
│  │  │  ├─ index.ts
│  │  │  ├─ language-keys.type.ts
│  │  │  ├─ translations.type.ts
│  │  │  ├─ user-detail.type.ts
│  │  │  ├─ user-list-filter-data.type.ts
│  │  │  └─ user-summary-filter-data.type.ts
│  │  └─ utils
│  │     ├─ axios.ts
│  │     ├─ business-entity.util.ts
│  │     ├─ change-case.ts
│  │     ├─ enum-to-array.util.ts
│  │     ├─ extract-user-id.util.ts
│  │     ├─ flatten-array.ts
│  │     ├─ format-number.ts
│  │     ├─ format-time.ts
│  │     ├─ get-user-login-id.ts
│  │     ├─ highlight.ts
│  │     ├─ index.ts
│  │     ├─ lazyImport.util.ts
│  │     ├─ storage-available.ts
│  │     ├─ string-placeholder-replacer.util.ts
│  │     ├─ translated-value.util.ts
│  │     └─ uuidv4.ts
│  ├─ theme
│  │  ├─ css.ts
│  │  ├─ custom-shadows.ts
│  │  ├─ index.tsx
│  │  ├─ options
│  │  │  ├─ contrast.ts
│  │  │  ├─ presets.ts
│  │  │  └─ right-to-left.tsx
│  │  ├─ overrides
│  │  │  ├─ components
│  │  │  │  ├─ accordion.tsx
│  │  │  │  ├─ alert.tsx
│  │  │  │  ├─ appbar.tsx
│  │  │  │  ├─ autocomplete.tsx
│  │  │  │  ├─ avatar.tsx
│  │  │  │  ├─ backdrop.tsx
│  │  │  │  ├─ badge.tsx
│  │  │  │  ├─ breadcrumbs.tsx
│  │  │  │  ├─ button-group.tsx
│  │  │  │  ├─ button.tsx
│  │  │  │  ├─ card.tsx
│  │  │  │  ├─ checkbox.tsx
│  │  │  │  ├─ chip.tsx
│  │  │  │  ├─ css-baseline.tsx
│  │  │  │  ├─ data-grid.tsx
│  │  │  │  ├─ date-picker.tsx
│  │  │  │  ├─ dialog.tsx
│  │  │  │  ├─ drawer.tsx
│  │  │  │  ├─ fab.tsx
│  │  │  │  ├─ list.tsx
│  │  │  │  ├─ loading-button.tsx
│  │  │  │  ├─ menu.tsx
│  │  │  │  ├─ pagination.tsx
│  │  │  │  ├─ paper.tsx
│  │  │  │  ├─ popover.tsx
│  │  │  │  ├─ progress.tsx
│  │  │  │  ├─ radio.tsx
│  │  │  │  ├─ rating.tsx
│  │  │  │  ├─ select.tsx
│  │  │  │  ├─ skeleton.tsx
│  │  │  │  ├─ slider.tsx
│  │  │  │  ├─ stepper.tsx
│  │  │  │  ├─ svg-icon.tsx
│  │  │  │  ├─ switch.tsx
│  │  │  │  ├─ table.tsx
│  │  │  │  ├─ tabs.tsx
│  │  │  │  ├─ textfield.tsx
│  │  │  │  ├─ timeline.tsx
│  │  │  │  ├─ toggle-button.tsx
│  │  │  │  ├─ tooltip.tsx
│  │  │  │  ├─ tree-view.tsx
│  │  │  │  └─ typography.tsx
│  │  │  ├─ default-props.tsx
│  │  │  └─ index.ts
│  │  ├─ palette.ts
│  │  ├─ shadows.ts
│  │  └─ typography.ts
│  ├─ vite-env.d.ts
│  └─ _mock
│     ├─ assets.ts
│     ├─ _invoice.ts
│     ├─ _mock.ts
│     └─ _others.ts
├─ tsconfig.json
├─ tsconfig.node.json
└─ vite.config.ts

```
