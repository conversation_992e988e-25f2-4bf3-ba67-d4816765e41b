import { ChangeEvent } from 'react';
import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: { xs: '90%', sm: '60%' },
  bgcolor: 'background.paper',
  borderRadius: '10px',
  p: 4,
};
interface CustomModalProps {
  isOpen: boolean;
  handleClose: (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any) => void;
  children: any;
  sx?: any;
  innerBoxStyle?: any;
}

export default function CustomModal({ isOpen, handleClose, children, sx, innerBoxStyle }: CustomModalProps) {
  return (
    <Modal
      open={isOpen}
      sx={{
        zIndex: 1,
        alignItems: 'center',

        ...sx,
        '&:focus': {
          outline: 'none',
        },
      }}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          ...style,
          ...innerBoxStyle,
          '&:focus': {
            outline: 'none',
          },
        }}
      >
        {children}
      </Box>
    </Modal>
  );
}
