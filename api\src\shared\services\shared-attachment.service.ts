import { Injectable } from '@nestjs/common';
import { toNumber } from 'lodash';
import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { ATTACHMENT_ENTITY_TYPE, HttpStatus } from '../enums';
import { multiObjectToInstance } from '../helpers';
import { replaceUrlVariable } from '../helpers/url-creator';
import {
	Attachment,
	BulkUploadResponse,
	NewAttachmentRequest,
	UpdateAttachmentRequest,
} from '../types/attachment.type';
import { HttpException } from '../exceptions';
import { AttachmentResponseDto } from '../dtos';

@Injectable()
export class SharedAttachmentService {
	constructor(private readonly attachmentApiClient: AttachmentApiClient) { }

	private ALLOWED_FILE_TYPES = [
		'text/plain',
		'text/html',
		'text/css',
		'text/xml',
		'application/json',
		'application/xml',
		'application/pdf',
		'application/msword',
		'application/vnd.ms-powerpoint',
		'image/jpeg',
		'image/png',
		'image/gif',
		'image/heic',
		'image/svg+xml',
		'image/jpg',
		'audio/mpeg',
		'audio/wav',
		'video/mp4',
		'video/webm',
		'text/csv',
		'application/zip',
		'application/rar',
		'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		'application/vnd.openxmlformats-officedocument.presentationml.presentation',
		'application/vnd.oasis.opendocument.text',
		'application/vnd.oasis.opendocument.presentation',
		'application/vnd.apple.keynote',
		'application/rtf',
		'message/rfc822',
		'application/vnd.ms-outlook',
		'application/vnd.ms-excel',
		'application/x-zip-compressed',
		'application/zip-compressed',
		'application/vnd.rar',
		'application/x-rar-compressed',
		'application/vnd.rar',
		'application/octet-stream',
		'multipart/x-zip',
		'image/tiff',
		'image/vnd.wap.wbmp',
		'image/x-ms-bmp',
		'text/calendar',
		'text/comma-separated-values',
		'application/x-tar',
		'application/vnd.ms-excel.sheet.macroEnabled.12',
		'application/vnd.ms-excel.sheet.binary.macroEnabled.12'
	];

	/**
	 * Add Bulk Attachment
	 * @request Attachment[]
	 * @returns  BulkUploadResponse[]
	 */
	public async addBulkAttachment(
		attachments: Attachment[],
		entityId: number,
		entityType: string,
		relPath: string,
		userId: string,
		additionalId?: number,
	): Promise<BulkUploadResponse[]> {
		const newUploadPayload = attachments.map(async (attachmentData: Attachment) => {
			if (!this.ALLOWED_FILE_TYPES.includes(attachmentData.attachment_content_type)) {
				throw new HttpException('Invalid file type.', HttpStatus.BAD_REQUEST);
			}

			let base64 = attachmentData.file_base64;
			if (attachmentData.file_base64.includes('base64,')) {
				base64 = attachmentData.file_base64.split('base64,')[1];
			}

			attachmentData.file_base64 = base64;

			return {
				entity_id: entityId,
				entity_type: entityType,
				attachment_rel_path: replaceUrlVariable(relPath, { entity_id: entityId }),
				file_data: Buffer.from(attachmentData.file_base64, 'base64'),
				created_by: userId.toLowerCase(),
				attachment_name: attachmentData.attachment_name,
				attachment_content_type: attachmentData.attachment_content_type,
				attachment_content_size: attachmentData.size,
				description: attachmentData.description,
				meta_data_1: additionalId?.toString() || null,
				meta_data_2: attachmentData.fileimage,
				meta_data_3: attachmentData.size,
			} as NewAttachmentRequest;
		});

		if (attachments.length) {
			const newUploadPayloadResolve = await Promise.all(newUploadPayload);
			return this.attachmentApiClient.addBulkAttachments(newUploadPayloadResolve);
		}
	}

	validateAttachment(attachments) {
		attachments.map((attachmentData: Attachment) => {
			if (!this.ALLOWED_FILE_TYPES.includes(attachmentData.attachment_content_type)) {
				throw new HttpException('Invalid file type.', HttpStatus.BAD_REQUEST);
			}
		});

		return true;
	}

	/**
	 * Update Bulk Attachment
	 * @request Attachment[]
	 * @returns  null
	 */
	public async updateBulkAttachment(attachments: Attachment[]): Promise<null> {
		attachments.forEach(async (attachmentData: Attachment) => {
			const requestData = {
				id: toNumber(attachmentData.id),
				description: attachmentData.description,
			} as UpdateAttachmentRequest;
			await this.attachmentApiClient.updateAttachments(requestData);
		});

		return null;
	}

	/**
	 * Delete Bulk Attachment
	 * @request Attachment[]
	 * @returns  null
	 */
	public async deleteBulkAttachment(attachments: AttachmentResponseDto[]): Promise<null> {
		attachments.forEach(async (attachmentData: AttachmentResponseDto) => {
			await this.attachmentApiClient.deleteAttachmentByFileId(attachmentData.file_id);
		});
		return null;
	}

	public async deleteBulkAttachmentByFileIds(fileIds: string[]): Promise<null> {
		if (!fileIds?.length) {
			return null;
		}
		
		await Promise.all(
			fileIds.map(fileId => this.attachmentApiClient.deleteAttachmentByFileId(fileId))
		);

		return null;
	}

	/**
	 * Perform Add And Update Activity for New or updated supporitng documents
	 * @param supportingDocuments
	 * @param entityId
	 * @param entityType
	 * @param relativePath
	 * @param userId
	 * @returns
	 */
	public async supportingDocumentsActivity(
		supportingDocuments: Attachment[],
		entityId: number,
		entityType: ATTACHMENT_ENTITY_TYPE,
		relativePath: string,
		userId: string,
		additionalId?: number,
	) {
		let uploadedDocumentList = [];

		const newAttachmentRequest = supportingDocuments.filter((newAttachment: Attachment) => {
			return newAttachment.isNew;
		});
		const updateMetadataRequest = supportingDocuments.filter((updateRequest: Attachment) => {
			return !updateRequest.isNew && updateRequest.isEdited;
		});

		if (newAttachmentRequest.length) {
			await this.addBulkAttachment(
				newAttachmentRequest,
				entityId,
				entityType,
				relativePath,
				userId,
				additionalId || null,
			);
		}

		if (updateMetadataRequest.length) {
			await this.updateBulkAttachment(updateMetadataRequest);
		}

		if (newAttachmentRequest.length) {
			const uploadedDocumentData = await this.attachmentApiClient.getAllAttachments(
				entityId,
				entityType,
			);
			uploadedDocumentList = multiObjectToInstance(AttachmentResponseDto, uploadedDocumentData);
			uploadedDocumentList.forEach(uploadData => {
				uploadData.isNew = false;
				uploadData.isEdited = false;
			});
		}

		return uploadedDocumentList;
	}

	/**
	 * Perform Delete Activity for already existing supporitng documents
	 */
	public async deleteSupportingDocumentsActivity(supportingDocuments: AttachmentResponseDto[]) {
		const deleteAttachmentRequest = supportingDocuments.filter(
			(newAttachment: AttachmentResponseDto) => {
				return !newAttachment.isNew;
			},
		);
		if (deleteAttachmentRequest.length) {
			await this.deleteBulkAttachment(deleteAttachmentRequest);
		}
		return null;
	}
}
