import CustomCard from '@/components/custom-card/custom-card';
import { Divider, Skeleton, Stack } from '@mui/material';
import { Box } from '@mui/system';
import React from 'react';

interface CardSkeletonProps {
  count: number;
  spacing?: number;
}

const ContactCardSkeleton: React.FC = () => {
  return (
    <CustomCard
      id={1}
      cardStyle={{
        height: 'auto',
        width: 'auto',
        p: 0,
        alignItems: 'left',
        justifyContent: 'left',
      }}
      cardContentStyle={{ p: 0 }}
      sx={{
        position: 'relative',
      }}
    >
      <>
        <Stack spacing={0.5} sx={{ px: 2, pt: 1, minHeight: 80 }} alignItems="flex-start">
          {/* <Skeleton variant="text" width="70%" height={20} /> */}
          <Skeleton variant="text" width="70%" height={28} />
          <Skeleton variant="text" width="40%" height={20} />
          <Skeleton variant="text" width="40%" height={20} />
        </Stack>

        <Divider sx={{ width: '100%', mt: 1, borderStyle: 'dashed' }} />

        <Stack spacing={1} sx={{ px: 2, py: 1 }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Skeleton variant="circular" width={20} height={20} />
            <Skeleton variant="text" width="70%" height={20} />
            {/* <Skeleton variant="circular" width={20} height={20} /> */}
          </Stack>

          <Stack direction="row" spacing={1} alignItems="center">
            <Skeleton variant="circular" width={20} height={20} />
            <Skeleton variant="text" width="50%" height={20} />
            {/* <Skeleton variant="circular" width={20} height={20} /> */}
          </Stack>
        </Stack>
      </>
    </CustomCard>
  );
};

export default function CustomCardSkeleton({ count = 3, spacing = 3 }: CardSkeletonProps) {
  return (
    <Box
      gap={spacing}
      display="grid"
      mt={2}
      gridTemplateColumns={{
        xs: 'repeat(1, 1fr)',
        sm: 'repeat(1, 1fr)',
        md: 'repeat(2, 1fr)',
        lg: 'repeat(3, 1fr)',
      }}
    >
      {Array.from({ length: count }).map((_, index) => (
        <ContactCardSkeleton key={index} />
      ))}
    </Box>
  );
}
