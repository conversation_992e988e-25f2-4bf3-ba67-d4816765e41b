<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 267.7 223.65">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: url(#linear-gradient-7);
      }

      .cls-6 {
        fill: url(#linear-gradient-6);
      }

      .cls-7 {
        fill: url(#linear-gradient);
      }

      .cls-8 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="131.82" y1="193.86" x2="131.82" y2="135.53" gradientTransform="translate(0 227.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="64.78" y1="226.93" x2="64.78" y2="53.9" gradientTransform="translate(67.03 227.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#0f0f19"/>
      <stop offset=".68" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="214.59" y1="91.2" x2="161.54" y2="91.2" gradientTransform="translate(0 227.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".56" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset=".92" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="58.63" y1="88.39" x2="100.91" y2="88.39" gradientTransform="translate(0 227.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".61" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="-216.51" y1="-317.74" x2="-216.51" y2="-232.51" gradientTransform="translate(280.15 -40.63) scale(1 -.78)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".81" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="29.05" y1="-745.58" x2="29.05" y2="-660.35" gradientTransform="translate(170.54 -174.29) scale(1 -.52)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".81" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="11.05" y1="733.64" x2="11.05" y2="818.88" gradientTransform="translate(-1326.63 169.72) rotate(90) scale(1 -1.91)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".51" stop-color="#0f0f19"/>
      <stop offset=".81" stop-color="#0f0f19" stop-opacity=".45"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-7" d="M131.82,92.11c-16.11,0-29.17-13.06-29.17-29.17s13.06-29.17,29.17-29.17,29.17,13.06,29.17,29.17h0c0,16.11-13.06,29.17-29.17,29.17ZM131.82,42.74c-11.17-.03-20.25,9.01-20.28,20.18-.03,11.17,9.01,20.25,20.18,20.28,11.17.03,20.25-9.01,20.28-20.18,0,0,0,0,0,0-.03-11.13-9.05-20.15-20.18-20.19v-.08Z"/>
  <path class="cls-1" d="M131.82,173.82l-3.56-4.71c-.27-.35-26.79-35.51-49.02-72.8-6.13-10.27-9.64-22.41-9.64-33.31C69.16,28.65,96.66.44,131.02,0c34.36-.44,62.57,27.06,63,61.42,0,.53,0,1.06,0,1.58,0,10.9-3.51,23.04-9.64,33.31-22.23,37.28-48.75,72.42-49.02,72.8l-3.55,4.71ZM131.82,9.71c-29.44,0-53.31,23.87-53.31,53.31,0,9.34,3.02,19.81,8.38,28.74,17.18,28.82,36.97,56.38,44.92,67.19,7.94-10.8,27.73-38.37,44.92-67.19,5.32-8.93,8.37-19.4,8.37-28.74,0-29.43-23.85-53.29-53.28-53.31Z"/>
  <rect class="cls-3" x="161.54" y="132.06" width="53.05" height="8.92"/>
  <polygon class="cls-8" points="267.7 223.65 0 223.65 32.71 134.87 58.63 134.87 58.63 143.79 38.93 143.79 12.79 214.73 255.05 214.73 228.81 140.99 214.59 140.99 214.59 132.06 235.11 132.06 267.7 223.65"/>
  <rect class="cls-2" x="58.63" y="134.87" width="42.28" height="8.92"/>
  <rect class="cls-4" x="59.18" y="140.95" width="8.92" height="66.84" transform="translate(-104.66 96.07) rotate(-45)"/>
  <rect class="cls-6" x="195.13" y="169.98" width="8.92" height="44.67" transform="translate(-69.89 127.49) rotate(-30.41)"/>
  <rect class="cls-5" x="75.35" y="176.31" width="162.44" height="8.92" transform="translate(-58.77 76.75) rotate(-23.32)"/>
</svg>