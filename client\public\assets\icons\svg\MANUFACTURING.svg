<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 230.72 234.76">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-13);
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-3 {
        fill: url(#linear-gradient-10);
      }

      .cls-4 {
        fill: url(#linear-gradient-12);
      }

      .cls-5 {
        fill: url(#linear-gradient-4);
      }

      .cls-6 {
        fill: url(#linear-gradient-3);
      }

      .cls-7 {
        fill: url(#linear-gradient-5);
      }

      .cls-8 {
        fill: url(#linear-gradient-8);
      }

      .cls-9 {
        fill: url(#linear-gradient-14);
      }

      .cls-10 {
        fill: url(#linear-gradient-7);
      }

      .cls-11 {
        fill: url(#linear-gradient-9);
      }

      .cls-12 {
        fill: url(#linear-gradient-11);
      }

      .cls-13 {
        fill: url(#linear-gradient-6);
      }

      .cls-14 {
        fill: url(#linear-gradient);
      }

      .cls-15 {
        fill: #0f0f19;
      }
    </style>
    <linearGradient id="linear-gradient" x1="179.98" y1="107.57" x2="78.85" y2="6.43" gradientTransform="translate(-2.4 128.56) rotate(45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0" y1="6.43" x2="230.72" y2="6.43" gradientTransform="translate(0 236.76) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".1" stop-color="#0f0f19" stop-opacity=".55"/>
      <stop offset=".22" stop-color="#0f0f19"/>
      <stop offset=".82" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="206.64" y1="84.04" x2="129.04" y2="6.43" gradientTransform="translate(17.17 104.84) rotate(45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="141.98" y1="130.95" x2="169.71" y2="103.22" gradientTransform="translate(-37.15 92.27) rotate(45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="166.81" y1="156.31" x2="210.31" y2="112.81" gradientTransform="translate(-39.92 64.02) rotate(45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset=".89" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0" y1="69.49" x2="88.74" y2="69.49" gradientTransform="translate(0 236.76) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".16" stop-color="#0f0f19" stop-opacity=".55"/>
      <stop offset=".34" stop-color="#0f0f19"/>
      <stop offset=".67" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="48.21" y1="71.62" x2="71.06" y2="12.1" gradientTransform="translate(-12.8 202.71) rotate(28.91) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="175.49" y1="174.51" x2="165.14" y2="164.16" gradientTransform="translate(-69.85 66.74) rotate(45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".13" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="119.04" y1="118.06" x2="134.07" y2="133.09" gradientTransform="translate(-51.73 110.5) rotate(45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19"/>
      <stop offset=".88" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="74.44" y1="111.11" x2="92.86" y2="129.53" gradientTransform="translate(0 236.76) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="711.1" y1="722.66" x2="698.53" y2="710.1" gradientTransform="translate(-599.61 53.74) rotate(52.2) scale(.89 -.87) skewX(-13.98)" gradientUnits="userSpaceOnUse">
      <stop offset=".13" stop-color="#0f0f19"/>
      <stop offset=".99" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="685.95" y1="747.81" x2="673.39" y2="735.24" gradientTransform="translate(-624.75 64.15) rotate(52.2) scale(.89 -.87) skewX(-13.98)" gradientUnits="userSpaceOnUse">
      <stop offset=".13" stop-color="#0f0f19"/>
      <stop offset=".99" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="422.57" y1="459.25" x2="442.06" y2="478.73" gradientTransform="translate(-290.45 527.21) rotate(5.19) scale(.92 -.91) skewX(-10.2)" gradientUnits="userSpaceOnUse">
      <stop offset=".31" stop-color="#0f0f19"/>
      <stop offset=".95" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="153" y1="233.62" x2="196.92" y2="189.7" gradientTransform="translate(-98.42 51.05) rotate(45) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0f0f19" stop-opacity="0"/>
      <stop offset=".05" stop-color="#0f0f19" stop-opacity=".55"/>
      <stop offset=".12" stop-color="#0f0f19"/>
      <stop offset=".87" stop-color="#0f0f19"/>
      <stop offset="1" stop-color="#0f0f19" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path class="cls-15" d="M209.78,155.86l-6.27-6.27,3.67-3.67c5.2-5.2,5.2-13.62,0-18.82-5.2-5.2-13.62-5.2-18.82,0l-5.24,5.24-6.27-6.27,5.24-5.24c8.64-8.68,22.68-8.72,31.37-.07,8.68,8.64,8.72,22.68.07,31.37-.03.03-.05.05-.08.08l-3.67,3.67Z"/>
  <rect class="cls-14" x="57.9" y="175.32" width="143.02" height="8.87" transform="translate(-89.2 144.14) rotate(-45)"/>
  <rect class="cls-2" y="225.89" width="230.72" height="8.87"/>
  <rect class="cls-6" x="112.97" y="187.09" width="109.75" height="8.87" transform="translate(-86.27 174.76) rotate(-45)"/>
  <rect class="cls-5" x="151.41" y="100.07" width="8.87" height="39.21" transform="translate(-38.98 145.25) rotate(-45)"/>
  <path class="cls-15" d="M138.84,108.95l-7.7-7.7c-8.66-8.66-8.66-22.7,0-31.37,8.66-8.66,22.7-8.66,31.37,0l7.43,7.43-6.27,6.27-7.43-7.43c-5.19-5.21-13.61-5.22-18.82-.04-5.21,5.19-5.22,13.61-.04,18.82.01.01.03.03.04.04l7.7,7.7-6.27,6.27Z"/>
  <rect class="cls-7" x="184.12" y="71.44" width="8.87" height="61.52" transform="translate(-17.04 163.26) rotate(-45)"/>
  <rect class="cls-13" y="162.83" width="88.74" height="8.87"/>
  <rect class="cls-10" x="55.2" y="163.33" width="8.87" height="63.14" transform="translate(-86.8 53.12) rotate(-28.91)"/>
  <polygon class="cls-15" points="95.19 73.51 88.92 67.24 140.34 15.82 184.26 59.75 178.63 65.38 175.49 62.25 174.86 62.88 140.34 28.36 95.19 73.51"/>
  <rect class="cls-8" x="163" y="62.99" width="14.63" height="8.87" transform="translate(2.21 140.17) rotate(-45)"/>
  <polygon class="cls-15" points="115.25 128.77 71.32 84.84 88.92 67.24 95.19 73.51 83.86 84.84 115.25 116.22 115.9 115.56 122.17 121.84 115.25 128.77"/>
  <rect class="cls-11" x="115.93" y="106.75" width="21.26" height="8.87" transform="translate(-41.55 122.05) rotate(-45)"/>
  <polygon class="cls-3" points="90.15 141.35 58.73 109.94 77.15 91.52 83.42 97.79 71.28 109.94 90.15 128.81 102.29 116.66 108.56 122.94 90.15 141.35"/>
  <rect class="cls-12" x="172.78" y="41.13" width="13.78" height="8.87" transform="translate(20.4 140.38) rotate(-45)"/>
  <rect class="cls-4" x="147.63" y="15.98" width="13.78" height="8.87" transform="translate(30.82 115.24) rotate(-45)"/>
  <polygon class="cls-1" points="68.16 150.74 49.35 131.92 65.59 115.68 71.86 121.95 61.89 131.92 68.16 138.19 78.14 128.22 84.41 134.49 68.16 150.74"/>
  <rect class="cls-9" x="170.52" y="-5.96" width="8.87" height="62.12" transform="translate(33.5 131.06) rotate(-45)"/>
</svg>