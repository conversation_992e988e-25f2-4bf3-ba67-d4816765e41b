/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, ReactNode, useEffect, useState } from 'react';
import { AppConfig } from '@/config/app.config';
import { AppConfigObj } from '../../shared/types/app-config.type';
import { isEmpty, toNumber } from 'lodash';
import { AppConfigContext } from '../contexts';
import { SplashScreen } from '@/components/loading-screen';

import { API_PATHS } from '@/shared/constants';
import Page500 from '@/components/error/500-view';
import { Box, Container } from '@mui/system';
import Page503 from '@/components/error/503-view';

interface Props {
  children?: ReactNode;
}

//Fetch the app configuration based on env from API.
const AppConfigProvider: FC<Props> = ({ children }) => {
  const [config, setConfig] = useState<AppConfigObj>({} as AppConfigObj);
  const [hasError, setHasError] = useState<number | null>(null);

  useEffect(() => {
    const loadAsync = async () => {
      const configUrl = `${AppConfig.httpProtocol}://${window.location.hostname}:${AppConfig.baseUrl}${API_PATHS.APPLICATION_CONFIGURATION.WEB_CLIENT}`;
      try {
        const response = await fetch(configUrl);

        if (!response.ok) {
          setHasError(response?.status ? toNumber(response?.status) : 500);
        } else {
          const json = await response.json();
          setConfig(json);
        }
      } catch (err) {
        console.log(err);
        setHasError(500);
      }
    };
    loadAsync();
  }, []);

  if (hasError) {
    return (
      <Container maxWidth={'lg'}>
        <Box
          sx={{
            right: 0,
            width: 1,
            bottom: 0,
            height: 1,
            zIndex: 9998,
            display: 'flex',
            position: 'absolute',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
          }}
        >
          {hasError === 503 ? <Page503 /> : <Page500 />}
        </Box>
      </Container>
    );
  }

  return isEmpty(config) ? (
    <SplashScreen />
  ) : (
    <AppConfigContext.Provider value={config}>{children}</AppConfigContext.Provider>
  );
};

export default AppConfigProvider;
