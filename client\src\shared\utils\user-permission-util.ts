import { toNumber } from 'lodash';
import { PERMISSIONS } from '../enum/permission.enum';
import { UserPermissionsResponse } from '../models';

const hasPermissionForLocation = (
  requiredPermissions: PERMISSIONS[],
  userAllPermissions: UserPermissionsResponse[],
  targetId: number,
): boolean => {
  if (!targetId || !requiredPermissions.length || !userAllPermissions.length) return false;

  return requiredPermissions.some((requiredPermission) =>
    userAllPermissions.some(
      (userPermission) =>
        userPermission.permissionName === requiredPermission &&
        userPermission.locations.some((locationId) => toNumber(locationId) === toNumber(targetId)),
    ),
  );
};

export const checkAnyPermission = (
  requiredPermissions: PERMISSIONS[],
  userAllPermissions: UserPermissionsResponse[],
  entityId: number,
  locationId?: number,
) => {
  const globalPermissions = requiredPermissions.filter((p) => !p.startsWith('Local.'));
  const localPermissions = requiredPermissions.filter((p) => p.startsWith('Local.'));

  if (globalPermissions?.length) {
    if (hasPermissionForLocation(globalPermissions, userAllPermissions, entityId)) {
      return true;
    }
  }

  if (localPermissions?.length && locationId) {
    return hasPermissionForLocation(localPermissions, userAllPermissions, locationId);
  }

  return false;
};
